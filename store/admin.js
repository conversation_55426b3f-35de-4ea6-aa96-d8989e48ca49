export const state = () => ({
  post: {
    status: 'draft',
    content: 'Here comes your blog.',
    title: null,
    excerpt: null,
    slug: null,
    category: 'blog',
    thumbnail: {
      small: null,
      large: null
    }
  },
  trainingImagePaths: []
})

export const mutations = {
  SET_POST (state, payload) {
    state.post = payload
  },
  SET_POST_STATUS (state, payload) {
    state.post.status = payload
  },
  SET_POST_CONTENT (state, payload) {
    state.post.content = payload
  },
  SET_POST_TITLE (state, payload) {
    state.post.title = payload
  },
  SET_POST_SLUG (state, payload) {
    state.post.slug = payload
  },
  SET_POST_EXCERPT (state, payload) {
    state.post.excerpt = payload
  },
  SET_POST_CATEGORY (state, payload) {
    state.post.category = payload
  },
  SET_POST_THUMBNAIL (state, payload) {
    state.post.thumbnail = payload
  },
  SET_TRAINING_IMAGE_PATHS (state, payload) {
    state.trainingImagePaths = payload
  }
}

export const actions = {
  resetPost ({ commit, state }) {
    commit('SET_POST', {
      status: 'draft',
      content: 'Here comes your blog.',
      title: null,
      excerpt: null,
      slug: null,
      category: 'blog',
      thumbnail: {
        small: null,
        large: null
      }
    })
  }
}
