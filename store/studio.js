import Vue from 'vue'

export const state = () => ({
  photoTemplates: [],
  settings: {
    photoTemplateId: null,
    size: '1024x831',
    outfitId: null,
    outfitPrompt: null,
    gender: 'male',
    modelId: null,
    glasses: true,
    hairstyle: false,
    quality: 'sd',
    emotionPrompt: null,
    ethnicity: null
  },
  photos: [],
  outfits: [],
  emotions: [],
  model: {},
  popups: {
    creditModal: false
  },
  canLoadMore: true
})

export const getters = {
  pendingPhotos: state => state.photos.filter(photo => photo.status === 'pending' || photo.status === 'enhancing' || photo.status === 'saving')
}

export const mutations = {
  SET_PHOTO_TEMPLATES (state, payload) {
    state.photoTemplates = payload
  },
  ADD_PHOTO_TEMPLATE (state, payload) {
    state.photoTemplates = [...state.photoTemplates, payload]
  },
  UPDATE_SETTINGS (state, payload) {
    const { key, value } = payload
    Vue.set(state.settings, key, value)
  },
  SET_MODEL_ID (state, payload) {
    state.modelId = payload
  },
  SET_PHOTOS (state, payload) {
    state.photos = payload
  },
  UPDATE_PHOTO (state, payload) {
    const { photoId, data } = payload
    const index = state.photos.findIndex(photo => photo._id === photoId)
    if (index !== -1) {
      Vue.set(state.photos, index, data)
    }
  },
  UPDATE_PHOTO_DATA (state, payload) {
    // Only change the changed key value. Keep the rest of the data the same.
    const { photoId, data } = payload
    const index = state.photos.findIndex(photo => photo._id === photoId)
    if (index !== -1) {
      const updatedPhoto = {
        ...state.photos[index],
        ...data
      }
      Vue.set(state.photos, index, updatedPhoto)
    }
  },
  SET_OUTFITS (state, payload) {
    state.outfits = payload
  },
  SET_MODEL (state, payload) {
    state.model = payload
  },
  SET_EMOTIONS (state, payload) {
    state.emotions = payload
  },
  SET_POPUP_STATUS (state, payload) {
    const { key, value } = payload
    Vue.set(state.popups, key, value)
  },
  SET_CAN_LOAD_MORE (state, payload) {
    state.canLoadMore = payload
  }
}

export const actions = {
  async fetchPhotoTemplates ({ commit }) {
    try {
      const { data } = await this.$axios.$get('/studio/phototemplate')
      commit('SET_PHOTO_TEMPLATES', data)
    } catch (err) {
      console.log(err)
    }
  },
  async fetchPhotos ({ commit, state }) {
    try {
      const { data } = await this.$axios.$get(`/studio/photo/${state.modelId}`, { params: { offset: state.photos.length, limit: 100 } })
      commit('SET_PHOTOS', [...state.photos, ...data])
      if (data.length === 0) {
        commit('SET_CAN_LOAD_MORE', false)
      }
    } catch (err) {
      console.log(err)
    }
  },
  async createPhoto ({ state, commit, rootState }) {
    try {
      const userCredits = rootState.user.studio.credits
      const creditsToDeduct = state.settings.quality === 'sd' ? 1 : 2
      if (userCredits < creditsToDeduct) {
        commit('SET_POPUP_STATUS', { key: 'creditModal', value: true })
        throw new Error('Not enough credits')
      }
      commit('user/REMOVE_STUDIO_CREDITS', creditsToDeduct, { root: true })

      const { success, data, errorMessage } = await this.$axios.$post('/studio/photo/create', {
        settings: state.settings
      })
      if (!success) {
        if (errorMessage === 'Not enough credits') {
          commit('SET_POPUP_STATUS', { key: 'creditModal', value: true })
          throw new Error('Not enough credits')
        }
        commit('user/ADD_STUDIO_CREDITS', creditsToDeduct, { root: true })
        throw new Error('Failed to create photo')
      }
      commit('SET_PHOTOS', [data, ...state.photos])
    } catch (err) {
      if (err.message === 'Not enough credits') {
        throw new Error('Not enough credits')
      }
      console.log(err)
    }
  },
  async fetchPendingPhotos ({ commit, state }) {
    try {
      const pendingPhotos = state.photos.filter(photo => photo.status === 'pending' || photo.status === 'enhancing' || photo.status === 'saving').map(photo => photo._id)
      if (pendingPhotos.length === 0 || !pendingPhotos) { return }

      const { success, data } = await this.$axios.$post(`/studio/photo/get-by-ids/${state.modelId}`, {
        ids: pendingPhotos
      })
      if (!success) { throw new Error('Failed to fetch pending photos') }
      data.forEach((photo) => {
        commit('UPDATE_PHOTO', { photoId: photo._id, data: photo })
      })
    } catch (err) {
      console.log(err)
    }
  },
  async enhancePhoto ({ commit, state, rootState }, photoId) {
    try {
      const userCredits = rootState.user.studio.credits
      const creditsToDeduct = state.settings.quality === 'sd' ? 1 : 2
      if (userCredits < creditsToDeduct) {
        commit('SET_POPUP_STATUS', { key: 'creditModal', value: true })
        throw new Error('Not enough credits')
      }
      commit('user/REMOVE_STUDIO_CREDITS', creditsToDeduct, { root: true })
      const { success, data } = await this.$axios.$post(`/studio/photo/enhance/${photoId}`)
      if (!success) { throw new Error('Failed to enhance photo') }
      commit('UPDATE_PHOTO', { photoId, data })
    } catch (err) {
      console.log(err)
    }
  },
  async fetchOutfits ({ commit, state }) {
    try {
      const { success, data } = await this.$axios.$get('/studio/outfit/all')
      if (!success) { throw new Error('Failed to fetch outfits') }
      commit('SET_OUTFITS', data)
    } catch (err) {
      console.log(err)
    }
  },
  async fetchModel ({ commit, state }) {
    try {
      const { success, data } = await this.$axios.$get(`/studio/model/single/${state.settings.modelId}`)
      if (!success) { throw new Error('Failed to fetch model') }
      if (data) {
        commit('SET_MODEL', data)
        commit('UPDATE_SETTINGS', { key: 'gender', value: data.trigger })
        commit('UPDATE_SETTINGS', { key: 'modelId', value: data._id })
        if (data.appearance) {
          if (data.appearance.glasses) { commit('UPDATE_SETTINGS', { key: 'glasses', value: true }) }
        }
        if (data.ethnicity) {
          commit('UPDATE_SETTINGS', { key: 'ethnicity', value: data.ethnicity })
        }
      }
    } catch (err) {
      console.log(err)
    }
  },
  async fetchEmotions ({ commit, state }) {
    try {
      const { success, data } = await this.$axios.$get('/studio/emotions')
      if (!success) { throw new Error('Failed to fetch emotions') }
      commit('SET_EMOTIONS', data)
    } catch (err) {
      console.log(err)
    }
  },

  async likePhoto ({ commit, state }, photoId) {
    try {
      const { success } = await this.$axios.$post(`/studio/photo/like/${photoId}`)
      console.log('photoid', photoId)
      if (!success) { throw new Error('Failed to like photo') }
      commit('UPDATE_PHOTO_DATA', { photoId, data: { liked: true } })
    } catch (err) {
      console.log(err)
    }
  },

  async dislikePhoto ({ commit, state }, photoId) {
    try {
      const { success } = await this.$axios.$post(`/studio/photo/dislike/${photoId}`)
      if (!success) { throw new Error('Failed to dislike photo') }
      commit('UPDATE_PHOTO_DATA', { photoId, data: { liked: false } })
    } catch (err) {
      console.log(err)
    }
  },

  async deletePhoto ({ commit, state }, photoId) {
    try {
      const { success } = await this.$axios.$post(`/studio/photo/delete/${photoId}`)
      if (!success) { throw new Error('Failed to delete photo') }
      commit('SET_PHOTOS', state.photos.filter(photo => photo._id !== photoId))
    } catch (err) {
      console.log(err)
    }
  }
}
