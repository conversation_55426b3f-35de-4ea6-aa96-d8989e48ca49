export default {
  computed: {
    completionData () {
      const { organization } = this.$store.state.organization
      const hasDetails = !!organization?.name
      const hasStyles = organization?.allowedOptions?.styles?.length > 0 || organization?.style?.length > 0
      const hasClothing = organization?.allowedOptions?.clothing?.male?.length > 0 && organization?.allowedOptions?.clothing?.female?.length > 0
      const hasCombos = organization?.allowedOptions?.combos?.length >= 8
      const hasBranding = (organization?.branding?.backgroundImage || organization?.branding?.backgroundColor)
      const hasBilling = organization?.creditLog?.some(item => item.mutation > 0)
      const hasInvitedTeam = organization?.inviteCount > 0
      const hasModels = organization?.ownerModelCount > 0

      return {
        hasDetails,
        hasStyles,
        hasClothing,
        hasCombos,
        hasBranding,
        hasBilling,
        hasInvitedTeam,
        hasModels
      }
    },
    completedAllMandatorySteps () {
      return this.completionData.hasDetails &&
        (this.completionData.hasCombos || (this.completionData.hasStyles && this.completionData.hasClothing)) &&
        this.completionData.hasBilling
    }
  }
}
