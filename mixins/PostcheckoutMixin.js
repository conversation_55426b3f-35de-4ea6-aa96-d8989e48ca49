import ModelPersonalizationMixin from './ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  data () {
    return {
      fetchInterval: null
    }
  },
  beforeDestroy () {
    if (this.fetchInterval) {
      clearInterval(this.fetchInterval)
    }
  },
  mounted () {
    if (this.isLoggedIn) {
      if (!this.isTeamMember) {
        const packageId = this.$store.state?.user?.packages[0]
        if (!packageId) {
          this.$router.push('/app/add')
        }
      }
      this.handleTeamLeadLogic()
    }
  },
  computed: {
    isUploading () {
      return this.$store.state.onboarding.isUploading
    },
    minimumPhotos () {
      return this.$store.getters['onboarding/minimumPhotos']
    },
    minimumFullBodyPhotos () {
      return this.$store.getters['onboarding/minimumFullBodyPhotos']
    },
    photos () {
      return this.$store.state.onboarding.photos || []
    },
    goodPhotos () {
      return this.photos.filter(photo => photo.status === 'success' || photo.status === 'pre-success')
    },
    goodFacePhotos () {
      return this.photos.filter(photo => photo.status === 'success' && photo.fullBody === false)
    },
    goodFullBodyPhotos () {
      return this.photos.filter(photo => photo.status === 'success' && photo.fullBody === true)
    },
    pendingPhotos () {
      return this.photos.filter(photo => photo.status === 'pending')
    },
    badPhotos () {
      return this.photos.filter(photo => photo.status === 'error')
    },
    styles () {
      const styles = this.$store.state.styles
      const userOrganization = this.$store.state?.organization?.organization?._id
      let filteredStyles = styles.filter((style) => {
        if (!style?.organizationIds) { return true }
        if (style?.organizationIds?.length === 0) { return true }
        if (style?.organizationIds?.includes(userOrganization)) { return true }
        return false
      })

      // Handle filtering and duplicating styles based on organization allowed styles
      if (this.isTeamMember && this.$store.state?.organization?.organization.allowedOptions?.styles) {
        const allowedStyleIds = this.$store.state?.organization?.organization.allowedOptions?.styles
        filteredStyles = allowedStyleIds.map(id => filteredStyles.find(style => style._id === id))

        const selectedStyleIds = this.selectedStyles.map((style) => {
          return style.style
        })

        // Remove first occurance of each selected style, if there's 2 same ideas, get first 2 occurences
        selectedStyleIds.forEach((id) => {
          const index = filteredStyles.findIndex(style => style._id === id)
          if (index !== -1) {
            filteredStyles.splice(index, 1)
          }
        })
      }
      return filteredStyles
    },
    clothingItems () {
      let filteredClothing = this.$store.state.clothing

      if (this.isTeamMember && this.$store.state?.organization?.organization.allowedOptions?.clothing) {
        const allowedClothingIds = [...this.$store.state?.organization?.organization.allowedOptions?.clothing?.male, ...this.$store.state?.organization?.organization.allowedOptions?.clothing?.female]
        filteredClothing = allowedClothingIds.map(id => filteredClothing.find(clothing => clothing._id === id))
      }
      return filteredClothing
    },
    selectedStyles: {
      get () {
        return this.$store.state.onboarding.selectedStyles
      },
      set (styles) {
        this.$store.commit('onboarding/SET_ALL_SELECTED_STYLES', styles)
      }
    },
    personalInfoSummary () {
      return {
        name: this.$store.state.onboarding.title,
        age: this.$store.state.onboarding.selectedAge,
        ethnicity: this.$store.state.onboarding.ethnicity,
        eyeColor: this.$store.state.onboarding.selectedEyeColor,
        gender: this.$store.state.onboarding.selectedSex,
        bodyType: this.$store.state.onboarding.selectedBodyType,
        height: this.$store.state.onboarding.selectedHeight,
        weight: this.$store.state.onboarding.selectedWeight,
        glasses: this.$store.state.onboarding.selectedGlasses
      }
    },
    gender () {
      return this.$store.state?.onboarding?.selectedSex || null
    }
  },
  methods: {
    openRequirementsModal () {
      this.$store.commit('onboarding/SET_SHOW_REQUIREMENTS_MODAL', true)
    },
    startFetchInterval (params) {
      this.fetchInterval = setInterval(() => {
        if (!this.isUploading) {
          this.$store.dispatch('onboarding/fetchModelInformation', { ...params, silent: true })
            .catch(() => {
              this.handleError('There was an error fetching the model information. Please, check your internet connection and try again.')
            })
        }
      }, 5000)
    },

    handleTeamLeadLogic () {
      const user = this.$store.state.user
      const organization = this.$store.state.organization.organization
      // Only handle TeamLead logic if user is a TeamLead and organization exists
      if (user.role !== 'TeamLead') { return }

      // If organization does not have any styles, redirect to onboarding step page
      if (!organization?.allowedOptions || !organization?.allowedOptions?.styles?.length) {
        this.$router.push({ path: this.localePath('/app/admin/team/new?step=1') })
        return
      }

      // If organization does not have any credits, redirect them to payment onboarding page
      if (organization?.credits <= 0) {
        if (!user.hasRetry) {
          this.$router.push({ path: this.localePath('/app/admin/credits') })
        }
      }
    }
  }
}
