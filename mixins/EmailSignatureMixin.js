export default {
  methods: {
    async uploadPictureToPublicBucket (profilePicture) {
      try {
        const blob = await fetch(profilePicture).then(r => r.blob())
        const formData = new FormData()
        formData.append('files', blob)
        formData.append('folder', 'email')
        formData.append('requiresFullUrl', '1')
        formData.append('public', '1')
        const response = await this.$axios.$post('/image/upload', formData)
        if (response.success && response.data.url) {
          return response.data.url
        }

        throw new Error('Failed to upload image')
      } catch (err) {
        console.error(err)
        throw new Error('Failed to upload image')
      }
    }
  }
}
