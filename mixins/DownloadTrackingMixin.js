export default {
  methods: {
    trackDownloads (id, photoIds) {
      console.log('trackDownloads', id, photoIds)
      return this.$axios.$post('/model/download/track/' + id, { photoIds })
        .then((response) => {
          if (response.success && response.totalDownloads >= 3) {
            if (this.$store.state.showReviewRequestModal) { return }
            if (this.isTeamMember) { return }
            if (!this.$cookies.get('hsp:seenreviewmodal')) {
              this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', true)
            }
          }
        })
    }
  }
}
