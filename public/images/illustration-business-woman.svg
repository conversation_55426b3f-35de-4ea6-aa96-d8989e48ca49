<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="865.7639" height="682.89244" viewBox="0 0 865.7639 682.89244" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M689.91048,568.97041l28.85759-33.02349.301.263,5.30779-6.07371-.30114-.263,8.86008-10.1394a3.82947,3.82947,0,0,0-.3637-5.40353l-21.4593-18.75229a3.82955,3.82955,0,0,0-5.4036.36359l-6.17784,7.06968-.48895-.40823-3.61547,4.33043.39144.32679-.81881.937-.41655-.34777L690.96759,512.18l.319.26636-28.60273,32.73183a3.82256,3.82256,0,0,0,.36365,5.40371l21.45924,18.75218A3.83417,3.83417,0,0,0,689.91048,568.97041Z" transform="translate(-167.11805 -108.55378)" fill="#3f3d56"/><path d="M663.55075,548.68814a1.83882,1.83882,0,0,1,.22341-2.099l43.47948-49.7561a1.83515,1.83515,0,0,1,2.58922-.17427l4.56586,3.98989-.17861.08958a2.11515,2.11515,0,0,0-.44282,3.48293l10.133,8.85478a2.14218,2.14218,0,0,0,1.90916.31771,2.0976,2.0976,0,0,0,1.36168-1.32927l.06492-.189,4.71637,4.12127a1.83755,1.83755,0,0,1,.17421,2.58939l-41.483,47.47164a4.87489,4.87489,0,0,1-6.87047.46225l-19.8448-17.34137A1.83764,1.83764,0,0,1,663.55075,548.68814Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><circle cx="540.25398" cy="413.26356" r="7.25309" fill="#E56C51"/><path d="M701.88942,549.65126,681.7908,532.088a.63444.63444,0,1,1,.83494-.95547l20.09862,17.56322a.63444.63444,0,1,1-.83494.95547Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M693.26106,545.91475l-6.611-5.777a.63447.63447,0,1,1,.835-.95551l6.611,5.777a.63447.63447,0,0,1-.835.95552Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M689.43841,550.28923l-6.611-5.777a.63447.63447,0,1,1,.835-.95551l6.611,5.777a.63447.63447,0,0,1-.835.95552Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M687.4805,552.52979l-6.611-5.777a.63444.63444,0,1,1,.83494-.95547l6.611,5.777a.63444.63444,0,1,1-.83494.95548Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M695.08422,551.31124,681.0579,539.05432a.63444.63444,0,1,1,.83494-.95547l14.02632,12.25691a.63444.63444,0,1,1-.83494.95548Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M681.10259,555.04692l-2.40043-2.09762a1.07885,1.07885,0,0,1,1.41981-1.62476l2.40042,2.09761a1.07885,1.07885,0,0,1-1.4198,1.62477Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M650.78266,515.80095a11.74519,11.74519,0,0,1,1.54218-17.94369l-19.1722-92.788,20.6846,6.52544,15.42513,89.7651a11.80884,11.80884,0,0,1-18.47971,14.44119Z" transform="translate(-167.11805 -108.55378)" fill="#9e616a"/><path d="M606.46716,267.77465l12.81306-9.672s11.30314-.80044,13.305,19.5834,14.53611,98.61661,14.53611,98.61661l19.60653,90.81-27.14078,5.95727-21-98-21.02158-67.61024Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><path d="M609.80444,435.00183,567.986,381.21966a4.58315,4.58315,0,0,1,.80395-6.42411l61.46234-47.79013a4.58315,4.58315,0,0,1,6.42411.80391l41.81841,53.78217a4.58316,4.58316,0,0,1-.80394,6.42413l-61.46234,47.79014A4.58315,4.58315,0,0,1,609.80444,435.00183Z" transform="translate(-167.11805 -108.55378)" fill="#3f3d56"/><path d="M610.86247,430.14665l-37.91027-48.756a4.06936,4.06936,0,0,1,.71381-5.70391l56.24847-43.73609a4.06935,4.06935,0,0,1,5.70391.71381l37.91027,48.75595a4.06935,4.06935,0,0,1-.71381,5.70392l-56.24847,43.73608A4.06934,4.06934,0,0,1,610.86247,430.14665Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M664.01479,385.7057l-19.12463-24.59595a1.01611,1.01611,0,1,1,1.60431-1.24744l19.12463,24.596a1.01611,1.01611,0,1,1-1.60431,1.24743Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M660.84507,388.17032l-19.12463-24.596a1.01616,1.01616,0,1,1,1.60439-1.24749l19.12463,24.596a1.01616,1.01616,0,0,1-1.60439,1.24749Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M646.84079,376.70074,638.55073,366.039a1.01616,1.01616,0,1,1,1.60439-1.24749l8.29006,10.66175a1.01616,1.01616,0,0,1-1.60439,1.2475Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M642.56709,397.6232l-30.19183-38.82937a1.01611,1.01611,0,1,1,1.60431-1.24743l30.19183,38.82937a1.01611,1.01611,0,1,1-1.60431,1.24743Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M639.39738,400.08782l-30.19184-38.82937a1.01616,1.01616,0,0,1,1.60439-1.2475l30.19184,38.82937a1.01616,1.01616,0,1,1-1.60439,1.2475Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M632.20225,405.6824,602.01042,366.853a1.01611,1.01611,0,1,1,1.60431-1.24743L633.80656,404.435a1.01611,1.01611,0,1,1-1.60431,1.24744Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M629.03254,408.147,598.8407,369.31765a1.01616,1.01616,0,0,1,1.60439-1.2475l30.19184,38.82937a1.01616,1.01616,0,0,1-1.60439,1.2475Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M621.83741,413.7416l-30.19183-38.82937a1.01611,1.01611,0,1,1,1.60431-1.24744l30.19183,38.82937a1.01611,1.01611,0,1,1-1.60431,1.24744Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><path d="M618.6677,416.20621l-30.19184-38.82936a1.01616,1.01616,0,1,1,1.60439-1.2475l30.19184,38.82937a1.01616,1.01616,0,0,1-1.60439,1.24749Z" transform="translate(-167.11805 -108.55378)" fill="#e4e4e4"/><circle cx="503.43609" cy="352.50801" r="0.41718" fill="#fff"/><path d="M700.882,570.77522h-3v-27a36.5,36.5,0,0,0-73,0v27h-3v-27a39.5,39.5,0,0,1,79,0Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><path d="M745.532,546.29518a19.41047,19.41047,0,0,0-18.99-15.02H590.522a19.58216,19.58216,0,0,0-19.29,16.57,224.54985,224.54985,0,0,0-2.01,42.93q.345,8.28,1.23,17,.57,5.70008,1.36,11.6a19.57249,19.57249,0,0,0,19.34,16.9h135.13a19.38984,19.38984,0,0,0,19.03-15.23q1.5-6.66009,2.48-13.27a170.72812,170.72812,0,0,0,1.7-17A159.654,159.654,0,0,0,745.532,546.29518Z" transform="translate(-167.11805 -108.55378)" fill="#E56C51"/><path d="M700.882,562.27522h-3v-27a36.5,36.5,0,0,0-73,0v27h-3v-27a39.5,39.5,0,0,1,79,0Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><path d="M663.91308,538.85536l-5-8.66027a4.88352,4.88352,0,0,0-9.03113,1.08013v10a3.5,3.5,0,0,0,7,0v-.59808l.96894,1.67822a3.5,3.5,0,0,0,6.06219-3.5Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><circle cx="457.2639" cy="453.72145" r="6" fill="#2f2e41"/><circle cx="531.7639" cy="454.22145" r="6" fill="#2f2e41"/><path d="M749.49194,590.77522a170.72812,170.72812,0,0,1-1.7,17H570.452q-.885-8.73009-1.23-17Z" transform="translate(-167.11805 -108.55378)" fill="#3f3d56"/><path d="M232.89614,682.66343c3.4179-27.4838,20.45086-54.56332,46.66209-63.50729a127.60381,127.60381,0,0,0,.00632,87.60863c4.02766,10.89467,9.64177,22.59279,5.85323,33.573-2.35719,6.83217-8.12373,12.05938-14.56956,15.32782-6.44622,3.26843-13.60019,4.82623-20.66462,6.35286l-1.39032,1.14988C237.5786,737.845,229.47825,710.14722,232.89614,682.66343Z" transform="translate(-167.11805 -108.55378)" fill="#e6e6e6"/><path d="M279.81833,619.67581a109.06693,109.06693,0,0,0-27.11387,61.38283,46.96807,46.96807,0,0,0,.53426,14.70626,26.9383,26.9383,0,0,0,6.69987,12.4945c3.01978,3.31784,6.4929,6.36192,8.65358,10.356a16.49435,16.49435,0,0,1,.80583,13.46535c-1.90766,5.47149-5.66754,9.93122-9.496,14.16468-4.25072,4.70045-8.74036,9.51541-10.54723,15.747-.21893.755-1.3777.37119-1.1591-.38271,3.14366-10.84185,13.66822-17.00022,18.68736-26.76533,2.342-4.55658,3.325-9.84656,1.12944-14.65649-1.92-4.20608-5.49879-7.34823-8.58542-10.68118a28.74515,28.74515,0,0,1-7.00622-11.97285,43.42046,43.42046,0,0,1-1.09769-14.63146,105.81419,105.81419,0,0,1,7.72806-32.15611,111.00121,111.00121,0,0,1,19.95939-31.986c.52191-.58442,1.32619.33488.80769.91548Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M253.033,673.67174a16.36351,16.36351,0,0,1-12.45586-17.14139c.06219-.7829,1.28171-.723,1.21943.06091a15.15226,15.15226,0,0,0,11.61914,15.92138c.76415.18168.37707,1.33973-.38271,1.1591Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M258.18588,706.77878a31.53933,31.53933,0,0,0,14.08421-18.16434c.22159-.75424,1.38042-.37062,1.15909.38271A32.80566,32.80566,0,0,1,258.73487,707.869c-.67686.4015-1.22218-.6909-.549-1.09023Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M264.96419,640.18049a9.26254,9.26254,0,0,0,8.77776-.44558c.67143-.40989,1.21605.683.549,1.09022a10.37942,10.37942,0,0,1-9.70945.51446.63083.63083,0,0,1-.3882-.7709.61342.61342,0,0,1,.77091-.3882Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M359.45374,668.24554c-.412.26786-.824.53572-1.23644.81389a121.98241,121.98241,0,0,0-15.59686,12.1769c-.38129.34-.76258.69028-1.13332,1.04052a128.59689,128.59689,0,0,0-27.92856,38.23074,124.88246,124.88246,0,0,0-6.84066,17.69882c-2.52419,8.37552-4.59465,17.65757-9.59122,24.50842a21.42471,21.42471,0,0,1-1.669,2.06039H250.30392c-.10262-.05149-.20574-.09274-.30886-.14424l-1.80285.08244c.07244-.31936.15443-.649.22687-.96839.04125-.18543.09256-.37086.1338-.55629.03069-.12362.06188-.2473.0825-.36061.01006-.04118.02062-.08237.03069-.1133.02062-.11331.05181-.21637.07243-.31936q.67985-2.76606,1.40093-5.53217c0-.01031,0-.01031.01006-.02062,3.69876-14.04162,8.60227-27.89782,15.453-40.56926.20624-.38117.412-.77265.63885-1.15382a119.16727,119.16727,0,0,1,10.70391-16.236,105.34914,105.34914,0,0,1,7.01572-8.02523A87.605,87.605,0,0,1,305.88338,674.437c16.195-8.55064,34.94428-11.82666,52.25193-6.60355C358.578,667.96742,359.01108,668.10129,359.45374,668.24554Z" transform="translate(-167.11805 -108.55378)" fill="#e6e6e6"/><path d="M359.35223,668.81863A109.06692,109.06692,0,0,0,300.74662,701.505a46.968,46.968,0,0,0-8.42761,12.06378,26.93831,26.93831,0,0,0-2.17308,14.01c.41356,4.46722,1.35391,8.98881.67435,13.47877a16.49433,16.49433,0,0,1-7.46366,11.23649c-4.81737,3.22014-10.50449,4.51727-16.1101,5.59249-6.224,1.19382-12.70762,2.33522-17.90214,6.22291-.62939.471-1.32349-.5331-.69505-1.00343,9.03757-6.76391,21.14861-5.34452,31.03538-10.11954,4.61335-2.22811,8.58318-5.86,9.726-11.02238.99936-4.51427.03366-9.1778-.42417-13.69734a28.74505,28.74505,0,0,1,1.61441-13.77788,43.42045,43.42045,0,0,1,7.9327-12.34329,105.81414,105.81414,0,0,1,25.53062-21.022,111.00085,111.00085,0,0,1,35.19423-13.52208c.76857-.15241.85726,1.06583.09371,1.21724Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M305.45639,695.80475a16.36352,16.36352,0,0,1,.375-21.18573c.521-.58766,1.45869.19437.937.78281a15.15226,15.15226,0,0,0-.30854,19.70786c.50075.60513-.50554,1.29672-1.00342.69506Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M289.63792,725.34125a31.53942,31.53942,0,0,0,22.18165-6.02355c.631-.4688,1.32533.53519.695,1.00343a32.80564,32.80564,0,0,1-23.09475,6.22113c-.78217-.08694-.55987-1.28748.21805-1.201Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M335.14679,676.24725a9.26256,9.26256,0,0,0,7.27683,4.929c.78288.077.55973,1.27749-.21806,1.201a10.37943,10.37943,0,0,1-8.0622-5.435.63084.63084,0,0,1,.15419-.84924.6134.6134,0,0,1,.84924.15419Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M707.585,696.33058c-22.8359-24.79405-34.58622-61.91457-22.27385-93.293a155.30529,155.30529,0,0,0,81.36824,68.91044c13.28632,4.82963,28.5668,8.81791,35.784,20.97381,4.49087,7.5636,4.80926,17.031,2.77417,25.58838-2.03541,8.55777-6.21625,16.42717-10.3556,24.18891l-.02578,2.19574C762.51618,735.38983,730.42088,721.12463,707.585,696.33058Z" transform="translate(-167.11805 -108.55378)" fill="#e6e6e6"/><path d="M685.99836,603.20487A132.7443,132.7443,0,0,0,721.67658,676.672a57.16444,57.16444,0,0,0,14.07816,11.07233,32.78626,32.78626,0,0,0,16.87418,3.60639c5.45679-.19457,11.016-1.02551,16.425.10977a20.07511,20.07511,0,0,1,13.13934,9.844c3.5808,6.07574,4.76495,13.07578,5.68505,19.96148,1.02159,7.64526,1.96158,15.60247,6.32755,22.18253.529.79726-.739,1.57147-1.26722.77541-7.596-11.44816-5.03638-26.06688-10.15711-38.40982-2.38943-5.75945-6.529-10.8337-12.72321-12.57829-5.41656-1.52557-11.15-.6736-16.67337-.42884a34.98538,34.98538,0,0,1-16.6307-2.91153,52.84685,52.84685,0,0,1-14.45191-10.49022,128.78566,128.78566,0,0,1-23.78461-32.47238,135.09837,135.09837,0,0,1-14.005-43.69794c-.13221-.94443,1.35423-.96822,1.48557-.03Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M715.07472,670.556a19.91585,19.91585,0,0,1-25.71768-1.91615c-.67817-.67362.33674-1.75911,1.01582-1.08459a18.44165,18.44165,0,0,0,23.92645,1.73352c.76983-.56676,1.54084.70371.77541,1.26722Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M749.8751,691.81374a38.3862,38.3862,0,0,0-5.79029-27.369c-.52615-.79911.7417-1.57356,1.26722-.77541a39.92746,39.92746,0,0,1,5.96743,28.49213c-.15957.94445-1.60306.59156-1.44436-.34776Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M693.35649,633.12987a11.27333,11.27333,0,0,0,6.4911-8.50253c.14751-.946,1.59091-.59209,1.44435.34776a12.63269,12.63269,0,0,1-7.16,9.422.76779.76779,0,0,1-1.02132-.2459.74657.74657,0,0,1,.24591-1.02132Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M793.75,567.45325c-.07531.59332-.15062,1.18664-.21675,1.78854a148.46376,148.46376,0,0,0-.96024,24.06381c.01581.62156.04119,1.25123.07482,1.871A156.514,156.514,0,0,0,806.18349,651.188a151.99366,151.99366,0,0,0,11.056,20.27558c5.79283,8.93276,12.78448,18.15723,15.21647,28.18675a26.07521,26.07521,0,0,1,.60058,3.17085L797.53689,744.756c-.12855.05479-.248.11811-.37692.17337l-1.34163,1.73917c-.23962-.31849-.48129-.654-.7209-.97246-.13976-.18417-.27161-.37769-.41137-.56186-.09067-.12574-.181-.252-.27-.36029-.03033-.04174-.06027-.08395-.08109-.11762-.089-.10829-.16018-.21832-.23961-.3185q-2.03408-2.80725-4.03577-5.65287c-.00958-.00811-.00958-.00811-.01124-.02557-10.13105-14.48076-19.14219-29.93452-25.52127-46.26472-.19176-.49138-.3935-.9904-.569-1.50094a145.03647,145.03647,0,0,1-6.65843-22.71268,128.21835,128.21835,0,0,1-1.9343-12.82854,106.62272,106.62272,0,0,1,1.99427-33.27732c4.79848-21.76673,16.50493-41.75651,34.97057-53.72165C792.80275,568.04781,793.26778,567.75089,793.75,567.45325Z" transform="translate(-167.11805 -108.55378)" fill="#e6e6e6"/><path d="M794.20234,567.99834a132.74422,132.74422,0,0,0-15.7452,80.1401,57.164,57.164,0,0,0,4.57431,17.31666A32.78627,32.78627,0,0,0,794.33324,678.494c4.47409,3.13,9.41306,5.81356,13.04837,9.97664a20.07512,20.07512,0,0,1,4.56428,15.77065c-.79895,7.007-4.068,13.30912-7.479,19.36093-3.78728,6.71937-7.82755,13.6387-8.30321,21.52112-.05763.95506-1.5362.80979-1.47866-.14383.82758-13.71406,11.6728-23.84519,15.01549-36.78337,1.55976-6.0372,1.30958-12.581-2.58578-17.70331-3.40632-4.47922-8.49706-7.25088-13.05456-10.38093a34.98535,34.98535,0,0,1-11.52574-12.33752,52.84687,52.84687,0,0,1-5.2232-17.07691,128.78532,128.78532,0,0,1,.55994-40.24734,135.09809,135.09809,0,0,1,15.127-43.32232c.46305-.83367,1.6642.04227,1.20418.8705Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M776.86816,639.28041a19.91586,19.91586,0,0,1-19.38048-17.01376c-.13591-.94615,1.328-1.2018,1.46407-.25439a18.44166,18.44166,0,0,0,18.06024,15.7895c.9559.011.8066,1.48956-.14383,1.47865Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M791.85568,677.20577A38.38622,38.38622,0,0,0,803.71046,651.867c.061-.95482,1.5396-.80984,1.47866.14383A39.92748,39.92748,0,0,1,792.79955,678.353c-.696.658-1.63612-.49283-.94387-1.14727Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M782.06047,596.32187a11.27332,11.27332,0,0,0,10.30189-2.8807c.68735-.66652,1.62674.48509.94387,1.14727a12.63272,12.63272,0,0,1-11.38959,3.21209.76778.76778,0,0,1-.66741-.81124.74659.74659,0,0,1,.81124-.66742Z" transform="translate(-167.11805 -108.55378)" fill="#fff"/><path d="M229.61244,748.0761c-4.144,0-8.29652.1709-12.35177.51563l-.59019.0498c-26.89858,2.37793-46.57364,16.17286-45.76445,32.08985l.15643,2.87793c.10315,1.81933.21307,3.77539.30355,5.94531.049,1.07031,1.5093,1.8916,3.32638,1.8916H836.3322c46.6489-.2832,93.12868-.63965,138.1406-1.05957a235.0853,235.0853,0,0,0,25.27343-1.30957c12.50905-1.48047,21.19113-4.415,26.54515-8.97168h.00169c6.65784-5.65039,6.86754-12.76562,6.46506-20.80469-.82018-16.23242-1.6336-33.28515-2.44533-50.28613-.717-14.9834-1.43066-29.92676-2.14431-44.2334-.44137-8.49023-1.42221-15.98437-8.9831-21.71191-8.19505-6.19434-23.141-9.334-44.42341-9.334-.25705,0-.51241,0-.77284.001-66.69684.2373-132.7832,23.1709-164.4456,57.06738-3.646,3.90332-7.04342,8.082-10.32922,12.124-4.85513,5.9707-9.87431,12.14355-15.86417,17.66406a74.55881,74.55881,0,0,1-10.47973,8.16894c-.257.17774-.5513.36817-.86246.54883-.712.46387-1.456.91114-2.18151,1.30957a75.76767,75.76767,0,0,1-13.36474,6.07617l-.46843.15723c-.35259.126-.73478.25195-1.117.36719-2.68039.86035-5.46478,1.60449-8.25424,2.20605-12.06936,2.66016-25.79345,3.29785-40.78925,1.89356a197.11106,197.11106,0,0,1-40.55672-8.26465c-13.88644-4.38867-26.88421-9.86035-39.45413-15.15137-5.52143-2.32519-11.23226-4.72851-16.93548-7.00586-1.07216-.42871-2.12656-.84765-3.197-1.2666-6.96563-2.71484-13.281-4.957-19.3064-6.85449a190.55334,190.55334,0,0,0-46.008-8.52149c-16.588-.9082-31.20581.80469-43.43751,5.07911a74.59932,74.59932,0,0,0-9.82527,4.23339c-14.48762,7.52344-23.41915,18.627-32.05558,29.36524-10.74353,13.35644-21.85319,27.16894-43.7901,34.3291-25.31317,8.26953-62.26194,4.834-88.40291-1.14062-8.21873-1.87891-16.42731-4.04-24.36616-6.13086q-4.80948-1.2671-9.62572-2.51661c-6.30948-1.6289-11.47325-2.87695-16.2506-3.93066l-1.511-.33594c-.84048-.18261-1.6818-.36523-2.53834-.53906-5.67447-1.19141-10.9583-2.12891-16.1618-2.86621L251.179,749.622c-2.46816-.32519-4.77566-.5957-7.04-.82324l-.90389-.08984C238.80017,748.287,234.21053,748.0761,229.61244,748.0761Z" transform="translate(-167.11805 -108.55378)" fill="#f2f2f2"/><path d="M404.393,336.772c-2.089-1.47454-4.37445-3.01218-6.93063-2.95071-6.18682.14877-9.52244,9.21835-15.70289,8.8994-3.94144-.20341-6.55789-4.20736-10.2278-5.65917-4.77785-1.89-10.858,1.80641-11.37928,6.91794l.6136-.5616a148.14611,148.14611,0,0,0,56.75648,2.622Q410.95782,341.4059,404.393,336.772Z" transform="translate(-167.11805 -108.55378)" fill="#f2f2f2"/><path d="M387.393,473.772c-2.089-1.47454-4.37445-3.01218-6.93063-2.95071-6.18682.14877-9.52244,9.21835-15.70289,8.8994-3.94144-.20341-6.55789-4.20736-10.2278-5.65917-4.77785-1.89-10.858,1.80641-11.37928,6.91794l.6136-.5616a148.14611,148.14611,0,0,0,56.75648,2.622Q393.95782,478.4059,387.393,473.772Z" transform="translate(-167.11805 -108.55378)" fill="#f2f2f2"/><path d="M807.90634,215.56549c-5.24659-7.14464-10.98674-14.595-17.40678-14.29714-15.53862.72083-23.91628,44.66588-39.4389,43.12048-9.8992-.9856-16.47062-20.386-25.68786-27.42053-11.99991-9.15777-27.27072,8.75264-28.57985,33.51964l1.54109-2.72111c46.49618,21.92466,95.20209,26.26554,142.54791,12.70452Q824.3943,238.01842,807.90634,215.56549Z" transform="translate(-167.11805 -108.55378)" fill="#f2f2f2"/><circle cx="144.7639" cy="130.22145" r="45" fill="#f2f2f2"/><circle cx="371.7639" cy="43.22145" r="15" fill="#2f2e41"/><circle cx="395.7639" cy="77.22145" r="38" fill="#2f2e41"/><polygon points="376.201 650.637 394.082 650.637 402.592 581.669 376.201 581.669 376.201 650.637" fill="#9e616a"/><path d="M537.484,746.94444l28.56664-1.70505v12.23919l27.1591,18.75705a7.64506,7.64506,0,0,1-4.34418,13.93624H554.85588l-5.86208-12.10643L546.705,790.17187H533.882Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><polygon points="269.971 628.072 284.32 638.742 332.303 588.475 311.126 572.728 269.971 628.072" fill="#9e616a"/><path d="M439.71447,723.316l23.94081,15.67793-7.30331,9.82137,10.60128,31.25791a7.64506,7.64506,0,0,1-11.802,8.59094l-27.29115-20.29408,2.52005-13.21284-9.06079,8.349-10.28985-7.65167Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><rect x="366.7639" y="269.22145" width="68" height="64" fill="#9e616a"/><path d="M522.50188,403.35s-33.92617,29.31063-18.27305,112.86791l19.11,81.87225L461.01587,701.483,480.88758,725.658l81.02127-111.911,29.36-68.8691,15.53-120.69995Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><polygon points="407.969 455.95 410.009 487.552 403.764 622.221 374.379 617.104 368.764 521.221 407.969 455.95" fill="#2f2e41"/><polygon points="417.086 125.739 384.276 127.019 371.199 148.725 341.366 154.549 359.176 244.649 341.586 310.039 404.791 337.699 418.856 295.549 437.548 346.649 467.559 314.016 442.858 251.023 455.636 201.329 452.764 149.221 428.764 146.221 417.086 125.739" fill="#2f2e41"/><circle cx="398.90833" cy="84.33274" r="30.87929" fill="#9e616a"/><path d="M586.99658,161.40936a35.37124,35.37124,0,0,0-53.7768,30.86976c15.73459,3.9505,32.26477,6.97367,49.04459.94491l3.96265-9.69832,2.33621,9.70551c5.11,2.08865,10.23327,3.85588,15.3565-.02017A36.116,36.116,0,0,0,586.99658,161.40936Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><path d="M524.916,111.05586c-10.37244-4.75592-23.387-2.52429-32.02362,4.93323-8.63672,7.45752-12.74262,19.59479-11.07526,30.88312,1.70087,11.51581,8.70172,21.43243,15.15491,31.12073,6.45325,9.68823,12.75775,20.30444,12.75055,31.94513a31.66669,31.66669,0,0,1-31.60382,31.64715c9.84027,4.23627,20.1109,8.55561,30.81256,8.05341,10.70172-.50214,21.91858-7.3819,23.66424-17.95215,1.25788-7.61627-2.47882-15.04309-5.59533-22.10547a156.36459,156.36459,0,0,1-10.928-35.97308c-1.04083-5.912-1.71857-12.12726.20886-17.81232a20.9162,20.9162,0,0,1,20.77863-13.72589l3.536-3.18121C542.1687,127.58657,535.28851,115.81172,524.916,111.05586Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><path d="M1027.60275,791.221h-859.294a1.19069,1.19069,0,0,1,0-2.38137h859.294a1.19069,1.19069,0,0,1,0,2.38137Z" transform="translate(-167.11805 -108.55378)" fill="#ccc"/><path d="M509.78722,460.77522H480.27453a4.57,4.57,0,0,0-3.49854,1.61738,6.999,6.999,0,1,0,1.80567,13.17309l3.44775,28.95416a4.6111,4.6111,0,0,0,4.40771,3.25537h19.188a4.611,4.611,0,0,0,4.40723-3.25537l4.1626-37.77691A4.6118,4.6118,0,0,0,509.78722,460.77522Zm-34.25635,13.5a5,5,0,0,1,0-10c.09131,0,.17627.0221.26661.02692a4.56348,4.56348,0,0,0,.06982,2.4408l2.11182,6.86474A4.93673,4.93673,0,0,1,475.53087,474.27522Z" transform="translate(-167.11805 -108.55378)" fill="#E56C51"/><path d="M485.26085,507.80125a11.74518,11.74518,0,0,0-1.63333-17.93562L502.328,396.98137l-20.65117,6.63046L466.708,493.45415a11.80884,11.80884,0,0,0,18.55284,14.3471Z" transform="translate(-167.11805 -108.55378)" fill="#9e616a"/><path d="M521.29675,272.77465l-12.81306-9.672s-11.30315-.80044-13.305,19.5834-14.53611,98.61661-14.53611,98.61661l-19.60653,90.81,27.14078,5.95727,21-98,21.02157-67.61024Z" transform="translate(-167.11805 -108.55378)" fill="#2f2e41"/><circle cx="464.7639" cy="241.52066" r="7.25309" fill="#E56C51"/></svg>
