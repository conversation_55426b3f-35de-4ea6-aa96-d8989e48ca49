<?php

use App\Http\Middleware\RedirectScopes;
use App\Http\Requests\TrendRequest;

Route::group(['middleware' => [RedirectScopes::class], 'prefix' => '/trends'], function () {
    Route::view('/', 'trends')->name('trends');

    foreach (Arr::objects(config('trends')) as $section) {
        $overview = Arr::first(Arr::objects($section->pages));

        Route::name($section->route)->get("/{$overview->slug}" . '{?}', fn () => null);

        Route::name("{$section->route}_redirect")->get("/{$section->path}", fn (TrendRequest $request) => (
            redirect()->route($overview->route, $request->all(), 301)
        ));

        foreach (Arr::objects($section->pages) as $page) {
            Route::name($page->route)->get("/{$page->slug}", fn (TrendRequest $request) => (
                view($page->view)
            ));

            Route::name("{$page->route}_redirect")->get("/{$section->path}/{$page->path}", fn (TrendRequest $request) => (
                redirect()->route($page->route, $request->all(), 301)
            ));

            foreach (Arr::objects($page->scopes) as $scope) {
                Route::name($scope->route)->get("/{$scope->slug}", fn (TrendRequest $request) => (
                    View::first([$scope->view, $page->view], compact('scope'))
                ));

                Route::name("{$scope->route}_redirect")->get("/{$section->path}/{$page->path}/{$scope->path}", fn (TrendRequest $request) => (
                    redirect()->route($scope->route, $request->all(), 301)
                ));
            }
        }
    }
});
