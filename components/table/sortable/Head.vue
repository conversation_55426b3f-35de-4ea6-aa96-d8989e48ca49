<template>
  <thead class="rounded-t-lg bg-gray-50">
    <template v-for="(item, index) in head">
      <th
        :key="index"
        class="py-3.5 table-break-all text-left text-sm font-medium text-gray-900 first:pl-4 first:pr-3 last:relative last:pl-3 last:pr-4 first:sm:pl-6 last:sm:pr-6 px-4"
        :class="{ 'cursor-pointer hover:bg-gray-100': item.sortable }"
        @click="handleSort(item)"
      >
        <div class="flex items-center gap-2">
          {{ item.label || item }}
          <span v-if="item.sortable && sortBy === item.key" class="text-gray-500">
            {{ sortDirection === 'asc' ? '↑' : '↓' }}
          </span>
        </div>
      </th>
    </template>
  </thead>
</template>

<script>
export default {
  props: {
    head: {
      type: Array,
      required: true
    },
    sortBy: {
      type: String,
      default: null
    },
    sortDirection: {
      type: String,
      default: 'asc'
    }
  },
  methods: {
    handleSort (item) {
      console.log(item)
      if (item.sortable) {
        this.$emit('sort', item.key)
      }
    }
  }
}
</script>
