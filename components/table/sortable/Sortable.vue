<template>
  <div class=" shadow-sm ring-1 ring-black ring-opacity-5 rounded-lg">
    <table class="min-w-full divide-y divide-gray-300">
      <TableSortableHead
        v-if="head"
        :head="head"
        :sort-by="sortBy"
        :sort-direction="sortDirection"
        class="lg:sticky lg:self-start top-0"
        @sort="handleSort"
      />
      <tbody class="divide-y divide-gray-200 rounded-b-lg bg-white">
        <slot />
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    head: {
      type: Array,
      required: false
    }
  },
  data () {
    return {
      sortBy: null,
      sortDirection: 'asc'
    }
  },
  methods: {
    handleSort (column) {
      if (this.sortBy === column) {
        // Toggle direction if clicking same column
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc'
      } else {
        // New column, default to ascending
        this.sortBy = column
        this.sortDirection = 'asc'
      }

      this.$emit('sort', { column: this.sortBy, direction: this.sortDirection })
    }
  }
}
</script>

  <style scoped>
  table tr:nth-child(even){
    background:rgba(0,0,0,0.05);
  }
  </style>
