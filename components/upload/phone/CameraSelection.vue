<template>
  <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
      <div class="mx-auto text-left md:max-w-xl md:text-center">
        <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
          Select a photo capture method
        </h1>
        <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
          If you are using a desktop computer or laptop, your photos will be process on there.
        </p>
      </div>

      <div class="mt-8 space-y-4">
        <div
          class="relative p-4 transition-all duration-200 bg-white border border-gray-200 rounded-lg shadow-sm hover:translate-x-1 hover:bg-gray-50"
          @click="$emit('next-step', 'Allow Camera')"
        >
          <div class="flex items-center justify-between gap-3 sm:gap-4">
            <div class="inline-flex items-center justify-center shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="text-gray-500 size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
                />
              </svg>
            </div>

            <p class="text-base font-bold leading-tight tracking-tight text-primary-500">
              Continue with upload guide
            </p>

            <button
              type="button"
              class="inline-flex items-center gap-1 ml-auto text-sm font-medium text-gray-500 transition-all duration-150 hover:text-gray-700"
            >
              <span class="hidden sm:inline-flex">
                Select
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                class="size-5 -mb-0.5"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
                  clip-rule="evenodd"
                />
              </svg>
              <span class="absolute inset-0" aria-hidden="true" />
            </button>
          </div>
        </div>

        <div class="relative">
          <div class="absolute inset-0 flex items-center" aria-hidden="true">
            <div class="w-full border-t border-gray-200" />
          </div>
          <div class="relative flex justify-center">
            <span class="bg-gray-50 px-2.5 text-sm font-medium text-gray-400">
              OR
            </span>
          </div>
        </div>

        <div
          class="relative p-4 transition-all duration-200 bg-white border border-gray-200 rounded-lg shadow-sm hover:translate-x-1 hover:bg-gray-50 lg:hidden cursor-pointer"
          @click="$emit('freeCamera')"
        >
          <div class="flex items-center justify-between gap-3 sm:gap-4">
            <div class="inline-flex items-center justify-center shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="text-gray-500 size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"
                />
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"
                />
              </svg>
            </div>

            <p class="text-base font-bold leading-tight tracking-tight text-primary-500">
              Take selfies without guide
            </p>

            <button
              type="button"
              class="inline-flex items-center gap-1 ml-auto text-sm font-medium text-gray-500 transition-all duration-150 hover:text-gray-700"
            >
              <span class="hidden sm:inline-flex">
                Select
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                class="size-5 -mb-0.5"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>

        <div
          class="relative p-4 transition-all duration-200 bg-white border border-gray-200 rounded-lg shadow-sm hover:translate-x-1 hover:bg-gray-50"
        >
          <div class="flex items-center justify-between gap-3 sm:gap-4">
            <div class="inline-flex items-center justify-center shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="text-gray-500 size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                />
              </svg>
            </div>

            <p class="text-base font-bold leading-tight tracking-tight text-primary-500">
              Upload from my photo gallery
            </p>

            <button
              type="button"
              class="inline-flex items-center gap-1 ml-auto text-sm font-medium text-gray-500 transition-all duration-150 hover:text-gray-700"
              @click="$emit('uploadFromGallery')"
            >
              <span class="hidden sm:inline-flex">
                Select
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                class="size-5 -mb-0.5"
              >
                <path
                  fill-rule="evenodd"
                  d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
                  clip-rule="evenodd"
                />
              </svg>
              <span class="absolute inset-0" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    pauseFor: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      loading: false
    }
  },
  watch: {
    pauseFor (newVal) {
      if (newVal > 0) {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$emit('resetPause')
        }, newVal)
      }
    }
  }
}
</script>
