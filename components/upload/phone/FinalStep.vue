<template>
  <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
      <div class="mx-auto text-left md:max-w-xl md:text-center">
        <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
          {{ $t('Photos uploaded successfully!') }}
        </h1>
        <p v-if="isPhoneFlow" class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
          {{ $t('You can now close this page and continue from your previous window.') }}
        </p>
        <p v-else class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
          {{ $t('If you started this process from your desktop, you can now close this page and continue from your computer.') }}
        </p>

        <!-- Success confirmation with desktop visibility tip -->
        <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div class="flex items-start gap-3">
            <svg class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd" />
            </svg>
            <div class="text-left">
              <p class="text-sm font-medium text-green-800">
                {{ $t('Your photos have been uploaded successfully') }}
              </p>
              <p v-if="isPhoneFlow" class="text-sm text-green-700 mt-1">
                {{ $t('💡 Tip: Your photos should now be visible on your desktop. If photos don\'t appear, try refreshing the page.') }}
              </p>
              <p v-else class="text-sm text-green-700 mt-1">
                {{ $t('If photos don\'t appear on your desktop, try refreshing the page.') }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <p class="text-lg font-bold tracking-tight text-primary-500">
          Need more photos?
        </p>
        <p class="mt-0.5 text-base font-medium text-gray-500">
          Click the button bellow to capture more photos or upload them from your phone library
        </p>
      </div>
    </div>

    <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <ButtonPrimary
          class="w-full"
          type="button"
          @click="$emit('next-step', 'Camera Selection')"
        >
          I want to upload more photos
        </ButtonPrimary>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    pauseFor: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      loading: false,
      isPhoneFlow: false
    }
  },
  watch: {
    pauseFor (newVal) {
      if (newVal > 0) {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$emit('resetPause')
        }, newVal)
      }
    }
  },
  mounted () {
    this.isPhoneFlow = !!this.$route.query.phoneFlow
  }
}
</script>

<i18n>
{
  "en": {
    "Photos uploaded successfully!": "Photos uploaded successfully!",
    "You can now close this page and continue from your previous window.": "You can now close this page and continue from your previous window.",
    "If you started this process from your desktop, you can now close this page and continue from your computer.": "If you started this process from your desktop, you can now close this page and continue from your computer.",
    "Your photos have been uploaded successfully": "Your photos have been uploaded successfully",
    "💡 Tip: Your photos should now be visible on your desktop. If photos don't appear, try refreshing the page.": "💡 Tip: Your photos should now be visible on your desktop. If photos don't appear, try refreshing the page.",
    "If photos don't appear on your desktop, try refreshing the page.": "If photos don't appear on your desktop, try refreshing the page."
  },
  "es": {
    "Photos uploaded successfully!": "¡Fotos subidas con éxito!",
    "You can now close this page and continue from your previous window.": "Ahora puedes cerrar esta página y continuar desde tu ventana anterior.",
    "If you started this process from your desktop, you can now close this page and continue from your computer.": "Si iniciaste este proceso desde tu escritorio, ahora puedes cerrar esta página y continuar desde tu computadora.",
    "Your photos have been uploaded successfully": "Tus fotos se han subido con éxito",
    "💡 Tip: Your photos should now be visible on your desktop. If photos don't appear, try refreshing the page.": "💡 Consejo: Tus fotos ahora deberían ser visibles en tu escritorio. Si las fotos no aparecen, intenta actualizar la página.",
    "If photos don't appear on your desktop, try refreshing the page.": "Si las fotos no aparecen en tu escritorio, intenta actualizar la página."
  },
  "de": {
    "Photos uploaded successfully!": "Fotos erfolgreich hochgeladen!",
    "You can now close this page and continue from your previous window.": "Du kannst diese Seite jetzt schließen und von deinem vorherigen Fenster aus fortfahren.",
    "If you started this process from your desktop, you can now close this page and continue from your computer.": "Wenn du diesen Prozess von deinem Desktop aus gestartet hast, kannst du diese Seite jetzt schließen und von deinem Computer aus fortfahren.",
    "Your photos have been uploaded successfully": "Deine Fotos wurden erfolgreich hochgeladen",
    "💡 Tip: Your photos should now be visible on your desktop. If photos don't appear, try refreshing the page.": "💡 Tipp: Deine Fotos sollten jetzt auf deinem Desktop sichtbar sein. Wenn die Fotos nicht erscheinen, versuche die Seite zu aktualisieren.",
    "If photos don't appear on your desktop, try refreshing the page.": "Wenn die Fotos nicht auf deinem Desktop erscheinen, versuche die Seite zu aktualisieren."
  }
}
</i18n>
