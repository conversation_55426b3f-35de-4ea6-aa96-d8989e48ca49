<template>
  <ButtonWhite size="sm" @click="openGoogleFilePicker">
    <div class="flex items-center gap-2">
      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" width="35.78" height="32" viewBox="0 0 256 229"><path fill="#0066DA" d="m19.354 196.034l11.29 19.5c2.346 4.106 5.718 7.332 9.677 9.678c11.34-14.394 19.232-25.44 23.68-33.137c4.513-7.811 10.06-20.03 16.641-36.655c-17.736-2.335-31.176-3.502-40.32-3.502c-8.777 0-22.217 1.167-40.322 3.502c0 4.545 1.173 9.09 3.519 13.196z" /><path fill="#EA4335" d="M215.681 225.212c3.96-2.346 7.332-5.572 9.677-9.677l4.692-8.064l22.434-38.855a26.566 26.566 0 0 0 3.518-13.196c-18.21-2.335-31.625-3.502-40.247-3.502c-9.266 0-22.682 1.167-40.248 3.502c6.503 16.716 11.977 28.935 16.422 36.655c4.483 7.789 12.4 18.834 23.752 33.137" /><path fill="#00832D" d="M128.001 73.311c13.12-15.845 22.162-28.064 27.125-36.655c3.997-6.918 8.396-17.964 13.196-33.137C164.363 1.173 159.818 0 155.126 0h-54.25C96.184 0 91.64 1.32 87.68 3.519c6.106 17.402 11.288 29.787 15.544 37.154c4.704 8.142 12.963 19.021 24.777 32.638" /><path fill="#2684FC" d="M175.36 155.42H80.642l-40.32 69.792c3.958 2.346 8.503 3.519 13.195 3.519h148.968c4.692 0 9.238-1.32 13.196-3.52z" /><path fill="#00AC47" d="M128.001 73.311L87.681 3.52c-3.96 2.346-7.332 5.571-9.678 9.677L3.519 142.224A26.567 26.567 0 0 0 0 155.42h80.642z" /><path fill="#FFBA00" d="m215.242 77.71l-37.243-64.514c-2.345-4.106-5.718-7.331-9.677-9.677l-40.32 69.792l47.358 82.109h80.496c0-4.546-1.173-9.09-3.519-13.196z" /></svg>
      <p>Google Drive</p>
    </div>
  </ButtonWhite>
</template>
<!-- eslint-disable no-undef -->
<script>
export default {
  data () {
    return {
      googleScope: 'https://www.googleapis.com/auth/drive.file',
      // googleScope: 'https://www.googleapis.com/auth/drive.metadata.readonly',
      pickerApiLoaded: false,
      accessToken: null,
      tokenClient: null
    }
  },
  computed: {
    googleApiKey () {
      return process.env.GOOGLE_API_KEY
    },
    googleClientId () {
      return process.env.GOOGLE_CLIENT_ID
    }
  },
  mounted () {
    this.loadScript('https://apis.google.com/js/api.js', this.onApiLoad)
    this.loadScript('https://accounts.google.com/gsi/client', this.gisLoaded)
  },
  methods: {
    loadScript (src, callback) {
      const script = document.createElement('script')
      script.src = src
      script.onload = callback
      document.head.appendChild(script)
    },
    onApiLoad () {
      gapi.load('picker', () => {
        this.pickerApiLoaded = true
      })
    },
    gisLoaded () {
      this.tokenClient = google.accounts.oauth2.initTokenClient({
        // eslint-disable-next-line camelcase
        client_id: this.googleClientId,
        scope: this.googleScope,
        callback: (response) => {
          console.log(response)
          if (response.error !== undefined) {
            this.$toast.error('Error:', response.error)
            return
          }
          this.accessToken = response.access_token
          this.createPicker()
        }
      })
    },
    openGoogleFilePicker () {
      if (this.pickerApiLoaded && this.tokenClient) {
        if (this.accessToken) {
          this.createPicker()
        } else {
          this.tokenClient.requestAccessToken({ prompt: 'consent' })
        }
      } else {
        this.$toast.error('Google Picker or GIS not fully initialized')
      }
    },
    createPicker () {
      try {
        const picker = new google.picker.PickerBuilder()
          .addView(google.picker.ViewId.DOCS_IMAGES)
          .setOAuthToken(this.accessToken)
          .setDeveloperKey(this.googleApiKey)
          .setCallback(this.handleFileSelect)
          .enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
          .setAppId('169782975036')
          .build()
        picker.setVisible(true)
      } catch (error) {
        this.$toast.error(error)
      }
    },
    handleFileSelect (data) {
      if (data[google.picker.Response.ACTION] === google.picker.Action.PICKED) {
        const documents = data[google.picker.Response.DOCUMENTS]
        const blobsPromises = documents.map(async (doc) => {
          try {
            const fileId = doc.id
            const response = await this.$axios.post(`/image/drive/${fileId}`, {
              accessToken: this.accessToken
            }, {
              headers: {
                Authorization: `Bearer ${this.accessToken}`
              },
              responseType: 'arraybuffer'
            })
            const arrayBuffer = response.data
            return arrayBuffer
          } catch (err) {
            console.log(err)
            return null
          }
        })
        Promise.all(blobsPromises)
          .then((blobs) => {
            blobs.forEach((blob) => {
              this.$emit('imageSelected', blob)
            })
          })
          .catch((error) => {
            this.$toast.error('Error fetching files:', error)
          })
      }
    }
  }
}
</script>
