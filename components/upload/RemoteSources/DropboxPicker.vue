<template>
  <ButtonWhite size="sm" @click="openDropboxFilePicker">
    <div class="flex items-center gap-2">
      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" width="37.58" height="32" viewBox="0 0 256 218"><path fill="#0061FF" d="M63.995 0L0 40.771l63.995 40.772L128 40.771zM192 0l-64 40.775l64 40.775l64.001-40.775zM0 122.321l63.995 40.772L128 122.321L63.995 81.55zM192 81.55l-64 40.775l64 40.774l64-40.774zM64 176.771l64.005 40.772L192 176.771L128.005 136z" />
      </svg>
      <p>Dropbox</p>
    </div>
  </ButtonWhite>
</template>

<script>
export default {
  data () {
    return {
      dropboxOptions: {
        success: (files) => {
          const blobsPromises = files.map((file) => {
            return fetch(file.link, {
              method: 'GET'
            }).then(res => res.blob())
          })
          Promise.all(blobsPromises)
            .then((blobs) => {
              blobs.forEach((blob) => {
                this.$emit('imageSelected', blob)
              })
            })
            .catch((error) => {
              this.$toast.error('Error fetching files:', error)
            })
        },
        linkType: 'direct',
        extensions: ['.jpg', '.png', '.jpeg'],
        multiselect: true
      }
    }
  },
  head () {
    return {
      script: [
        {
          src: 'https://www.dropbox.com/static/api/2/dropins.js',
          'data-app-key': '2wszg6s6puctlkz',
          hid: 'dropbox-picker',
          id: 'dropboxjs'
        }
      ]
    }
  },
  methods: {
    openDropboxFilePicker () {
      if (!window.Dropbox) {
        this.$toast.error('Dropbox is not available at the moment. Please try again later.')
      } else {
        window.Dropbox.choose(this.dropboxOptions)
      }
    }
  }
}
</script>
