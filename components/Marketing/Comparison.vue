<template>
  <section id="styles" class="py-12  sm:py-16 lg:py-20 xl:py-24 xl:pt-12 lg:pt-12">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-3xl mx-auto text-center">
        <h3 class="text-lg font-bold text-teal-500">
          {{ $t('Photorealistic headshots') }}
        </h3>
        <h2 class="mt-6 text-2xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          {{ $t('We take our quality very seriously') }}
        </h2>
        <p class="mt-4 text-base font-normal text-gray-600">
          {{ $t('With the same uploaded photos, HeadshotPro\'s AI headshot generator performs far better than alternatives. Why? Because we only do professional headshots. Our AI experts are solely focused on optimizing towards studio quality headshots you’re proud to use in professional settings.') }}
        </p>
        <div class="hidden md:flex w-full items-center justify-center pt-12 pb-4">
          <ImageDns class="w-[300px]" width="300" :src="require('@/assets/img/comparison/before.png')" />
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto mt-8 md:mt-0 px-4 md:px-0">
      <div class="space-y-8">
        <div class="flex flex-col justify-center items-center space-y-3">
          <img width="181" height="28" class="h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
          <ImageDns :src="require('@/assets/img/comparison/us.jpg')" class="w-full object-cover" />
        </div>
        <div>
          <ul class="grid grid-cols-2 gap-3 md:gap-4">
            <ListCheckSolid>{{ $t('Indistinguishable from real') }}</ListCheckSolid>
            <ListCheckSolid>{{ $t('14 days money back guaranteed') }}</ListCheckSolid>
            <ListCheckSolid>{{ $t('Choose clothing and location') }}</ListCheckSolid>
            <ListCheckSolid>{{ $t('High resemblance') }}</ListCheckSolid>
            <ListCheckSolid>{{ $t('Clear and sharp') }}</ListCheckSolid>
            <ListCheckSolid>{{ $t('Matching poses') }}</ListCheckSolid>
          </ul>
        </div>
      </div>

      <div class="space-y-8">
        <div class="flex flex-col justify-center items-center space-y-3">
          <span class="font-bold text-[#AE051A] text-[17px]">{{ $t('Known alternatives') }}</span>
          <ImageDns :src="require('@/assets/img/comparison/them.jpg')" class="w-full object-cover" />
        </div>
        <div>
          <ul class="grid grid-cols-2 gap-3 md:gap-4 opacity-[0.7]">
            <ListCrossSolid>{{ $t('Obvious AI generated') }}</ListCrossSolid>
            <ListCrossSolid>{{ $t('No refund after usage') }}</ListCrossSolid>
            <ListCrossSolid>{{ $t('Random clothing and location') }}</ListCrossSolid>
            <ListCrossSolid>{{ $t('Deformed faces') }}</ListCrossSolid>
            <ListCrossSolid>{{ $t('Unsharp and blurry') }}</ListCrossSolid>
            <ListCrossSolid>{{ $t('Random poses') }}</ListCrossSolid>
          </ul>
        </div>
      </div>
    </div>
    <div class="mt-16 w-full mx-auto max-w-3xl bg-[#E8F6E5] rounded-md p-3 px-6 flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
      <div class="flex items-center justify-center md:justify-start">
        <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
        <p class="font-medium text-center md:text-left text-green-900">
          {{ $t('Get your headshots done in 2 hours, right from your home') }}
        </p>
      </div>
      <nuxt-link to="/auth/login?redirect=%2Fapp%2Fadd">
        <ButtonPrimary size="sm">
          <span class="font-medium">{{ $t('Create your headshots') }}</span>
          <IconChevron class="w-4 h-4 ml-1.5" />
        </ButtonPrimary>
      </nuxt-link>
    </div>
  </section>
</template>

<script>
export default {
  // props: {
  //   styles: {
  //     type: Array,
  //     default: () => []
  //   },
  //   clothing: {
  //     type: Array,
  //     default: () => []
  //   }
  // }
  computed: {
    styles () {
      return this.$store.state.styles
    },
    clothing () {
      return this.$store.state.clothing
    }
  }

}
</script>

<i18n>
  {
    "es": {
      "Photorealistic headshots": "Retratos fotorrealistas",
      "We take our quality very seriously": "Nos tomamos la calidad muy en serio",
      "With the same uploaded photos, HeadshotPro's AI headshot generator performs far better than alternatives. Why? Because we only do professional headshots. Our AI experts are solely focused on optimizing towards studio quality headshots you’re proud to use in professional settings.": "Con las mismas fotos subidas, el generador de retratos de IA de HeadshotPro funciona mucho mejor que las alternativas. ¿Por qué? Porque solo hacemos retratos profesionales. Nuestros expertos en IA están enfocados únicamente en optimizar hacia retratos de calidad de estudio de los que te sientas orgulloso de usar en entornos profesionales.",
      "Indistinguishable from real": "Indistinguible de lo real",
      "14 days money back guaranteed": "Garantía de devolución de dinero de 14 días",
      "Choose clothing and location": "Elige la ropa y la ubicación",
      "High resemblance": "Alta semejanza",
      "Clear and sharp": "Claro y nítido",
      "Matching poses": "Poses coincidentes",
      "Known alternatives": "Alternativas conocidas",
      "Obvious AI generated": "Obviamente generado por IA",
      "No refund after usage": "Sin reembolso después del uso",
      "Random clothing and location": "Ropa y ubicación aleatorias",
      "Deformed faces": "Caras deformadas",
      "Unsharp and blurry": "Desenfocado y borroso",
      "Random poses": "Poses aleatorias",
      "Get your headshots done in 2 hours, right from your home": "Obtén tus retratos en 2 horas, directamente desde tu casa"
    }
  }
</i18n>
