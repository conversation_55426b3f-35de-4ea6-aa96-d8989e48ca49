<template>
  <div
    class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg border border-[#21B8BA] p-4 sm:p-6 md:p-8 space-y-6"
  >
    <div>
      <div class="md:gap-12 md:flex md:items-start">
        <div class="flex-1 min-w-0">
          <span
            v-if="currentDiscountTier?.discount > 0"
            class="px-4 py-1 text-xs font-extrabold tracking-wide text-white uppercase bg-[#21B8BA] rounded-full"
          >
            {{ currentDiscountTier?.discount || 0 }}% DISCOUNT
          </span>

          <div class="flex items-end gap-0.5 mt-4">
            <p class="text-[40px] leading-none text-primary-500 font-bold tracking-[-0.2px]">
              ${{ formattedPrice }}
            </p>
            <p class="text-base font-normal text-paragraph">
              / team member
            </p>
          </div>

          <div class="mt-4 space-y-2">
            <div class="flex flex-col space-y-3">
              <div class="flex space-x-2 w-full items-center justify-start">
                <p class="text-sm font-normal tracking-[-0.2px] text-[#474368]">
                  Total members:
                </p>
                <Input v-model="total" type="number" :min="1" class="w-[80px]" @input="checkForQuantity" />
              </div>
              <div class="flex flex-col space-y-2 w-full">
                <input
                  v-model="total"
                  type="range"
                  class="w-full bg-transparent cursor-pointer appearance-none disabled:opacity-50 disabled:pointer-events-none focus:outline-none
                        [&::-webkit-slider-thumb]:w-6
                        [&::-webkit-slider-thumb]:h-6
                        [&::-webkit-slider-thumb]:-mt-2
                        [&::-webkit-slider-thumb]:appearance-none
                        [&::-webkit-slider-thumb]:bg-[#1B145D]
                        [&::-webkit-slider-thumb]:shadow-[0_0_0_3px_rgba(255,255,255,1)]
                        [&::-webkit-slider-thumb]:rounded-full
                        [&::-webkit-slider-thumb]:transition-all
                        [&::-webkit-slider-thumb]:duration-150
                        [&::-webkit-slider-thumb]:ease-in-out

                        [&::-moz-range-thumb]:w-6
                        [&::-moz-range-thumb]:h-6
                        [&::-moz-range-thumb]:appearance-none
                        [&::-moz-range-thumb]:bg-[#1B145D]
                        [&::-moz-range-thumb]:border-3
                        [&::-moz-range-thumb]:border-white
                        [&::-moz-range-thumb]:rounded-full
                        [&::-moz-range-thumb]:transition-all
                        [&::-moz-range-thumb]:duration-150
                        [&::-moz-range-thumb]:ease-in-out

                        [&::-webkit-slider-runnable-track]:w-full
                        [&::-webkit-slider-runnable-track]:h-2.5
                        [&::-webkit-slider-runnable-track]:bg-[#EAECF0]
                        [&::-webkit-slider-runnable-track]:rounded-full

                        [&::-moz-range-track]:w-full
                        [&::-moz-range-track]:h-2.5
                        [&::-moz-range-track]:bg-[#EAECF0]
                        [&::-moz-range-track]:rounded-full"
                  min="1"
                  :max="teamMaxSeats"
                >
              </div>
            </div>
          </div>
          <div class="mt-6 w-full">
            <nuxt-link to="/app" role="button" class="w-full">
              <ButtonPrimary class="w-full">
                Get started now
              </ButtonPrimary>
            </nuxt-link>
          </div>
          <div class="mt-6">
            <p class="text-base font-bold text-primary-500">
              Includes:
            </p>

            <ul class="mt-2.5 space-y-1.5 text-base font-normal text-paragraph">
              <li class="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="w-5 h-5 -mb-0.5 text-[#00B67A]"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                {{ photoPerStyle * 8 }} headshots per person
              </li>

              <li class="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="w-5 h-5 -mb-0.5 text-[#00B67A]"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                Pick from 100+ backdrops & outfits
              </li>

              <li class="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="w-5 h-5 -mb-0.5 text-[#00B67A]"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                2K sharp resolution
              </li>

              <li class="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="w-5 h-5 -mb-0.5 text-[#00B67A]"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                Custom branded profile pictures
              </li>
              <li class="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="w-5 h-5 -mb-0.5 text-[#00B67A]"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
                Easy to use admin dashboard
              </li>
            </ul>
          </div>
        </div>

        <div class="bg-[#F8FCFF] mt-6 md:mt-0 border border-[#ECEAFF] rounded-lg p-6 md:w-[20rem]">
          <h3 class="text-lg font-bold text-primary-500">
            💰 Get a lifetime bulk discount
          </h3>
          <p class="text-[15px] text-paragraph mt-2">
            Get a lifetime discount based on the amount of seats you purchase on your organisation. This includes repurchases.
          </p>
          <ul class="space-y-2 mt-4">
            <template v-for="option in teamDiscountOptions">
              <li v-if="option.discount > 0" :key="option.value" class="text-paragraph flex items-center justify-start gap-3">
                <span class="w-[90px] font-medium text-[15px]">{{ option.label }} users</span>
                <IconArrowRight class="w-4 h-4" />
                <CheckoutPercentageOffCircle :percentage="option.discount" />
              </li>
            </template>
          </ul>
          <!-- <p class="text-[#474368] text-lg font-normal">
            Offline Photoshoot: <span class="font-bold tabular-nums text-sm"><strike>${{ (total * 232.5).toFixed(2) }}</strike></span>
          </p>
          <p class="text-[#474368] text-lg font-normal flex justify-start items-end gap-1 mt-2">
            <Logo class="h-6 w-auto inline-block" />
            <span>: <span class="font-bold tabular-nums">${{ (total * price).toFixed(2) }}</span></span>
          </p>
          <p class="mt-2 text-[11px] italic text-paragraph">
            The average cost of professional headshots in the United States is <a
              href="/blog/how-much-does-a-headshot-cost"
              class="underline"
              target="_blank"
            >$232.50 per session</a>* Our packages
            start from $29.
          </p> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      total: 1,
      totalPrice: 39
    }
  },
  computed: {
    price () {
    // teamDiscountOptions
      const { teamDiscountOptions, total } = this
      const activeBulkDiscount = teamDiscountOptions.find(item => total >= item.from && total <= item.to)
      if (activeBulkDiscount) {
        return this.totalPrice * (1 - activeBulkDiscount.discount / 100)
      }
      return this.totalPrice
    },
    currentDiscountTier () {
      const { teamDiscountOptions, total } = this
      const activeBulkDiscount = teamDiscountOptions.find(item => total >= item.from && total <= item.to)
      return activeBulkDiscount
    },
    // price () {
    //   const { total } = this
    //   let totalPrice = this.totalPrice
    //   if (total >= 200) {
    //     totalPrice *= 0.4
    //   } else if (total >= 50) {
    //     totalPrice *= 0.5
    //   } else if (total >= 10) {
    //     totalPrice *= 0.7
    //   } else if (total >= 5) {
    //     totalPrice *= 0.8
    //   }
    //   return totalPrice
    // },
    formattedPrice () {
      // use 0 or 2 decimals
      return this.price.toFixed(this.price % 1 === 0 ? 0 : 2)
    }
  },
  methods: {
    checkForQuantity ($event) {
      const quantity = parseInt($event)
      if (quantity < 1 || $event === '') {
        this.total = 1
      }
    }
  }

}
</script>

<style>

</style>
