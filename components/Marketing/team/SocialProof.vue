<template>
  <section id="social-proof" class="py-12 pb-4">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="max-w-5xl mx-auto text-center">
        <h2 class="text-lg font-bold text-teal-500">
          Reviews & examples
        </h2>
        <p class="mt-6 text-3xl font-medium lg:leading-[42px] tracking-tight sm:text-3xl lg:text-4xl text-primary-500">
          <span class=" font-bold  text-teal-500"> {{ $store.state.stats.photos }}</span> AI headshots already created <br class="hidden md:block">for <span class=" font-bold  text-yellow-500"> {{ $store.state.stats.users }}</span> happy customers!
        </p>
        <p class="mt-4 text-base font-normal text-gray-600">
          You're in good company. Companies of all sizes trust HeadshotPro.
        </p>
      </div>
      <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto mt-12">
        <div class="bg-[#F3FBF2] p-6 rounded-md space-y-4 pb-12">
          <h3 class="font-medium text-2xl text-brand-500 tracking-[-1.5px]">
            for your team
          </h3>
          <div>
            <ul class="flex flex-col space-y-2.5">
              <ListCheckSolid>no awkward photoshoots</ListCheckSolid>
              <ListCheckSolid>get it done right from their home</ListCheckSolid>
              <ListCheckSolid>boost confidence with a good photo</ListCheckSolid>
              <ListCheckSolid>increase trust of customers</ListCheckSolid>
            </ul>
          </div>
        </div>
        <div class="bg-[#F3FBF2] p-6 rounded-md space-y-4 pb-12">
          <h3 class="font-medium text-2xl text-brand-500 tracking-[-1.5px]">
            and for you
          </h3>
          <div>
            <ul class="flex flex-col space-y-2.5">
              <ListCheckSolid>save up to 10x the cost</ListCheckSolid>
              <ListCheckSolid>onboard new employees easily</ListCheckSolid>
              <ListCheckSolid>coordinate from one simple app</ListCheckSolid>
              <ListCheckSolid>no missing members due to sickness</ListCheckSolid>
            </ul>
          </div>
        </div>
      </div>
      <div class="isolate mx-auto max-w-2xl rounded-lg border-2 border-primary-500 bg-white px-6 py-5 shadow-lg sm:px-8 mt-[-24px]">
        <template v-if="!isLoggedIn">
          <div class="flex flex-col gap-6 lg:flex-row lg:items-end">
            <div class="flex-1">
              <label for="companyName" class="block text-sm font-medium leading-6 text-gray-900">Company name</label>
              <div class="mt-1">
                <input
                  id="companyName"
                  v-model="companyName"
                  type="text"
                  name="companyName"
                  class="block h-12 w-full rounded-lg border-0 px-4 py-2 text-gray-900 placeholder-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-teal-500"
                  placeholder="Acme Corp"
                >
              </div>
            </div>

            <div class="flex-1">
              <label for="teamSize" class="block text-sm font-medium leading-6 text-gray-900">Team size</label>
              <div class="mt-1">
                <input
                  id="teamSize"
                  v-model="teamSize"
                  type="number"
                  name="teamSize"
                  class="block h-12 w-full rounded-lg border-0 px-4 py-2 text-gray-900 placeholder-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-teal-500"
                  placeholder="5"
                  min="1"
                  max="100"
                >
              </div>
            </div>

            <div class="flex flex-col items-center gap-4 sm:flex-row sm:gap-6">
              <button title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-[#ff6600] px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button" @click="createTeam">
                Create your team
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </template>
        <template v-else>
          <nuxt-link to="/app">
            <ButtonPrimary class="w-full md:auto">
              To dashboard
            </ButtonPrimary>
          </nuxt-link>
        </template>
      </div>
    </div>
    <div class="hidden md:grid md:grid-cols-3 gap-4 mt-8 max-w-6xl mx-auto">
      <LandingpageRandomTrustpilotReview v-for="i in 3" :key="i" />
    </div>
  </section>
</template>

<script>
export default {
  data () {
    return {
      companyName: null,
      teamSize: null
    }
  },
  methods: {
    createTeam () {
      const url = '/app/admin/team/new'
      if (this.companyName && this.teamSize) {
        this.$router.push({ path: url, query: { companyName: this.companyName, teamSize: this.teamSize } })
      } else {
        this.$router.push({ path: url })
      }
    }
  }

}
</script>

<style>

</style>
