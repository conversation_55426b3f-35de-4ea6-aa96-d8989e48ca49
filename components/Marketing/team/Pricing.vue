<template>
  <section id="pricing" class="py-12 bg-[#F8FCFF] sm:py-16 lg:py-20 xl:py-24">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
      <div class="relative max-w-4xl mx-auto text-left md:text-center">
        <!-- <div class="flex items-center justify-start md:justify-center gap-x-3">
          <img class="w-auto h-6" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
          <img class="w-auto h-6" src="@/assets/img/logo-trustpilot.png" alt="" loading="lazy">
        </div> -->
        <div class="flex items-center justify-center">
          <TrustpilotRating />
        </div>
        <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500">
          How Much Do Corporate Headshots Cost?
        </h2>
        <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-xl">
          The average cost of professional headshots in the United States is <a href="#" title="" class="underline">
            $232.50 per
            session</a>* Our
          packages start
          from $29.
        </p>
      </div>

      <div class="relative mt-8 sm:mt-12">
        <MarketingTeamPricingCalculator />

        <div class="absolute flex-col items-center hidden gap-1 xl:flex right-24 -top-32">
          <p
            class="text-base leading-4 rotate-[-12deg] py-4 text-right font-cursive text-paragraph tracking-[-0.056px]"
          >
            Not only is it faster, we<br>
            also save you a lot of money
          </p>
          <svg
            class="w-auto h-16 text-paragraph"
            viewBox="0 0 45 62"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.36371 57.3665C12.1116 58.1161 14.7701 58.8861 17.8017 59.7086C16.9652 60.9529 15.5586 61.2271 14.2447 60.9761C10.3031 60.2232 6.32146 59.3877 2.39855 58.2639C0.15336 57.629 -0.379449 55.9172 1.03385 54.6333C3.3544 52.4556 5.84454 50.4226 8.29461 48.3071C8.43347 48.1838 8.59704 48.0091 8.75126 48.0199C9.32807 47.9803 9.92024 48.0747 10.5371 48.1177C10.5585 48.5711 10.8389 49.1488 10.6259 49.4264C9.91296 50.4135 9.09517 51.2869 8.34213 52.1915C7.94091 52.6952 7.49963 53.1164 7.42822 54.095C8.47703 53.9022 9.5906 53.7405 10.6394 53.5477C31.9307 48.7035 43.9866 31.8919 40.8717 11.6581C40.4504 9.05025 39.6959 6.47234 39.1203 3.85373C38.9234 2.9362 38.7912 2.04975 38.5942 1.13222C38.7978 1.04007 39.0415 1.03045 39.2451 0.938297C39.7538 1.37246 40.5062 1.79702 40.7219 2.34369C41.5384 4.12843 42.355 5.91318 42.9031 7.75898C48.4341 26.1141 40.3659 44.0806 22.6323 52.4688C19.2356 54.0665 15.4042 55.0757 11.7824 56.3121C11.0174 56.5779 10.2369 56.7096 9.43179 56.8928C9.31763 56.9646 9.30829 57.15 9.36371 57.3665Z"
            />
          </svg>
        </div>
      </div>

      <div class="absolute flex-col items-center hidden gap-1 xl:flex right-24 -top-32">
        <p
          class="text-base leading-4 rotate-[-12deg] py-4 text-right font-cursive text-paragraph tracking-[-0.056px]"
        >
          Not only is it faster, we<br>
          also save you a lot of money
        </p>
        <svg
          class="w-auto h-16 text-paragraph"
          viewBox="0 0 45 62"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.36371 57.3665C12.1116 58.1161 14.7701 58.8861 17.8017 59.7086C16.9652 60.9529 15.5586 61.2271 14.2447 60.9761C10.3031 60.2232 6.32146 59.3877 2.39855 58.2639C0.15336 57.629 -0.379449 55.9172 1.03385 54.6333C3.3544 52.4556 5.84454 50.4226 8.29461 48.3071C8.43347 48.1838 8.59704 48.0091 8.75126 48.0199C9.32807 47.9803 9.92024 48.0747 10.5371 48.1177C10.5585 48.5711 10.8389 49.1488 10.6259 49.4264C9.91296 50.4135 9.09517 51.2869 8.34213 52.1915C7.94091 52.6952 7.49963 53.1164 7.42822 54.095C8.47703 53.9022 9.5906 53.7405 10.6394 53.5477C31.9307 48.7035 43.9866 31.8919 40.8717 11.6581C40.4504 9.05025 39.6959 6.47234 39.1203 3.85373C38.9234 2.9362 38.7912 2.04975 38.5942 1.13222C38.7978 1.04007 39.0415 1.03045 39.2451 0.938297C39.7538 1.37246 40.5062 1.79702 40.7219 2.34369C41.5384 4.12843 42.355 5.91318 42.9031 7.75898C48.4341 26.1141 40.3659 44.0806 22.6323 52.4688C19.2356 54.0665 15.4042 55.0757 11.7824 56.3121C11.0174 56.5779 10.2369 56.7096 9.43179 56.8928C9.31763 56.9646 9.30829 57.15 9.36371 57.3665Z"
          />
        </svg>
      </div>
    </div>

    <div class="max-w-2xl mx-auto mt-8">
      <MarketingLogoCloud />
    </div>

    <!-- <LandingpageV2PricingTiersBottomTestimonials /> -->
    <div class="hidden md:grid md:grid-cols-3 gap-4 mt-8 max-w-6xl mx-auto">
      <client-only>
        <LandingpageRandomTrustpilotReview v-for="i in 9" :key="i" />
      </client-only>
    </div>
  </section>
</template>

<script>
export default {
  data () {
    return {
      total: 5,
      totalPrice: 39
    }
  },
  computed: {
    price () {
      const { total } = this
      let totalPrice = this.totalPrice
      if (total >= 200) {
        totalPrice *= 0.4
      } else if (total >= 50) {
        totalPrice *= 0.5
      } else if (total >= 10) {
        totalPrice *= 0.7
      } else if (total >= 5) {
        totalPrice *= 0.8
      }
      return totalPrice
    },
    formattedPrice () {
      // use 0 or 2 decimals
      return this.price.toFixed(this.price % 1 === 0 ? 0 : 2)
    }
  }

}
</script>

<style>

</style>
