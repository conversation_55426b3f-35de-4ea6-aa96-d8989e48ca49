<template>
  <div class="bg-white py-24 sm:py-32">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-2xl text-center">
        <h2 class="text-3xl font-bold tracking-tight text-brand-500 sm:text-4xl">
          {{ title }}
        </h2>
      </div>
      <div class="mx-auto mt-8 max-w-2xl sm:mt-12 lg:mt-12 lg:max-w-none">
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
          <div v-for="(feature, index) in steps" :key="feature.name" class="flex flex-col">
            <dt class="text-base font-medium leading-7 text-gray-900">
              <div class="mb-6 flex h-10 w-10 items-center justify-center rounded-lg bg-teal-100">
                <strong>{{ index }}</strong>
              </div>
              {{ feature.name }}
            </dt>
            <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-gray-600">
              <p class="flex-auto">
                {{ feature.description }}
              </p>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    steps: {
      type: Array,
      required: true
    }
  }
}
</script>
