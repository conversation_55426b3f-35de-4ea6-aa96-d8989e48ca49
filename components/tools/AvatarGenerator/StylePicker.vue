<template>
  <div class="grid grid-cols-3 gap-3">
    <button
      v-for="style in styles"
      :key="style.name"
      class="flex flex-col items-center gap-2 focus:outline-none"
      @click="$emit('style-selected', style.name)"
    >
      <div
        class="h-24 w-24 rounded-full bg-gray-100 bg-cover bg-center"
        :style="{ backgroundImage: `url(${style.image})` }"
        :class="{
          'border border-indigo-500 ring-4 ring-indigo-500/50':
            selectedStyle === style.name,
        }"
      />
      <p class="text-xs font-medium text-gray-600">
        {{ style.label }}
      </p>
    </button>
  </div>
</template>
<script>
export default {
  layout: 'tools',
  props: {
    selectedStyle: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      styles: [
        {
          name: 'flat illustration',
          label: 'Flat illustration',
          image: '/avatar-results/styles/flat-style.png'
        },
        {
          name: '3D rendered',
          label: '3D rendered',
          image: '/avatar-results/styles/3d-rendered.png'
        },
        {
          name: 'comic book art style',
          label: 'Comic book',
          image: '/avatar-results/styles/comic-book.png'
        },
        // {
        //   name: "pixel art style",
        //   label: "Pixel art",
        //   image: "/avatar-results/styles/pixel-art.png",
        // },
        {
          name: 'anime style',
          label: 'Anime',
          image: '/avatar-results/styles/anime.png'
        },
        {
          name: 'chibi style',
          label: 'Chibi Cartoon',
          image: '/avatar-results/styles/chibi.png'
        }
      ]
    }
  }
}
</script>
