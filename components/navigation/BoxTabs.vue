<template>
  <div>
    <!-- Mobile dropdown -->
    <div class="sm:hidden">
      <label for="tabs" class="sr-only">Select a tab</label>
      <select
        id="tabs"
        name="tabs"
        class="block w-full px-2 py-1.5 border border-black/10 shadow-sm rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
        :value="activeItem"
        @change="handleClick($event.target.value)"
      >
        <option
          v-for="item in items"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </option>
      </select>
    </div>

    <!-- Desktop tabs -->
    <div class="hidden sm:block">
      <nav class="isolate flex divide-x divide-gray-200 rounded-lg shadow" aria-label="Tabs">
        <button
          v-for="(item, index) in items"
          :key="item.value"
          :class="[
            activeItem === item.value ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700',
            index === 0 ? 'rounded-l-lg' : '',
            index === items.length - 1 ? 'rounded-r-lg' : '',
            'group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-2.5 text-center text-sm font-medium hover:bg-gray-50 focus:z-10'
          ]"
          @click="handleClick(item.value)"
        >
          <span>{{ item.label }}</span>
          <span
            aria-hidden="true"
            :class="[
              activeItem === item.value ? 'bg-primary-500' : 'bg-transparent',
              'absolute inset-x-0 bottom-0 h-0.5'
            ]"
          />
        </button>
      </nav>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,
      validator: value => value.every(item => 'label' in item && 'value' in item)
    },
    initialActive: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      activeItem: this.initialActive
    }
  },
  methods: {
    handleClick (value) {
      this.activeItem = value
      this.$emit('selected', value)
    }
  }
}
</script>
