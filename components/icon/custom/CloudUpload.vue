<template>
  <div class="-space-y-5">
    <div class="flex items-end -space-x-6">
      <div class="w-[51px] h-[51px] rounded-full bg-gradient-to-br from-[#D0D5DD] to-40% to-white" />
      <div class="w-16 h-16 rounded-full bg-gradient-to-br from-[#D0D5DD] to-40% to-white" />
      <div class="w-[51px] h-[51px] rounded-full bg-gradient-to-br from-[#D0D5DD] to-40% to-white" />
    </div>
    <div class="flex items-center justify-center">
      <div :class="`w-12 h-12 rounded-full bg-[#34405466] flex items-center justify-center relative ${circleClass}`">
        <IconCloudArrowUp class="w-6 h-6 text-white" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    circleClass: {
      type: String,
      default: ''
    }
  }
}
</script>
