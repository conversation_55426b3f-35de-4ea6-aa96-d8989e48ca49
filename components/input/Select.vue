<template>
  <div class="" :class="{ dark: darkMode, light: !darkMode }">
    <div class="flex content-center items-center justify-between mb-1">
      <label
        v-if="label"
        for="email"
        class="block text-sm font-semibold leading-5"
        :class="{
          'text-primary-500': !disabled,
          'text-gray-400': disabled,
          'text-white/90': darkMode,
          'text-gray-700': !darkMode
        }"
      >
        {{ label }}
      </label>
      <slot />
    </div>
    <div class="select-wrapper relative rounded-md shadow-sm">
      <select
        :id="id"
        :value="value"
        class="block w-full pl-3 pr-8 pt-2 pb-2.5 bg-white border rounded-lg border-gray-300 placeholder-gray-400 focus:outline-none focus:border-teal-500 focus:ring-teal-500 sm:text-sm focus:ring-1 overflow-x-auto"
        @input="$emit('input', $event.target.value)"
      >
        <option :key="'placholder'" value="" disabled selected hidden>
          {{ placeholder }}
        </option>
        <option
          v-for="(option, index) in options"
          :key="index"
          :value="option.value || option"
          :selected="value === (option.value || option)"
        >
          {{ option.title || option }}
        </option>
      </select>
    </div>
    <span v-if="description" class="text-xs text-gray-700" :class="{ 'text-black/70': darkMode, 'text-gray-600': !darkMode }">
      {{ description }}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    darkMode: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: 'Select your option'
    },
    id: {
      type: String,
      default: ''
    },
    value: {
      type: [Number, String, Boolean],
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default () {
        return []
      }
    }
  }
}
</script>

<style scoped>
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: none;
}

.select-wrapper::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #6b7280;
  pointer-events: none;
}

.select-wrapper:hover::after {
  border-top-color: #374151;
}
</style>

<i18n>
  {
    "en": {
      "Select your option": "Select your option"
    },
    "es": {
      "Select your option": "Selecciona una opción"
    },
    "de": {
      "Select your option": "Option auswählen"
    }
  }
</i18n>
