<template>
  <div class="">
    <div class="flex items-center content-center justify-between">
      <label
        v-if="label"
        for="email"
        class="block text-sm font-semibold leading-5"
        :class="{
          'text-primary-500': !disabled,
          'text-gray-400': disabled,
          'text-white/90': darkMode,
          'text-gray-700': !darkMode
        }"
      >
        {{ label }}
      </label>
      <span v-if="maxLength && maxLength < 1000" class="text-xs" :class="{ 'text-white/70': darkMode, 'text-gray-700': !darkMode }">{{ currentLength }} of {{ maxLength }}</span>
    </div>
    <p v-if="description" class="text-xs leading-[13px] mt-0.5 mb-2" :class="{ 'text-white/70': darkMode, 'text-gray-700': !darkMode }">
      {{ description }}
    </p>
    <div class="relative mt-1 rounded-md shadow-sm">
      <textarea
        :id="id"
        :autocomplete="autocomplete"
        :rows="rows"
        :value="value"
        class="block w-full p-2 form-input sm:leading-5 border px-3 pt-2 pb-2.5 bg-white rounded-lg border-gray-300 placeholder-gray-400 focus:outline-none focus:border-teal-500 focus:ring-teal-500 sm:text-sm focus:ring-1"
        :class="{
          'border-red-500 focus:border-red-500': error,
          'border-black/20': !error,
          'opacity-50': disabled,
          'opacity-100': !disabled,
          'border-white/20 bg-white/10 text-white': darkMode,
          'bg-dark/10 border-black/20 text-black': !darkMode,
          [textareaClass]: !!textareaClass
        }"
        :placeholder="placeholder"
        :maxlength="maxLength"
        @input="$emit('input', $event.target.value)"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    darkMode: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    rows: {
      type: Number,
      default: 5
    },
    maxLength: {
      type: Number,
      default: 9999999
    },
    error: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    autocomplete: {
      type: String,
      default: 'off'
    },
    textareaClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    currentLength () {
      if (!this.value || this.value === null) {
        return 0
      } else {
        return this.value.length
      }
    }
  }
}
</script>

<style></style>
