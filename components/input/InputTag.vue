<template>
  <div class="w-full mb-2 flex flex-col">
    <label v-if="label" :for="id" class="block text-sm font-semibold leading-5 text-gray-700">{{ label }}</label>
    <div v-if="description" class="text-xs text-gray-700 mb-2">
      {{ description }}
    </div>
    <div class="space-y-2 my-2">
      <input
        type="text"
        placeholder="Enter a Tag"
        class="w-full p-2 text-sm border border-white rounded bg-gray-100 border-opacity-20"
        @keydown.enter="addTag"
        @keydown.188="addTag"
        @keydown.delete="removeLastTag"
      >
      <div v-for="(tag, index) in tags" :key="index" class="tag-input__tag">
        <span @click="removeTag(index)">x</span>
        <template v-if="tag.title">
          {{ tag.title }}
        </template>
        <template v-else>
          {{ tag }}
        </template>
      </div>
    </div>
    <p v-if="tags && tags.length < 1" class="mt-1 text-xs text-gray-700">
      Press enter to add a tag
    </p>
  </div>
</template>
<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: () => `inputtag-${uuid()}`
    },
    description: {
      type: String,
      default: ''
    },
    tags: {
      type: Array,
      required: true
    }
  },
  watch: {
    currentTags (newValue, oldValue) {
      this.$emit('update', this.tags)
    }
  },
  methods: {
    addTag (event) {
      event.preventDefault()
      const val = event.target.value.trim()
      if (val.length > 0) {
        this.$emit('update', [...this.tags, val])
        event.target.value = ''
      }
    },
    removeTag (index) {
      const newTags = this.tags.filter((item, i) => {
        return index !== i
      })
      this.$emit('update', newTags)
    },
    removeLastTag (event) {
      if (event.target.value.length === 0) {
        this.removeTag(this.tags.length - 1)
      }
    },

    updateTag () {
      this.$emit('update', this.tags)
    }
  }
}
</script>
<style scoped>
.tag-input {
  width: 100%;
  border: 1px solid #eee;
  font-size: 0.9em;
  box-sizing: border-box;
  padding: 0 10px;
}

.tag-input__tag {
  height: 30px;
  font-size: 11px;
  float: left;
  margin-right: 10px;
  margin-top: 10px;
  line-height: 30px;
  padding: 0 5px;
  border-radius: 5px;
  @apply bg-gray-100;
}

.tag-input__tag > span {
  cursor: pointer;
  opacity: 0.75;
}

.tag-input__text {
  border: none;
  outline: none;
  font-size: 0.9em;
  line-height: 50px;
  background: none;
}
</style>
