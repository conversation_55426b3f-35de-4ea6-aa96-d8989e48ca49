<template>
  <div class="">
    <label v-if="label" for="email" class="block text-sm font-medium leading-5 text-gray-700">{{ label }}</label>
    <div class="relative rounded-md shadow-sm">
      <svg
        width="22"
        height="22"
        class="absolute top-0 left-0 mt-3 ml-3 text-gray-700 fill-current"
        version="1.1"
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="m45.031 70.832c5.7227 0 11.266-1.8438 15.84-5.2109l15.66 16.066 4.2969-4.1875-15.555-15.961c4.1445-4.8164 6.4727-10.984 6.4727-17.418 0-14.73-11.98-26.711-26.711-26.711-14.73 0-26.715 11.98-26.715 26.711 0 14.727 11.984 26.711 26.711 26.711zm0-47.422c11.422 0 20.711 9.2891 20.711 20.711 0 5.9844-2.5898 11.676-7.1016 15.613-3.7695 3.2891-8.6016 5.0977-13.609 5.0977-11.422 0-20.711-9.2891-20.711-20.711s9.293-20.711 20.711-20.711z" />
      </svg>
      <input
        :id="id"
        :value="value"
        class="block w-full p-2 py-3 pl-9 text-sm leading-5 border border-gray-300 rounded form-input"
        :placeholder="placeholder"
        @input="$emit('input', $event.target.value)"
      >
    </div>
  </div>
</template>

<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: () => `search-${uuid()}`
    },
    value: {
      type: String,
      default: ''
    }
  }

}
</script>

<style>

</style>
