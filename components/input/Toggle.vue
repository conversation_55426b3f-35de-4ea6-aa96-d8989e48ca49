<template>
  <div class="flex items-center">
    <!-- Enabled: "bg-indigo-600", Not Enabled: "bg-gray-200" -->
    <button
      :id="id"
      type="button"
      :class="{ 'bg-brand-500': active, 'bg-black/10': !active }"
      class="relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
      role="switch"
      aria-checked="false"
      aria-labelledby="annual-billing-label"
      @click="$emit('change')"
    >
      <!-- Enabled: "translate-x-5", Not Enabled: "translate-x-0" -->
      <span aria-hidden="true" :class="{ 'translate-x-4': active, 'translate-x-0': !active }" class="pointer-events-none inline-block h-4 w-4 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out" />
    </button>
    <div class="ml-2 flex items-center justify-start space-x-2" @click="$emit('change')">
      <slot />
    </div>
  </div>
</template>

<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    active: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: () => `toggle-${uuid()}`
    }
  }
}
</script>

<style></style>
