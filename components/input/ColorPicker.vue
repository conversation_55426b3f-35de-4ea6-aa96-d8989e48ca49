<template>
  <div class="flex flex-col gap-4">
    <div
      ref="picker"
      class="relative w-full h-48 rounded-lg cursor-pointer"
      @mousedown="startPicking"
      @mousemove="pickColor"
      @mouseup="stopPicking"
      @mouseleave="stopPicking"
    >
      <!-- Rainbow gradient background -->
      <div class="absolute inset-0 rounded-lg" :style="gradientStyle" />

      <!-- Color picker circle -->
      <div
        ref="pickerCircle"
        class="absolute w-6 h-6 -ml-3 -mt-3 border-2 border-white rounded-full shadow-lg"
        :style="{
          left: position.x + 'px',
          top: position.y + 'px',
          backgroundColor: selectedColor
        }"
      />
    </div>

    <!-- Selected color hex code -->
    <div class="flex items-center gap-2">
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700">Selected color hex code</label>
        <div class="mt-1 flex rounded-md shadow-sm">
          <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
            #
          </span>
          <input
            v-model="hexCode"
            type="text"
            class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300 uppercase"
            pattern="[0-9A-Fa-f]{6}"
            @input="updateFromHex"
          >
        </div>
      </div>
      <ButtonWhite
        size="sm"
        class="mt-6 inline-flex py-2.5"
        @click="$emit('select', selectedColor)"
      >
        Select
      </ButtonWhite>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ColorPicker',
  props: {
    startColor: {
      type: String,
      default: '#000000'
    }
  },
  data () {
    return {
      isPicking: false,
      position: { x: 0, y: 0 },
      selectedColor: '#000000',
      hexCode: '000000',
      gradientStyle: {
        background: `
          linear-gradient(to bottom,
            rgba(0, 0, 0, 0) 50%,
            rgba(0, 0, 0, 1) 100%
          ),
          linear-gradient(to bottom,
            rgba(255, 255, 255, 1) 0%,
            rgba(255, 255, 255, 0) 50%
          ),
          linear-gradient(to right,
            #FF0000 0%,
            #FF00FF 17%,
            #0000FF 33%,
            #00FFFF 50%,
            #00FF00 67%,
            #FFFF00 83%,
            #FF0000 100%
          )
        `
      }
    }
  },
  watch: {
    hexCode (newVal) {
      // remove all non-hex characters
      if (newVal.match(/[^0-9A-Fa-f]/g)) {
        this.hexCode = newVal.replace(/[^0-9A-Fa-f]/g, '')
      }
    }
  },
  mounted () {
    this.selectedColor = this.startColor
    this.hexCode = this.startColor.substring(1)
    // Move pickerCircle to the start color
    const rect = this.$refs.picker.getBoundingClientRect()
    this.$refs.pickerCircle.style.left = `${rect.width / 2}px`
    this.$refs.pickerCircle.style.top = `${rect.height / 2}px`
    this.$refs.pickerCircle.style.backgroundColor = this.startColor
  },
  methods: {
    startPicking (event) {
      this.isPicking = true
      this.pickColor(event)
    },
    stopPicking () {
      this.isPicking = false
    },
    pickColor (event) {
      if (!this.isPicking) { return }

      const rect = this.$refs.picker.getBoundingClientRect()
      const x = Math.max(0, Math.min(event.clientX - rect.left, rect.width))
      const y = Math.max(0, Math.min(event.clientY - rect.top, rect.height))

      this.position = { x, y }

      // Create a canvas to sample the color
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = rect.width
      canvas.height = rect.height

      // Draw the gradient
      const gradient1 = ctx.createLinearGradient(0, 0, rect.width, 0)
      gradient1.addColorStop(0, '#FF0000')
      gradient1.addColorStop(0.17, '#FF00FF')
      gradient1.addColorStop(0.33, '#0000FF')
      gradient1.addColorStop(0.5, '#00FFFF')
      gradient1.addColorStop(0.67, '#00FF00')
      gradient1.addColorStop(0.83, '#FFFF00')
      gradient1.addColorStop(1, '#FF0000')

      ctx.fillStyle = gradient1
      ctx.fillRect(0, 0, rect.width, rect.height)

      // Add white-to-transparent gradient (vertical, top to middle)
      const whiteGradient = ctx.createLinearGradient(0, 0, 0, rect.height / 2)
      whiteGradient.addColorStop(0, 'rgba(255, 255, 255, 1)')
      whiteGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

      ctx.fillStyle = whiteGradient
      ctx.fillRect(0, 0, rect.width, rect.height / 2)

      // Add transparent-to-black gradient (vertical, middle to bottom)
      const blackGradient = ctx.createLinearGradient(0, rect.height / 2, 0, rect.height)
      blackGradient.addColorStop(0, 'rgba(0, 0, 0, 0)')
      blackGradient.addColorStop(1, 'rgba(0, 0, 0, 1)')

      ctx.fillStyle = blackGradient
      ctx.fillRect(0, rect.height / 2, rect.width, rect.height / 2)

      // Sample the color
      const pixel = ctx.getImageData(x, y, 1, 1).data
      this.selectedColor = `#${this.rgbToHex(pixel[0], pixel[1], pixel[2])}`
      this.hexCode = this.selectedColor.substring(1)
      this.$emit('select', this.selectedColor)
    },
    rgbToHex (r, g, b) {
      return ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()
    },
    updateFromHex () {
      if (/^[0-9A-Fa-f]{6}$/.test(this.hexCode)) {
        this.selectedColor = `#${this.hexCode}`
      }
    }
  }
}
</script>
