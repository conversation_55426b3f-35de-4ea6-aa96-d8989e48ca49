<template>
  <div class="bg-gray-50 p-2 rounded-md flex flex-col items-start justify-start border border-black/5 space-y-1">
    <div class="flex items-center justify-between w-full">
      <span v-if="label" class="text-sm text-gray-700">{{ label }}:</span>
      <span v-if="showValue" class="text-sm text-gray-700">{{ value }}</span>
    </div>
    <input
      :id="id"
      type="range"
      :min="parseInt(min)"
      :max="parseInt(max)"
      :value="parseInt(value)"
      :steps="steps"
      class="w-full"
      @input="$emit('input', $event.target.value)"
    >
  </div>
</template>

<script>
export default {
  props: {
    showValue: {
      type: Boolean,
      default: true
    },
    value: {
      type: Number,
      default: 0
    },
    min: { type: Number, default: 0 },
    max: { type: Number, default: 100 },
    steps: { type: Number, default: 1 },
    label: { type: String, default: null }
  },
  data () {
    return {
    }
  }

}
</script>

<style>

</style>
