<template>
  <div class="flex content-center items-center justify-start space-x-2">
    <input
      :id="id"
      v-model="modelValue"
      type="checkbox"
    >
    <label v-if="label" :for="id" class="text-left align-middle block text-sm font-medium leading-[16px]" :class="{'text-white/70': darkMode, 'text-gray-700':!darkMode}">{{ label }}</label>
  </div>
</template>

<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    darkMode: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: () => `checkbox-${uuid()}`
    },
    value: {
      type: Boolean,
      default: false
    },
    description: {
      type: String,
      default: ''
    }
  },
  computed: {
    modelValue: {
      get () {
        return this.value
      },
      set (value) {
        this.$emit('input', value)
      }
    }
  }
}
</script>

<style>

</style>
