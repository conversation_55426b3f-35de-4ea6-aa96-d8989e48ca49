<template>
  <div>
    <div class="mt-2 sm:col-span-2 sm:mt-0">
      <div class="relative flex max-w-lg justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
        <div class="text-center">
          <svg v-if="!file.name" class="mx-auto h-12 w-12 text-gray-700" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <svg v-if="file.name" class="mx-auto h-12 w-12 text-gray-700" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
          <template v-if="!file.name">
            <p class="mt-1 text-sm text-gray-700">
              <button type="button" class="text-brand-600 font-medium transition duration-150 ease-in-out hover:text-brand-500 focus:underline focus:outline-none">
                Upload a file
              </button>
              or drag and drop
            </p>
            <p class="mt-1 text-xs text-gray-500">
              CSV up to 10MB
            </p>
          </template>
          <template v-else>
            <p class="mt-1 text-sm text-gray-700">
              {{ file.name }}
            </p>
          </template>
        </div>
        <label :for="id" class="file-label" />
        <input
          :id="id"
          ref="file"
          type="file"
          :accept="fileFormat"
          class="file-input"
          name="file"
          :multiple="multiple"
          @change="setFileInfo"
        >
      </div>
    </div>
  </div>
</template>

<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    fileFormat: {
      type: String,
      required: false
    },
    id: {
      type: String,
      default: () => `file-${uuid()}`
    }
  },
  data () {
    return {
      file: {
        name: ''
      }
    }
  },
  methods: {
    setFileInfo (event) {
      const file = event.target.files[0]
      if (file) {
        this.file.name = file.name
        this.$emit('change', event)
      }
    }
  }
}
</script>

<style>
.file-input {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.file-label {
  width: 100%;
  height: 130px;
  cursor: pointer;
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  z-index: 999;

  @apply rounded-md;
}
</style>
