<template>
  <Popup @closeModal="$emit('closeModal')">
    <div class="space-y-3">
      <h3 class="text-xl font-bold leading-6 text-gray-900">
        <!-- <span v-if="reviewAreYouHappy !== false">Get $15 refunded instantly</span> -->
        <span v-if="reviewAreYouHappy === null">How was your experience?</span>
        <span v-if="reviewAreYouHappy === false">Get refunded instantly</span>
        <span v-if="reviewAreYouHappy === true">Leave a review (takes 30 seconds)</span>
      </h3>
      <div v-if="reviewAreYouHappy === null" class="text-sm text-gray-700 space-y-2">
        <p>We promise at least 1 profile-worthy headshot in every order. Your quick feedback here will be seen by our CEO.</p>
      </div>
      <div v-if="reviewAreYouHappy === true" class="text-sm text-gray-700 space-y-2">
        <p>Can you spare 1-2 sentences for a TrustPilot review? Reviews make a meaningful difference to us. The best ones are shared across our entire company.</p>
        <p>Your time spent won't be wasted 😉</p>
        <!-- <p><strong>Here's how you can help us:</strong></p>
        <ul class="space-y-1">
          <li>1. Go to Trustpilot and leave a review.</li>
          <li>2. Screenshot it and send it to <strong><a href="mailto:<EMAIL>" class="underline text-blue-500" target="_blank"><EMAIL></a></strong> </li>
        </ul>
        <p>P.S. Make sure to include <strong>{{ $store.state.user.uid }}</strong> in your email.</p> -->
      </div>
      <div class="mt-4">
        <template v-if="reviewAreYouHappy === null">
          <strong class="text-sm text-gray-700">Are you happy with your results?</strong>
          <div class="grid grid-cols-2 gap-4 w-full mt-2">
            <ButtonDark size="sm" @click="selectHappiness(true)">
              <span>Yes, I'm happy</span>
            </ButtonDark>
            <ButtonWhite size="sm" @click="selectHappiness(false)">
              <span>No, I'm not happy</span>
            </ButtonWhite>
          </div>
        </template>
      </div>
      <p v-if="reviewAreYouHappy === true" class="text-sm text-gray-700">
        Thank you,
      </p>
      <div v-if="reviewAreYouHappy === true" class="flex space-x-2 items-center justify-start">
        <img src="@/assets/img/headshot-danny.png" class="w-12 h-12 rounded-full">
        <div class="flex flex-col text-sm items-start justify-center ">
          <span class="font-bold">Danny Postma</span>
          <span>Founder and CEO of HeadshotPro</span>
        </div>
      </div>
      <template v-if="reviewAreYouHappy === false">
        <div class="space-y-4">
          <p class="text-sm text-gray-700">
            Sorry to hear you weren't happy with your results. No worries, we have a 14-day money back guarantee. Please contact <a href="mailto:<EMAIL>" class="underline text-blue-500"><EMAIL></a> for a full refund.
          </p>
          <a href="mailto:<EMAIL>" target="_blank" class="block">
            <ButtonPrimary size="sm">
              Request a full refund
            </ButtonPrimary>
          </a>
        </div>
      </template>
      <template v-if="reviewAreYouHappy === true">
        <LoadingWrapper :is-loading="!inviteLink" title="Creating your review link">
          <a :href="inviteLink" target="_blank" @click="trackReviewClick">
            <ButtonPrimary class="!bg-[#ff6600] hover:!bg-[#ff6600]/90 w-full">
              <span>Review HeadshotPro</span>
              <IconChevron class="w-3 h-3 text-white ml-1.5" />
            </ButtonPrimary>
          </a>
        </LoadingWrapper>
      </template>
      <button v-if="reviewAreYouHappy !== null" class="text-xs text-gray-500 underline" @click="reviewAreYouHappy = null">
        I changed my mind
      </button>
      <!-- <p class="text-xs text-gray-700">
        P.S. Make sure to include <strong>{{ $store.state.user.uid }}</strong> in your email.
      </p>
      <p v-if="reviewAreYouHappy !== false" class="text-xs text-gray-700">
        P.S.S. Disappointed in your results? Contact me now with the email above for priority customer support. On average, it takes us 5-10 business days to get back to customers who leave 1-star or 2-star reviews on TrustPilot.
      </p> -->
    </div>
  </Popup>
</template>

<script>
export default {
  props: {
    modelId: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      reviewAreYouHappy: null,
      inviteLink: null
    }
  },
  mounted () {
    this.$posthog.capture('$trustpilot:show_popup')
  },
  methods: {
    async fetchInviteUrl () {
      const response = await this.$axios.$post('/trustpilot/create-invite-url', {
        uid: this.$store.state.user.uid,
        modelId: this.modelId
      })
      if (response?.success && response.url) {
        this.inviteLink = response.url
      }
    },
    selectHappiness (value) {
      this.reviewAreYouHappy = value
      this.$posthog.capture('$trustpilot:select-happiness', {
        happy: value
      })
      if (value) {
        this.fetchInviteUrl()
      }
    },
    trackReviewClick () {
      this.$posthog.capture('$trustpilot:click-review-link')
    }
  }

}
</script>

<style>

</style>
