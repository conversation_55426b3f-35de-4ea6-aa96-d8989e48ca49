<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-start min-h-screen px-4 pt-4 pb-20 text-center md:items-end md:justify-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity cursor-pointer" @click="closeModal">
        <div class="absolute inset-0 bg-gray-500 opacity-75" />
      </div>

      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" />&#8203;

      <div :class="`overflow-y-scroll popup-modal relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle  w-full max-h-[calc(100vh-64px)] ${additionalClasses}`" :style="`max-width:${maxWidth}; padding:${padding}px;`" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
        <div class="w-4 h-4 bg-black rounded-full absolute top-2 right-2 flex items-center justify-center cursor-pointer" @click="closeModal">
          <IconCross class="w-3 h-3 text-white" />
        </div>
        <!-- <div class="absolute top-0 right-0 flex items-center mt-2 mr-2 h-7"> -->
        <!-- <button aria-label="Close panel" class="text-gray-600 transition duration-150 ease-in-out hover:text-gray-500" @click="closeModal">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button> -->
        <!-- </div> -->
        <div>
          <div class="text-left">
            <h3 v-if="title" id="modal-headline" class="text-base font-medium leading-6 text-gray-900">
              {{ title }}
            </h3>
            <div class="">
              <slot />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      required: false
    },
    padding: {
      type: Number,
      default: 24
    },
    size: {
      type: String,
      required: false,
      default: 'sm'
    },
    additionalClasses: {
      type: String,
      default: ''
    }
  },
  computed: {
    windowWidth () {
      return window.innerWidth
    },
    maxWidth () {
      if (!process.client) { return '768px' }
      if (this.windowWidth < 768) { return '90vw' }

      const { size } = this
      if (size === 'xs') { return '20rem' } else if (size === 'sm') { return '24rem' } else if (size === 'md') { return '28rem' } else if (size === 'lg') { return '32rem' } else if (size === 'xl') { return '36rem' } else if (size === '2xl') { return '42rem' } else if (size === '3xl') { return '48rem' } else if (size === '4xl') { return '56rem' } else if (size === '5xl') { return '64rem' } else if (size === '6xl') { return '72rem' } else if (size === '7xl') { return '80rem' } else if (size === 'full') { return '98%' }
      return '100%'
    }
  },
  methods: {
    closeModal () {
      document.body.style.overflow = 'auto'
      this.$emit('closeModal')
      this.$emit('close')
    }
  }
}
</script>
