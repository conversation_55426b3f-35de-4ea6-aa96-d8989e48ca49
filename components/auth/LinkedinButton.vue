<template>
  <div class="w-full relative">
    <AuthLastUsedTooltip provider="linkedin" />
    <template v-if="isBannedUserAgent">
      <div class="text-left flex mb-2 p-1 rounded-md bg-orange-100">
        <IconExclamation class="w-4 h-4 mr-0.5 text-orange-500" /><span class="text-xs text-gray-700">LinkedIn Auth only available in native browser.</span>
      </div>
      <button class="opacity-50 w-full h-30 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-white/20 bg-[#173E64] px-6 py-3 text-center text-base font-medium text-white shadow-sm hover:bg-[#3B63B6] focus:outline-none focus:ring-2 focus:ring-offset-2">
        <div class="mr-1.5 flex h-6 w-6 items-center justify-center rounded bg-white">
          <IconSolidLinkedIn class="w-3.5 h-3.5" />
        </div>
        <span>Continue with LinkedIn</span>
      </button>
    </template>
    <template v-else>
      <button class="w-full h-30 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-white/20 bg-[#173E64] px-6 py-3 text-center text-base font-medium text-white shadow-sm hover:bg-[#3B63B6] focus:outline-none focus:ring-2 focus:ring-offset-2" @click="redirect">
        <div class="mr-1.5 flex h-6 w-6 items-center justify-center rounded bg-white">
          <IconSolidLinkedIn class="w-3.5 h-3.5" />
        </div>
        <span>Continue with LinkedIn</span>
      </button>
    </template>
  </div>
</template>

<script>
export default {
  computed: {
    isBannedUserAgent () {
      // Social Authentication doesn't work in TikTok, Instagram, Twitter, etc, so we hide the option on there.
      if (!process.client) { return false }
      const bannedAgents = ['TikTok', 'musical_ly', 'Bytedance', 'Instagram', 'Twitter']
      const onlyOnDevices = ['iPhone', 'iPad', 'iPod']
      return bannedAgents.some(agent => navigator.userAgent.includes(agent)) && onlyOnDevices.some(device => navigator.userAgent.includes(device))
    }
  },
  methods: {
    redirect () {
      const url = process.env.SERVER_URL + '/auth/linkedin/redirect'
      window.location.href = url
    }
  }
}
</script>

  <style scoped>
  @import url("https://fonts.googleapis.com/css2?family=Roboto&display=swap");
  span {
    font-family: "Roboto", sans-serif;
  }
  </style>
