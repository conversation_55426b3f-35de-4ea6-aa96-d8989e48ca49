<template>
  <LoadingWrapper :is-loading="isSubmitting" title="Creating your account...">
    <form class="space-y-3" @submit.prevent="handleLogin">
      <div class="space-y-5">
        <!-- <Input v-model="name" type="text" placeholder="Enter your name" label="Name" /> -->
        <Input v-model="email" type="email" placeholder="Enter your email address" label="Email" />
        <div class="grid grid-cols-2 gap-4">
          <Input v-model="password" type="password" placeholder="Enter your password" label="Password" />
          <Input v-model="passwordRepeat" type="password" placeholder="Enter your password" label="Repeat password" />
        </div>

        <!-- <div class="flex content-center items-start justify-start space-x-2">
          <input id="tos" v-model="tos" type="checkbox" class="mt-1">
          <label for="tos" class="block mb-1 text-sm font-medium leading-5 text-gray-700">
            I agree to the
            <a href="/legal/terms-and-conditions" target="_blank" class="underline ">terms & conditions</a> and
            <a href="/legal/privacy-policy" target="_blank" class="underline ">privacy policy</a>.
          </label>
        </div> -->

        <div v-if="!isSubmitting">
          <button type="submit" class="inline-flex w-full items-center justify-center rounded-md border border-transparent bg-brand-500 px-6 py-4 text-sm font-bold text-white transition-all duration-200 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2">
            Create account
          </button>
        </div>

        <LoadingSpinner v-else :title="loadingMessage" class="text-black" />
        <p class="mt-4 text-xs text-gray-400">
          When creating a new account, you agree to the
          <a href="/legal/terms-and-conditions" target="_blank" class="underline">terms & conditions</a>
          and
          <a href="/legal/privacy-policy" target="_blank" class="underline">privacy policy</a>
          .
        </p>
        <p v-if="hasError" class="rounded border border-red-100 bg-red-50 p-2 text-sm text-red-500">
          {{ errorMessage }}
        </p>
      </div>
    </form>
  </LoadingWrapper>
</template>

<script>
export default {
  name: 'LoginForm',
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      email: '',
      password: '',
      name: '',
      passwordRepeat: '',
      tos: true,
      isSubmitting: false,
      hasError: false,
      isLoading: false,
      acceptTerms: false,
      loadingMessage: 'Creating your account',
      errorMessage: 'Signup failed. Have you already signed up?'
    }
  },
  methods: {
    async sendVerificationEmail (userEmail) {
      return await this.$axios.$post('/auth/send-custom-verification-email', {
        userEmail
      })
    },
    async handleLogin () {
      if (!this.tos) {
        return this.$toast.open({ type: 'error', message: 'Please accept the legal documents.' })
      }
      if (!this.email || this.email.length < 5) {
        return this.$toast.open({ type: 'error', message: 'Please fill in a correct email.' })
      }
      if (this.password !== this.passwordRepeat) {
        return this.$toast.open({ type: 'error', message: 'Passwords do not match.' })
      }

      // const { isValidEmail } = await this.$axios.$post('/user/validate/email', { email: this.email })
      // if (!isValidEmail) { return this.$toast.open({ type: 'error', message: 'Please fill in a valid email.' }) }
      this.isSubmitting = true
      try {
        const credentials = await this.$fire.auth.createUserWithEmailAndPassword(this.email, this.password)

        if (!credentials || credentials.length === 0) {
          throw new Error('Something went wrong. Please try again.')
        }
        // await credentials.user?.sendEmailVerification()
        // await this.sendVerificationEmail(this.email)
        await credentials.user.updateProfile({
          displayName: this.name
        })

        await this.createDatabaseAccount(credentials.user)
      } catch (e) {
        this.$toast.open({ type: 'error', message: e.message })
        console.error('error', e.code, e.message)
        this.isSubmitting = false
        this.hasError = true
        this.errorMessage = e.message
      }
    }
  }
}
</script>

<style></style>
