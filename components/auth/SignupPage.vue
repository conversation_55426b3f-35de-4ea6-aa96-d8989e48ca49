<template>

<AuthPageWrapper>
    <p class="text-[#21B8BA] text-sm uppercase mt-10 font-medium">
      {{ $store.state.stats.photos.split(',')[0] }}M+ headshots already created
    </p>
    <h1 class="text-xl font-medium mt-4 text-primary-500">
      Professional business headshots, without physical photo shoot
    </h1>
    <p class="mt-4 text-sm text-black opacity-70">
      Create an account or log in with an existing one to continue.
    </p>

    <AuthLastUsedIndicator class="mt-4" />

    <div class="py-4">
      <LoadingWrapper :is-loading="isLoading" :title="loadingText">
        <div class="relative">
          <AuthLastUsedTooltip provider="email" />
          <nuxt-link :to="$route.query && $route.query.redirect ? '/auth/email?redirect=' + $route.query.redirect : '/auth/email'">
            <ButtonWhite class="w-full bg-gray-100">
              <IconSolidEnveloppe class="mr-1.5 h-4 w-4 text-gray-700" />
              <span>Continue with email</span>
            </ButtonWhite>
          </nuxt-link>
        </div>
        <AuthGoogleButton class="w-full mt-2" />
        <AuthLinkedinButton class="w-full mt-2" />
        <p class="mt-4 text-[11px] text-slate-500">
          New accounts are subject to our
          <a href="/legal/terms-and-conditions" target="_blank" class="underline">terms & conditions</a>
          and
          <a href="/legal/privacy-policy" target="_blank" class="underline">privacy policy</a>
          .
        </p>
      </LoadingWrapper>
    </div>

    <div class="flex flex-col my-6 space-y-2.5">
      <div class="flex items-center justify-start">
        <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
        <span class="text-xs text-slate-500">14 days <strong class="text-slate-700">money back guaranteed</strong></span>
      </div>
      <div class="flex items-center justify-start">
        <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
        <span class="text-xs text-slate-500"><strong class="text-slate-700">Two hours done</strong>, fast turn around</span>
      </div>
      <div class="flex items-center justify-start">
        <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
        <span class="text-xs text-slate-500">Founded in Holland. <strong class="text-slate-700">We respect your privacy</strong></span>
      </div>
      <div class="flex items-center justify-start">
        <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
        <span class="text-xs text-slate-500"><strong class="text-slate-700">{{ happyCustomers }}</strong> happy customers</span>
      </div>
    </div>

    <div class="mt-12">
      <div class="flex items-center justify-center flex-col gap-3 lg:items-start">
        <span class="text-xs text-gray-600 flex-shrink-0 text-center">As seen on: </span>
        <div class="flex items-center justify-between space-x-2 lg:justify-start">
          <img src="@/assets/img/logo-1.png" class="w-[40px] md:w-[40px] grayscale">
          <img src="@/assets/img/logo-5.png" class="w-1/5 md:w-[75px] grayscale">
          <img src="@/assets/img/logo-2.png" class="w-[40px] md:w-[40px] grayscale">
          <img src="@/assets/img/logo-3.png" class="w-1/5 md:w-[75px] grayscale">
          <img src="@/assets/img/logo-4.png" class="w-1/5 md:w-[75px] grayscale">
        </div>
      </div>
      <!-- <p class="text-sm font-medium text-gray-900">
          Already have an account? <a href="#" title="" class="font-bold text-brand-500 hover:underline">Login now</a>
        </p> -->
    </div>
  </AuthPageWrapper>
</template>

<script>
export default {
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      required: true
    }
  },
  computed: {
    happyCustomers () {
      return Math.round(this.$store.state.stats.users.replace(/,/g, '') / 1000) + ',000+'
    }
  }
}
</script>

  <style>

  </style>
