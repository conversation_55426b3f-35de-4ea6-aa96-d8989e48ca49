<template>
  <Card class="relative overflow-hidden" @click="$emit('click')">
    <div class="space-y-[12px] flex flex-col">
      <span class="text-teal-500 uppercase text-[12px] leading-[20px] tracking-[-0.4%]">Step {{ step }}</span>
      <H6>{{ title }}</H6>
      <Paragraph size="md" :class="!isActive ? 'hidden md:block' : ''">
        {{ description }}
      </Paragraph>
    </div>
    <!-- Progress Bar -->
    <div v-if="isActive" class="absolute bottom-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-700">
      <div
        class="h-1 bg-teal-500 transition-width duration-100 ease-linear"
        :style="{ width: progress + '%' }"
      />
    </div>
  </Card>
</template>

<script>
import H6 from '@/components/landingpage/common/H6.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'

export default {
  components: {
    H6,
    Paragraph
  },
  props: {
    step: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style scoped>
/* Add any component-specific styles here */
.transition-width {
  transition-property: width;
}
</style>
