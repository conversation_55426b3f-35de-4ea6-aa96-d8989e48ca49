<template>
  <LandingpageCommonSection id="pricing" class="bg-muted-50">
    <LandingpageCommonTitleWrapper class="text-center items-center justify-center mx-auto max-w-2xl">
      <TrustpilotRatingSmall />
      <H2>
        {{ $t('Calculate your company\'s discount') }}
      </H2>
      <Paragraph size="lg">
        {{ $t('Affordable pricing with volume discounts. See how cost-effective professional, consistent headshots can be for your entire organization.') }}
      </Paragraph>
    </LandingpageCommonTitleWrapper>
    <client-only>
      <PricingBlock :key="currencyTrigger" @showSalesModal="$emit('showSalesModal')" />
    </client-only>
    <!-- <div class="flex flex-col gap-8 justify-center items-center">
      <H4>
        Pay the way you want. We are flexible.
      </H4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-[24px] max-w-4xl mx-auto">
        <div v-for="item in paymentItems" :key="item.title" class="flex flex-col items-center justify-center gap-[16px] text-center">
          <IconSquare :icon="item.icon" :color="item.color" />
          <H6>
            {{ item.title }}
          </H6>
          <Paragraph size="sm">
            {{ item.description }}
          </Paragraph>
        </div>
      </div>
    </div> -->
    <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
  </LandingpageCommonSection>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import H2 from '@/components/landingpage/common/H2.vue'
import PricingBlock from '@/components/landingpage/b2b/PricingBlock.vue'
// import H4 from '@/components/landingpage/common/H4.vue'
// import H6 from '@/components/landingpage/common/H6.vue'
// import IconSquare from '@/components/landingpage/common/IconSquare.vue'

export default {
  components: {
    Paragraph,
    H2,
    PricingBlock
    // H4,
    // H6,
    // IconSquare
  },
  data () {
    return {
      currencyTrigger: 0,
      paymentItems: [
        {
          title: 'Pay up front with bulk discount',
          description: 'Pre-purchase credits for your team and get up to 50% off your purchase',
          icon: 'IconMiniReceipt',
          color: '#64CDCF'
        },
        {
          title: 'Pay by invoice (Enterprise)',
          description: "Only pay what you need. We'll send an invoice at the end of the month.",
          icon: 'IconMiniDocumentCurrency',
          color: '#64CDCF'
        },
        {
          title: 'Pay by usage (Soon)',
          description: 'Automatically get charged once your credits run out.',
          icon: 'IconMiniBarChart',
          color: '#64CDCF'
        }
      ],
      cardItems: [
        {
          title: 'Easily onboard new hires',
          description: 'Seamlessly sync professional headshots across your HR, CRM, and internal platforms.',
          image: require('@/assets/img/b2b/use-case-1.png')
        },
        {
          title: 'Uniform headshots',
          description: 'Ensure brand consistency across Slack, LinkedIn, and email signatures',
          image: require('@/assets/img/b2b/use-case-2.png')
        },
        {
          title: 'Website redesign',
          description: 'Create professional \'About Us\' pages and internal directories easily.',
          image: require('@/assets/img/b2b/use-case-3.png')
        },
        {
          title: 'Corporate gifts',
          description: 'Stand out with a high-tech corporate gift your clients will actually use.',
          image: require('@/assets/img/b2b/use-case-4.png')
        },
        {
          title: 'Conference & events',
          description: 'Present a unified, professional look for speaker bios and event materials.',
          image: require('@/assets/img/b2b/use-case-5.png')
        }
      ]
    }
  },
  mounted () {
    this.$root.$on('currency-changed', this.handleCurrencyChange)
  },
  beforeDestroy () {
    this.$root.$off('currency-changed', this.handleCurrencyChange)
  },
  methods: {
    handleCurrencyChange () {
      this.currencyTrigger += 1
      this.$forceUpdate()
      this.$nextTick(() => {
        this.$children.forEach((child) => {
          if (child.$forceUpdate) {
            child.$forceUpdate()
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.card{
  @apply p-4
}
</style>

<i18n>
  {
    "en": {
      "Calculate your company's discount": "Calculate your company's discount",
      "Affordable pricing with volume discounts. See how cost-effective professional, consistent headshots can be for your entire organization.": "Affordable pricing with volume discounts. See how cost-effective professional, consistent headshots can be for your entire organization."
    },
    "es": {
      "Calculate your company's discount": "Calcula el descuento de tu empresa",
      "Affordable pricing with volume discounts. See how cost-effective professional, consistent headshots can be for your entire organization.": "Precios asequibles con descuentos por volumen. Ve lo rentables que pueden ser las fotos profesionales y consistentes para toda tu organización."
    },
    "de": {
      "Calculate your company's discount": "Berechne den Rabatt deines Unternehmens",
      "Affordable pricing with volume discounts. See how cost-effective professional, consistent headshots can be for your entire organization.": "Erschwingliche Preise mit Mengenrabatten. Sieh, wie kosteneffizient professionelle, einheitliche Bewerbungsfotos für deine gesamte Organisation sein können."
    }
  }
</i18n>
