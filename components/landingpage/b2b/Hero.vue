<template>
  <LandingpageCommonSection id="hero">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Left Column: Text Content -->
      <div class="space-y-6">
        <div class="bg-green-500/10 p-2 w-fit rounded-lg">
          <TrustpilotRatingSmall />
        </div>
        <H1>
          {{ $t('Get uniform headshots for your entire team') }}
        </H1>
        <p class="text-lg text-gray-600">
          {{ $t('Create professional, matching headshots across departments, locations, and time zones. It only takes 10 minutes to set up.') }}
        </p>
        <ul class="space-y-3 text-lg text-gray-700">
          <LandingpageCommonChecklistItem>{{ $t('Simple admin dashboard for overseeing photoshoots of all sizes.') }}</LandingpageCommonChecklistItem>
          <LandingpageCommonChecklistItem>{{ $t('Pick your own company approved backdrops and clothing styles') }}</LandingpageCommonChecklistItem>
          <LandingpageCommonChecklistItem>{{ $t('Achieve perfect brand consistency on your website, Slack, LinkedIn & more.') }}</LandingpageCommonChecklistItem>
        </ul>
        <div class="flex flex-col sm:flex-row gap-4 pt-4">
          <nuxt-link to="/app/add" class="w-full md:w-auto">
            <ButtonOrange size="base" class="w-full md:w-auto">
              <span class="flex items-center justify-center">
                {{ $t('Create your organization') }}
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" /></svg>
              </span>
            </ButtonOrange>
          </nuxt-link>
          <ButtonWhite size="base" class="w-full md:w-auto" @click="$emit('showSalesModal')">
            <span class="flex items-center justify-center space-x-2">
              <img class="size-6 rounded-full" src="@/assets/img/headshot-danny.png">
              <span>{{ $t('Contact sales') }}</span>
            </span>
          </ButtonWhite>
        </div>
      </div>

      <!-- Right Column: Image/Demo -->
      <div class="relative">
        <!-- Trustpilot Rating -->
        <!-- <div class="mb-4 flex items-center justify-center lg:justify-start">
          <TrustpilotRatingSmall />
        </div> -->

        <!-- Product Image Placeholder -->
        <VideoPlayer
          src="https://cdn2.headshotpror2.com/demo-b2b-compressed.mp4"
          :placeholder="require('@/assets/img/b2b/app-placeholder.jpg')"
          :autoplay="true"
          :markers="[{ time: 15, label: $t('Onboarding') }, { time: 236, label: $t('Dashboard') }, { time: 538, label: $t('For your team') }]"
        />
      </div>
    </div>
    <!-- Logo Cloud -->
    <LandingpageCommonLogoCloud />

    <!-- Demo Video Modal -->
    <portal to="modal">
      <Modal v-if="showDemoModal" max-width="sm:max-w-5xl" @close="closeDemoModal">
        <div class="p-6">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">
            {{ $t('B2B Demo Video') }}
          </h2>
          <VideoPlayer
            src="https://cdn2.headshotpror2.com/demo-b2b-compressed.mp4"
            :placeholder="require('@/assets/img/b2b/app-placeholder.jpg')"
            :autoplay="true"
            :markers="[{ time: 15, label: $t('Onboarding') }, { time: 236, label: $t('Dashboard') }, { time: 538, label: $t('For your team') }]"
          />
        </div>
      </Modal>
    </portal>
  </LandingpageCommonSection>
</template>

<script>
// Import the new components
import LandingpageCommonLogoCloud from '@/components/landingpage/common/LogoCloud.vue'
import LandingpageCommonChecklistItem from '@/components/landingpage/common/ChecklistItem.vue'
import VideoPlayer from '@/components/landingpage/common/VideoPlayer.vue'
import TrustpilotRatingSmall from '@/components/trustpilot/RatingSmall.vue'
import ButtonOrange from '@/components/button/Orange.vue'
import ButtonWhite from '@/components/button/White.vue'
import H1 from '@/components/landingpage/common/H1.vue'
import Modal from '@/components/modal/Modal.vue'
// import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    LandingpageCommonLogoCloud,
    LandingpageCommonChecklistItem,
    VideoPlayer,
    TrustpilotRatingSmall,
    ButtonOrange,
    ButtonWhite,
    H1,
    Modal
    // Paragraph
  },
  data () {
    return {
      showDemoModal: false,
      isMuted: true,
      isPlaying: false,
      duration: 0,
      currentTime: 0,
      showControls: false
    }
  },
  computed: {
    isMobile () {
      // Basic check for mobile devices
      if (typeof window !== 'undefined') {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      }
      return false
    }
  },
  mounted () {
    // Check for deep link query parameter
    this.checkForDemoDeepLink()

    const video = this.$refs.demoVideo
    if (video) {
      // Initial state sync
      this.isPlaying = !video.paused
      this.isMuted = video.muted

      // Event listeners for state changes
      video.addEventListener('play', () => { this.isPlaying = true })
      video.addEventListener('pause', () => { this.isPlaying = false })
      video.addEventListener('volumechange', () => { this.isMuted = video.muted })
      video.addEventListener('loadedmetadata', this.updateDuration)
      video.addEventListener('timeupdate', this.updateTime)

      // Set initial playing state based on autoplay
      // Autoplay might not work immediately or be blocked, listener handles actual state
      if (!this.isMobile) {
        video.play().catch(() => {
          // Handle potential autoplay block if needed
          this.isPlaying = false
        })
      }
    }
  },
  methods: {
    checkForDemoDeepLink () {
      // Check if ?event=show-demo is present in the URL
      if (this.$route.query.event === 'show-demo') {
        // Wait 1 second, then show the modal
        setTimeout(() => {
          this.showDemoModal = true
        }, 1000)
      }
    },
    closeDemoModal () {
      this.showDemoModal = false
      // Remove the query parameter from the URL
      if (this.$route.query.event === 'show-demo') {
        this.$router.replace({
          query: { ...this.$route.query, event: undefined }
        })
      }
    },
    togglePlay () {
      const video = this.$refs.demoVideo
      if (video) {
        if (video.paused) {
          video.play()
        } else {
          video.pause()
        }
      }
    },
    toggleMute () {
      const video = this.$refs.demoVideo
      if (video) {
        video.muted = !video.muted
      }
    },
    updateDuration () {
      const video = this.$refs.demoVideo
      if (video) {
        this.duration = video.duration
      }
    },
    updateTime () {
      const video = this.$refs.demoVideo
      if (video) {
        this.currentTime = video.currentTime
      }
    },
    seek (event) {
      const video = this.$refs.demoVideo
      if (video) {
        const time = parseFloat(event.target.value) // Ensure it's a number
        video.currentTime = time
        this.currentTime = time // Update reactive property immediately
      }
    },
    formatTime (timeInSeconds) {
      if (isNaN(timeInSeconds) || timeInSeconds === Infinity || timeInSeconds < 0) {
        return '0:00'
      }
      const minutes = Math.floor(timeInSeconds / 60)
      const seconds = Math.floor(timeInSeconds % 60).toString().padStart(2, '0')
      return `${minutes}:${seconds}`
    }
  }
  // No script logic needed for this static component based on the image
}
</script>

<i18n>
  {
    "en": {
      "Get uniform headshots for your entire team": "Get uniform headshots for your entire team",
      "Create professional, matching headshots across departments, locations, and time zones. It only takes 10 minutes to set up.": "Create professional, matching headshots across departments, locations, and time zones. It only takes 10 minutes to set up.",
      "Simple admin dashboard for overseeing photoshoots of all sizes.": "Simple admin dashboard for overseeing photoshoots of all sizes.",
      "Pick your own company approved backdrops and clothing styles": "Pick your own company approved backdrops and clothing styles",
      "Achieve perfect brand consistency on your website, Slack, LinkedIn & more.": "Achieve perfect brand consistency on your website, Slack, LinkedIn & more.",
      "Create your organization": "Create your organization",
      "Contact sales": "Contact sales",
      "Onboarding": "Onboarding",
      "Dashboard": "Dashboard",
      "For your team": "For your team",
      "B2B Demo Video": "B2B Demo Video"
    },
    "es": {
      "Get uniform headshots for your entire team": "Consigue fotos uniformes para todo tu equipo",
      "Create professional, matching headshots across departments, locations, and time zones. It only takes 10 minutes to set up.": "Crea fotos profesionales uniformes entre departamentos, ubicaciones y zonas horarias. Solo toma 10 minutos configurarlo.",
      "Simple admin dashboard for overseeing photoshoots of all sizes.": "Panel administrativo simple para supervisar sesiones fotográficas de cualquier tamaño.",
      "Pick your own company approved backdrops and clothing styles": "Elige fondos y estilos de ropa aprobados por tu empresa",
      "Achieve perfect brand consistency on your website, Slack, LinkedIn & more.": "Logra consistencia perfecta de marca en tu sitio web, Slack, LinkedIn y más.",
      "Create your organization": "Crear tu organización",
      "Contact sales": "Contactar ventas",
      "Onboarding": "Incorporación",
      "Dashboard": "Panel",
      "For your team": "Para tu equipo",
      "B2B Demo Video": "Video Demo B2B"
    },
    "de": {
      "Get uniform headshots for your entire team": "Einheitliche Bewerbungsfotos für dein gesamtes Team",
      "Create professional, matching headshots across departments, locations, and time zones. It only takes 10 minutes to set up.": "Erstelle professionelle, einheitliche Bewerbungsfotos über Abteilungen, Standorte und Zeitzonen hinweg. Die Einrichtung dauert nur 10 Minuten.",
      "Simple admin dashboard for overseeing photoshoots of all sizes.": "Einfaches Admin-Dashboard zur Verwaltung von Fotoshootings jeder Größe.",
      "Pick your own company approved backdrops and clothing styles": "Wähle deine firmeneigenen Hintergründe und Kleidungsstile",
      "Achieve perfect brand consistency on your website, Slack, LinkedIn & more.": "Erreiche perfekte Markenkonsistenz auf deiner Website, Slack, LinkedIn und mehr.",
      "Create your organization": "Organisation erstellen",
      "Contact sales": "Vertrieb kontaktieren",
      "Onboarding": "Einführung",
      "Dashboard": "Dashboard",
      "For your team": "Für dein Team",
      "B2B Demo Video": "B2B Demo Video"
    }
  }
</i18n>

<style scoped>
.gradient-background {
  /* Equivalent to bg-gradient-to-br from-[#75DBD8] to-[#61B7EB] bg-opacity-30 */
  background-image: linear-gradient(to bottom right, rgba(117, 219, 216, 0.3), rgba(97, 183, 235, 0.3));
}

.video-control-button {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px; /* Adjust size as needed */
  height: 32px; /* Adjust size as needed */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.video-control-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

/* Custom styling for the range input */
.video-progress-bar {
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  width: 100%; /* Full width */
  height: 6px; /* Progress bar height */
  background: rgba(255, 255, 255, 0.3); /* Track color */
  border-radius: 5px;
  cursor: pointer;
  outline: none;
}

/* Style for the thumb (the draggable part) in WebKit browsers (Chrome, Safari) */
.video-progress-bar::-webkit-slider-thumb {
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  width: 14px; /* Thumb width */
  height: 14px; /* Thumb height */
  background: #fff; /* Thumb color */
  border-radius: 50%;
  cursor: pointer;
}

/* Style for the thumb in Firefox */
.video-progress-bar::-moz-range-thumb {
  width: 14px; /* Thumb width */
  height: 14px; /* Thumb height */
  background: #fff; /* Thumb color */
  border-radius: 50%;
  cursor: pointer;
  border: none; /* Remove default border */
}

/* Style for the track in Firefox */
.video-progress-bar::-moz-range-track {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  cursor: pointer;
}
</style>
