<template>
  <LandingpageCommonSection id="team-headshots">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
      <div class="space-y-6 h-full flex flex-col items-start justify-center">
        <H2>
          {{ $t('Make studio quality headshots the new standard at your company.') }}
        </H2>
        <Paragraph size="lg">
          {{ $t('Outfit your entire team in cohesive professional headshots with matching clothing and backdrops.') }}
        </Paragraph>
        <div class="flex flex-col gap-4">
          <IconListItem
            icon="IconMiniSwatch"
            :title="$t('Extensive Style Library')"
            :description="$t('Choose from over 200+ different backdrops and outfits.')"
            color="#73D9D8"
          />
          <IconListItem
            icon="IconMiniCheckBadge"
            :title="$t('Branded Profile Pictures')"
            :description="$t('Automatically apply your company branding to profile pictures for 1:1 consistency.')"
            color="#6AC9E0"
          />
          <IconListItem
            icon="IconMiniPhoto"
            :title="$t('Choose from 80 Photos')"
            :description="$t('Receive 80 high-resolution headshots and 80 branded PFPs per team member.')"
            color="#64BAE9"
          />
        </div>
        <div class="flex flex-col md:flex-row gap-4 w-full">
          <nuxt-link to="/app/add" class="w-full md:w-auto">
            <ButtonPrimary class="w-full md:w-auto">
              <span>{{ $t('Create your organization now') }}</span>
              <IconChevron class="size-4 text-white" />
            </ButtonPrimary>
          </nuxt-link>
          <nuxt-link to="/backdrop-and-outfit" class="w-full md:w-auto">
            <ButtonWhite class="w-full md:w-auto">
              {{ $t('View all styles') }}
            </ButtonWhite>
          </nuxt-link>
        </div>
      </div>
      <div>
        <div class="flex flex-col" @mouseover="handleMouseOver" @mouseleave="handleMouseLeave">
          <div class="flex flex-row">
            <button v-for="example in Object.keys(examples)" :key="example" :class="currentExample === example ? 'active' : ''" class="example-button bg-white rounded-t text-sm px-3 py-1 text-black/60" @click="setExample(example)">
              <span class="capitalize">{{ $t(example) }}</span>
            </button>
          </div>
          <div class="bg-[#E9E9EC] p-2 rounded-b-lg rounded-tr-lg">
            <transition name="fade" mode="out-in">
              <div :key="currentExample" class="grid grid-cols-2 gap-4">
                <ImageDns v-for="(imageSrc, index) in examples[currentExample]" :key="index" class="rounded-md" :src="imageSrc" />
              </div>
            </transition>
          </div>
        </div>
      </div>
    </div>
  </LandingpageCommonSection>
</template>

<script>
import { defineComponent } from 'vue'
import H2 from '@/components/landingpage/common/H2.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import IconListItem from '@/components/landingpage/common/IconListItem.vue'

// TODO: Remove defineComponent once migrated to Vue 3.

export default defineComponent({
  name: 'TeamHeadshots',
  components: {
    H2,
    Paragraph,
    IconListItem
  },
  data () {
    return {
      currentExample: 'business',
      intervalId: null, // To store the interval ID
      isHovered: false, // To track hover state
      examples: {
        business: [
          require('@/assets/img/b2b/b2b-example-1.png'),
          require('@/assets/img/b2b/b2b-example-3.png'),
          require('@/assets/img/b2b/b2b-example-2.png'),
          require('@/assets/img/b2b/b2b-example-4.png')
        ],
        casual: [
          require('@/assets/img/b2b/b2b-example-casual-1.png'),
          require('@/assets/img/b2b/b2b-example-casual-2.png'),
          require('@/assets/img/b2b/b2b-example-casual-3.png'),
          require('@/assets/img/b2b/b2b-example-casual-4.png')
        ],
        custom: [
          require('@/assets/img/b2b/b2b-example-office-1.png'),
          require('@/assets/img/b2b/b2b-example-office-3.png'),
          require('@/assets/img/b2b/b2b-example-office-2.png'),
          require('@/assets/img/b2b/b2b-example-office-4.png')
        ]
      }
    }
  },
  mounted () {
    this.startAutoCycle() // Start the cycle when the component mounts
  },
  beforeDestroy () {
    this.stopAutoCycle() // Clear the interval when the component is destroyed
  },
  methods: {
    setExample (example) {
      this.stopAutoCycle()
      this.currentExample = example
      this.startAutoCycle() // Restart cycle after manual selection
    },
    nextExample () {
      const keys = Object.keys(this.examples)
      const currentIndex = keys.indexOf(this.currentExample)
      const nextIndex = (currentIndex + 1) % keys.length
      this.currentExample = keys[nextIndex]
    },
    startAutoCycle () {
      this.stopAutoCycle() // Ensure no duplicate intervals
      if (!this.isHovered) {
        this.intervalId = setInterval(this.nextExample, 3000)
      }
    },
    stopAutoCycle () {
      clearInterval(this.intervalId)
      this.intervalId = null
    },
    handleMouseOver () {
      this.isHovered = true
      this.stopAutoCycle()
    },
    handleMouseLeave () {
      this.isHovered = false
      this.startAutoCycle()
    }
  }
})
</script>

<i18n>
  {
    "en": {
      "Make studio quality headshots the new standard at your company.": "Make studio quality headshots the new standard at your company.",
      "Outfit your entire team in cohesive professional headshots with matching clothing and backdrops.": "Outfit your entire team in cohesive professional headshots with matching clothing and backdrops.",
      "Extensive Style Library": "Extensive Style Library",
      "Choose from over 200+ different backdrops and outfits.": "Choose from over 200+ different backdrops and outfits.",
      "Branded Profile Pictures": "Branded Profile Pictures",
      "Automatically apply your company branding to profile pictures for 1:1 consistency.": "Automatically apply your company branding to profile pictures for 1:1 consistency.",
      "Choose from 80 Photos": "Choose from 80 Photos",
      "Receive 80 high-resolution headshots and 80 branded PFPs per team member.": "Receive 80 high-resolution headshots and 80 branded PFPs per team member.",
      "Create your organization now": "Create your organization now",
      "View all styles": "View all styles",
      "business": "business",
      "casual": "casual",
      "custom": "custom"
    },
    "es": {
      "Make studio quality headshots the new standard at your company.": "Haz que las fotos de calidad de estudio sean el nuevo estándar en tu empresa.",
      "Outfit your entire team in cohesive professional headshots with matching clothing and backdrops.": "Viste a todo tu equipo con fotos profesionales cohesivas con ropa y fondos a juego.",
      "Extensive Style Library": "Amplia Biblioteca de Estilos",
      "Choose from over 200+ different backdrops and outfits.": "Elige entre más de 200 fondos y atuendos diferentes.",
      "Branded Profile Pictures": "Fotos de Perfil con Marca",
      "Automatically apply your company branding to profile pictures for 1:1 consistency.": "Aplica automáticamente la marca de tu empresa a las fotos de perfil para una consistencia 1:1.",
      "Choose from 80 Photos": "Elige entre 80 Fotos",
      "Receive 80 high-resolution headshots and 80 branded PFPs per team member.": "Recibe 80 fotos de alta resolución y 80 fotos de perfil con marca por miembro del equipo.",
      "Create your organization now": "Crear tu organización ahora",
      "View all styles": "Ver todos los estilos",
      "business": "empresarial",
      "casual": "casual",
      "custom": "personalizado"
    },
    "de": {
      "Make studio quality headshots the new standard at your company.": "Mache Studio-Qualität Bewerbungsfotos zum neuen Standard in deinem Unternehmen.",
      "Outfit your entire team in cohesive professional headshots with matching clothing and backdrops.": "Statte dein gesamtes Team mit einheitlichen professionellen Bewerbungsfotos mit passender Kleidung und Hintergründen aus.",
      "Extensive Style Library": "Umfangreiche Stil-Bibliothek",
      "Choose from over 200+ different backdrops and outfits.": "Wähle aus über 200 verschiedenen Hintergründen und Outfits.",
      "Branded Profile Pictures": "Marken-Profilbilder",
      "Automatically apply your company branding to profile pictures for 1:1 consistency.": "Wende automatisch dein Unternehmens-Branding auf Profilbilder für 1:1 Konsistenz an.",
      "Choose from 80 Photos": "Wähle aus 80 Fotos",
      "Receive 80 high-resolution headshots and 80 branded PFPs per team member.": "Erhalte 80 hochauflösende Bewerbungsfotos und 80 gebrandete Profilbilder pro Teammitglied.",
      "Create your organization now": "Organisation jetzt erstellen",
      "View all styles": "Alle Stile ansehen",
      "business": "Business",
      "casual": "Lässig",
      "custom": "Individuell"
    }
  }
</i18n>

<style scoped>
.example-button.active {
  @apply text-black font-medium;
  background-color: #E9E9EC;
}
.example-button:not(.active) {
  @apply text-black/60;
  background-color: #fff;
}
.example-button:not(.active):hover {
  @apply scale-105 transition-all duration-300 bg-gray-100;
}

/* Fade transition for images */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
  /* .fade-enter-from for Vue 3 */
.fade-leave-to
/* .fade-leave-active in <2.1.8 */
{
  opacity: 0;
}
</style>
