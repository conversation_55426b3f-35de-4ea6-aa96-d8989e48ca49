<template>
  <div class="relative overflow-hidden rounded-lg">
    <img class="object-cover w-full h-full" :src="image" alt="">

    <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/10 to-transparent group-hover:hidden" />

    <div class="absolute inset-x-0 bottom-0 flex items-center justify-center p-2 group-hover:hidden">
      <p class="text-xs text-center font-medium tracking-tight text-white">
        {{ title }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    image: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  }

}
</script>

<style>

</style>
