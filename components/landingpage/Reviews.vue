<template>
  <section id="examples" class="py-12 bg-white">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-5xl mx-auto text-center">
        <h2 class="mt-6 text-2xl font-medium  tracking-tight text-primary-500 sm:text-4xl sm:leading-[44px]">
          <slot name="title" />
        </h2>
        <p class="mt-4 text-base font-normal text-gray-600">
          You're in good company. See what their results look like below.
        </p>
      </div>
      <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
      <div v-show="!screenWidth || screenWidth >= 768" class="hidden md:grid grid-cols-4 md:grid-cols-8 gap-1 mt-8">
        <ImageDns v-for="item of reviews.slice(0, 16)" :key="item._id" :src="item.image" />
        <!-- <ImageDns alt="Headshot Example" v-for="index of 16" :key="index" :src="require(`@/assets/img/examples/${index + 29}.jpg`)" width="150" height="225" /> -->
      </div>
      <div class="grid md:hidden grid-cols-4 md:grid-cols-8 gap-1 mt-4">
        <ImageDns v-for="item of reviews.slice(0, 8)" :key="item._id" :src="item.image" />
      </div>
      <!-- <div class="w-full flex items-center justify-center pt-6">
        <nuxt-linkt to="/app/add">
          <ButtonOrange>
            <span class="flex-shrink-0">Get your headshots now</span>
            <IconSmallArrow class="flex-shrink-0 w-5 h-5 text-white ml-1.5" />
          </ButtonOrange>
        </nuxt-linkt>
      </div> -->
      <div class="mt-16 w-full mx-auto max-w-3xl bg-[#E8F6E5] rounded-md p-3 px-6 flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
        <div class="flex items-center justify-center md:justify-start">
          <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
          <!-- <ExperimentWrapper id="7-profile-worthy-refund">
            <template #variant>
              <p class="font-medium text-center md:text-left text-green-900">
                Try risk free now with our <nuxt-link to="/refund" class="underline">
                  ‘Profile-Worthy’ Money Back Guarantee
                </nuxt-link>
              </p>
            </template>
            <template #control>
              <p class="font-medium text-center md:text-left text-green-900">
                Get your headshots done in 2 hours, right from your home
              </p>
            </template>
          </ExperimentWrapper> -->
        </div>
        <nuxt-link to="/auth/login?redirect=%2Fapp%2Fadd">
          <ButtonPrimary size="sm">
            <span class="font-medium">Get your headshots</span>
            <IconChevron class="w-4 h-4 ml-1.5" />
          </ButtonPrimary>
        </nuxt-link>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    tweets: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    reviews () {
      return this.$store.state.reviews
    },
    items () {
      // Get first 2
      // If mobile get first
      if (process.client && window.innerWidth < 768) {
        return this.$store.state.examples.slice(0, 1)
      }
      return this.$store.state.examples.slice(0, 2)
    }
  }
  // data () {
  //   return {
  //     tweets: []
  //   }
  // },
  // async fetch () {
  //   const tweets = await this.$axios.$get('/reviews/wall-of-love/twitter', { params: { limit: 8 } })
  //   this.tweets = tweets
  // }
}
</script>

<style>

</style>
