<template>
  <li class="flex items-center text-sm">
    <svg class="w-5 h-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
    </svg>
    <span><slot /></span>
  </li>
</template>

<script setup>
// No specific props needed if using slots for content
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
