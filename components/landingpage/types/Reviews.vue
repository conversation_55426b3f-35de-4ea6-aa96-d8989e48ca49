<template>
  <section class="py-12  bg-[#F8FCFF] sm:py-16 lg:py-20 xl:py-24">
    <div class="max-w-screen-xl px-4 mx-auto 2xl:px-0 sm:px-6 lg:px-8">
      <div class="text-left md:text-center">
        <div class="flex items-center justify-start md:justify-center gap-x-3">
          <div class="flex -space-x-2 overflow-hidden">
            <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-1.jpg" alt="">
            <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-2.jpg" alt="">
            <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-3.jpg" alt="">
          </div>
          <p class="text-sm sm:text-base lg:text-lg -mt-0.5 font-medium tracking-tighter text-paragraph">
            Trusted by <span class="font-bold text-[#00B67A]">{{ $store.state.stats.users }}+</span> happy customers
          </p>
        </div>
        <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500">
          HeadshotPro Success Stories
        </h2>
        <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl">
          The fastest way to get professional headshots you can use anywhere
        </p>
      </div>

      <div class="relative mt-8 md:px-20 sm:mt-12 lg:px-0">
        <div class="absolute flex-col items-center hidden gap-1 xl:flex right-8 top-[-128px]">
          <p
            class="text-sm leading-4 rotate-[-12deg] py-4 text-right font-cursive text-paragraph tracking-[-0.056px]"
          >
            These photos are<br>
            100% AI generated
          </p>
          <svg
            class="w-auto h-10 text-paragraph"
            viewBox="0 0 22 41"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.53796 38.6866C8.57188 38.4947 10.5521 38.3342 12.7948 38.1201C12.5882 39.0022 11.7313 39.4684 10.7889 39.6202C7.96176 40.0756 5.08487 40.4938 2.1662 40.7376C0.498109 40.8818 -0.335102 40.0427 0.243298 39.0102C1.1735 37.2759 2.2568 35.5848 3.29038 33.8565C3.34802 33.7567 3.40765 33.6226 3.51302 33.5943C3.8848 33.4439 4.30429 33.365 4.72578 33.2518C4.86698 33.5007 5.21497 33.7615 5.15138 33.9642C4.95466 34.6749 4.65654 35.3454 4.41012 36.0189C4.28492 36.3899 4.11001 36.7236 4.33677 37.2869C4.9789 36.9458 5.67274 36.6077 6.31487 36.2666C19.0899 28.8215 22.3824 16.7359 14.6464 6.10992C13.6362 4.74479 12.4132 3.47051 11.2976 2.13367C10.9098 1.66421 10.5737 1.19774 10.186 0.728278C10.2953 0.631442 10.4543 0.571868 10.5637 0.475033C11.023 0.604761 11.6413 0.674917 11.9376 0.932763C12.9796 1.74954 14.0215 2.56633 14.9025 3.47695C23.7158 12.5147 23.3937 24.3595 13.973 32.9958C12.1659 34.645 9.90557 36.0617 7.84801 37.5588C7.41463 37.8776 6.93352 38.1249 6.45043 38.4064C6.39477 38.472 6.44052 38.5778 6.53796 38.6866Z"
            />
          </svg>
        </div>

        <div v-show="!screenWidth || screenWidth >= 768" class="hidden md:grid grid-cols-2 md:grid-cols-4 gap-2 mt-4">
          <template v-for="item of newReviews.slice(0, 12)">
            <div
              :key="item._id"
              class="transition duration-300 relative overflow-hidden rounded-md"
            >
              <ImageDns :src="item?.thumbnail || item.image" />
              <div v-if="item?.review?.quote" class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
                <p class="font-bold">
                  {{ item?.review?.title }}
                </p>
                <p class="italic text-white/80 text-[10px] leading-[12px]">
                  "{{ item?.review?.quote }}"
                </p>
              </div>
            </div>
          </template>
        </div>
        <div class="grid md:hidden grid-cols-2 gap-1 mt-4">
          <ImageDns v-for="item of newReviews.slice(0, 4)" :key="item._id" :src="item?.thumbnail || item.image" />
        </div>
      </div>

      <div class="mt-8 text-left md:text-center sm:mt-12">
        <p class="text-base font-bold tracking-tighter text-[#474368]">
          Our customers also use their AI headshots for:
        </p>

        <div class="flex flex-wrap items-center justify-start gap-3 mt-4 md:justify-center md:gap-6 xl:gap-x-16">
          <div class="inline-flex items-center gap-3">
            <svg
              aria-hidden="true"
              class="size-7 text-[#77C3EC]"
              viewBox="0 0 90 90"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21.807 55.554C21.807 60.1794 18.0287 63.9579 13.4035 63.9579C8.77832 63.9579 5 60.1794 5 55.554C5 50.9286 8.77832 47.1501 13.4035 47.1501H21.807V55.554Z"
              />
              <path
                d="M26.0393 55.5542C26.0393 50.9288 29.8176 47.1503 34.4428 47.1503C39.068 47.1503 42.8463 50.9288 42.8463 55.5542V76.5965C42.8463 81.2219 39.068 85.0004 34.4428 85.0004C29.8176 85.0004 26.0393 81.2219 26.0393 76.5965V55.5542Z"
              />
              <path
                d="M34.4428 21.8078C29.8176 21.8078 26.0393 18.0293 26.0393 13.4039C26.0393 8.7785 29.8176 5 34.4428 5C39.068 5 42.8463 8.7785 42.8463 13.4039V21.8078H34.4428Z"
              />
              <path
                d="M34.4448 26.0423C39.07 26.0423 42.8483 29.8208 42.8483 34.4462C42.8483 39.0716 39.07 42.8501 34.4448 42.8501H13.4035C8.77832 42.8501 5 39.0716 5 34.4462C5 29.8208 8.77832 26.0423 13.4035 26.0423H34.4448Z"
              />
              <path
                d="M68.1904 34.4461C68.1904 29.8207 71.9687 26.0422 76.5939 26.0422C81.2191 26.0422 84.9974 29.8207 84.9974 34.4461C84.9974 39.0715 81.2191 42.85 76.5939 42.85H68.1904V34.4461Z"
              />
              <path
                d="M63.9582 34.4462C63.9582 39.0716 60.1798 42.8501 55.5547 42.8501C50.9295 42.8501 47.1512 39.0716 47.1512 34.4462V13.4039C47.1512 8.7785 50.9295 5 55.5547 5C60.1798 5 63.9582 8.7785 63.9582 13.4039V34.4462Z"
              />
              <path
                d="M55.5547 68.1927C60.1798 68.1927 63.9582 71.9712 63.9582 76.5966C63.9582 81.222 60.1798 85.0005 55.5547 85.0005C50.9295 85.0005 47.1512 81.222 47.1512 76.5966V68.1927H55.5547Z"
              />
              <path
                d="M55.5547 63.9582C50.9295 63.9582 47.1512 60.1797 47.1512 55.5543C47.1512 50.9289 50.9295 47.1504 55.5547 47.1504H76.596C81.2211 47.1504 84.9995 50.9289 84.9995 55.5543C84.9995 60.1797 81.2211 63.9582 76.596 63.9582H55.5547Z"
              />
            </svg>
            <p class="text-base font-medium text-[#474368]">
              Slack/Microsoft Teams
            </p>
          </div>

          <div class="inline-flex items-center gap-3">
            <svg
              aria-hidden="true"
              class="size-7 text-[#77C3EC]"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M12.75 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM7.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM8.25 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM9.75 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM10.5 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM12.75 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM14.25 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 13.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
              />
              <path
                fill-rule="evenodd"
                d="M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z"
                clip-rule="evenodd"
              />
            </svg>
            <p class="text-base font-medium text-[#474368]">
              Conference Bio Photos
            </p>
          </div>

          <div class="inline-flex items-center gap-3">
            <svg
              aria-hidden="true"
              class="size-7 text-[#77C3EC]"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M7.5 5.25a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0 1 12 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 0 1 7.5 5.455V5.25Zm7.5 0v.09a49.488 49.488 0 0 0-6 0v-.09a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5Zm-3 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
                clip-rule="evenodd"
              />
              <path
                d="M3 18.4v-2.796a4.3 4.3 0 0 0 .713.31A26.226 26.226 0 0 0 12 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 0 1-6.477-.427C4.047 21.128 3 19.852 3 18.4Z"
              />
            </svg>
            <p class="text-base font-medium text-[#474368]">
              Business Cards
            </p>
          </div>
          <div class="inline-flex items-center gap-3">
            <svg
              aria-hidden="true"
              class="size-7 text-[#77C3EC]"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M4.913 2.658c2.075-.27 4.19-.408 6.337-.408 2.147 0 4.262.139 6.337.408 1.922.25 3.291 1.861 3.405 3.727a4.403 4.403 0 0 0-1.032-.211 50.89 50.89 0 0 0-8.42 0c-2.358.196-4.04 2.19-4.04 4.434v4.286a4.47 4.47 0 0 0 2.433 3.984L7.28 21.53A.75.75 0 0 1 6 21v-4.03a48.527 48.527 0 0 1-1.087-.128C2.905 16.58 1.5 14.833 1.5 12.862V6.638c0-1.97 1.405-3.718 3.413-3.979Z"
              />
              <path
                d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 7.797 9 9.103 9 10.609v4.285c0 1.507 1.128 2.814 2.67 2.94 1.243.102 2.5.157 3.768.165l2.782 2.781a.75.75 0 0 0 1.28-.53v-2.39l.33-.026c1.542-.125 2.67-1.433 2.67-2.94v-4.286c0-1.505-1.125-2.811-2.664-2.94A49.392 49.392 0 0 0 15.75 7.5Z"
              />
            </svg>
            <p class="text-base font-medium text-[#474368]">
              Social Media
            </p>
          </div>

          <div class="inline-flex items-center gap-3">
            <svg
              aria-hidden="true"
              class="size-7 text-[#77C3EC]"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M3 2.25a.75.75 0 0 0 0 1.5v16.5h-.75a.75.75 0 0 0 0 1.5H15v-18a.75.75 0 0 0 0-1.5H3ZM6.75 19.5v-2.25a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h.75a.75.75 0 0 1 0 1.5h-.75A.75.75 0 0 1 6 6.75ZM6.75 9a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM6 12.75a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 6a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75Zm-.75 3.75A.75.75 0 0 1 10.5 9h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 12a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM16.5 6.75v15h5.25a.75.75 0 0 0 0-1.5H21v-12a.75.75 0 0 0 0-1.5h-4.5Zm1.5 4.5a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 2.25a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75v-.008a.75.75 0 0 0-.75-.75h-.008ZM18 17.25a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z"
                clip-rule="evenodd"
              />
            </svg>
            <p class="text-base font-medium text-[#474368]">
              Real Estate
            </p>
          </div>
        </div>
      </div>

      <div class="gap-6 mt-8 sm:flex sm:items-center sm:justify-center sm:mt-12">
        <nuxt-link
          to="/pricing"
          title=""
          class="text-lg w-full leading-6 sm:w-auto inline-flex font-bold text-white rounded-lg bg-primary-500 px-6 pt-2.5 pb-3.5 h-12 border border-primary-600 transition-all duration-150 hover:bg-opacity-90 shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] gap-1.5 items-center justify-center disabled:bg-opacity-20"
          role="button"
        >
          Choose your headshot package
        </nuxt-link>

        <nuxt-link
          to="/reviews"
          title=""
          class="text-base w-full mt-4 sm:mt-0 sm:w-auto h-12 inline-flex font-semibold text-gray-600 rounded-lg shadow-sm bg-gray-200 px-4 pt-2.5 pb-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20"
          role="button"
        >
          See 100+ real examples
        </nuxt-link>
      </div>

      <div class="grid max-w-5xl grid-cols-1 gap-6 mx-auto mt-8 sm:mt-12 md:grid-cols-3">
        <template v-for="item of trustPilotReviews.slice(0, 3)">
          <LandingpageV2ReviewTrustpilot :key="item.id" :cap-length="true" :item="item" />
        </template>
      </div>
      <div class="flex items-center justify-center mt-4">
        <nuxt-link to="/reviews" class="text-sm font-medium text-primary-500 hover:underline bg-white/20 rounded p-2">
          Read all our reviews (1,000+)
        </nuxt-link>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  computed: {
    reviews () {
      return this.$store.state.reviews
    },
    trustPilotReviews () {
      return this.$store.state.trustpilotReviews
    },
    newReviews () {
      const filteredReviews = this.reviews.filter(review =>
        this.isCreatedAfterAugust21(review.createdAt) && review?.review?.frontpage === true
      )

      // Separate reviews by gender
      const maleReviews = filteredReviews.filter(review => review.trigger === 'male')
      const femaleReviews = filteredReviews.filter(review => review.trigger === 'female')

      // Interleave male and female reviews
      const mixedReviews = []
      const maxLength = Math.max(maleReviews.length, femaleReviews.length)
      for (let i = 0; i < maxLength; i++) {
        if (maleReviews[i]) { mixedReviews.push(maleReviews[i]) }
        if (femaleReviews[i]) { mixedReviews.push(femaleReviews[i]) }
      }

      return mixedReviews
    }
  },
  methods: {
    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    }
  }

}
</script>

<style>

</style>
