<template>
  <div class="w-full hidden md:block">
    <!-- START STEPS -->
    <div class="relative z-40 hidden md:block">
      <div aria-hidden="true" class="absolute inset-0 hidden sm:grid">
        <div class="bg-[#E0FAFA]" />
      </div>

      <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
        <div class="relative -top-11 isolate z-20 mx-auto max-w-5xl rounded-lg border-2 border-[#DAF1FE] bg-[#FAFAFA] px-6 py-5 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] sm:px-8">
          <div class="flex flex-col gap-6 lg:flex-row lg:items-end">
            <img width="956" height="48" src="@/assets/img/landing-page/steps-hero.png">
          </div>
        </div>
      </div>
    </div>
    <!-- <PERSON><PERSON> STEPS -->

    <!-- START BASIC PRICING -->
    <section class="relative bg-[#E0FAFA] py-6 sm:py-10 md:pt-0 lg:-mt-2">
      <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
        <div class="gap-8 sm:flex sm:items-center sm:justify-center sm:gap-16 md:flex-row">
          <div>
            <h3 class="text-md font-bold text-primary-500 lg:text-lg">
              {{ $t('All photoshoots include:') }}
            </h3>
            <ul class="mt-3 space-y-1 text-sm font-medium text-primary-500 md:space-y-2 xl:text-base">
              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                {{ $t('Done in 2 hours or less') }}
              </li>

              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                {{ $t('8x cheaper than a photographer') }}
              </li>

              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                {{ $t('Hundreds of results to choose from') }}
              </li>
            </ul>
          </div>

          <div class="hidden space-y-2 md:block">
            <div class="flex items-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
              <client-only>
                <p class="text-5xl font-bold text-primary-500">
                  <span v-if="getFormattedPriceParts(price).smallTextPrefix" class="text-3xl font-normal">
                    {{ getFormattedPriceParts(price).smallTextPrefix }}
                  </span>
                  <span v-if="getFormattedPriceParts(price).largePart">
                    {{ getFormattedPriceParts(price).largePart }}
                  </span>
                </p>
              </client-only>
            </div>
            <div class="flex items-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
              <div class="flex items-center gap-2">
                <p class="text-5xl font-bold text-primary-500">
                  2
                </p>
                <p class="text-lg font-bold leading-none text-primary-500">
                  {{ $t('hours') }}
                  <br>
                  {{ $t('done') }}
                </p>
              </div>
            </div>
          </div>

          <div class="hidden sm:block">
            <h3 class="text-md font-bold text-primary-500 lg:text-lg">
              {{ $t('Every package includes:') }}
            </h3>
            <ul class="mt-3 space-y-1 text-sm font-medium text-primary-500 md:space-y-2 xl:text-base">
              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                {{ $t('Indistinguishable from real photos') }}
              </li>

              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                {{ $t('Business expense-ready invoice') }}
              </li>

              <li class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                {{ $t('Discounts up to 60% for teams') }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    <!-- END BASIC PRICING -->
  </div>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'

export default {
  name: 'Stats',
  mixins: [PosthogMixin],
  computed: {
    price () {
      return this.getLocalizedPrice('small', true, 0, true)
    }
  }
}
</script>

<i18n>
  {
    "es": {
      "All photoshoots include:": "Todas las sesiones incluyen",
      "Done in 2 hours or less": "Hecho en 2 horas o menos",
      "8x cheaper than a photographer": "8x más barato que un fotógrafo",
      "Hundreds of results to choose from": "Cientos de resultados para elegir",
      "Every package includes:": "Todos los paquetes incluyen:",
      "Indistinguishable from real photos": "Indistinguible de fotos reales",
      "Business expense-ready invoice": "Factura para tu negocio",
      "Discounts up to 60% for teams": "Descuentos de hasta el 60% para equipos",
      "hours": "horas",
      "done": "hecho"
    },
    "de": {
      "All photoshoots include:": "Jedes Fotoshooting beinhaltet:",
      "Done in 2 hours or less": "In 2 Stunden oder weniger erledigt",
      "8x cheaper than a photographer": "8 × günstiger als ein Fotograf",
      "Hundreds of results to choose from": "Hunderte Ergebnisse zur Auswahl",
      "Every package includes:": "Jedes Paket enthält:",
      "Indistinguishable from real photos": "Von echten Fotos nicht zu unterscheiden",
      "Business expense-ready invoice": "Rechnung fürs Unternehmen geeignet",
      "Discounts up to 60% for teams": "Rabatte bis zu 60 % für Teams",
      "hours": "Stunden",
      "done": "fertig"
    }
  }
</i18n>
