<template>
  <Popup size="lg" @closeModal="(isSubmitted) ? redirectToDashboard() : $emit('close')">
    <div v-if="!isSubmitted" class="flex flex-col gap-4">
      <div class="flex flex-col">
        <Heading>
          Get a custom quote
        </Heading>
        <Paragraph size="sm">
          We'll get back to you with a custom quote for your team.
        </Paragraph>
      </div>
      <Input v-model="invoice.meta.companyName" label="Company name*" />
      <div class="grid grid-cols-2 gap-2">
        <Input v-model="invoice.quantity" label="Amount of credits needed*" />
        <InputSelect :value="invoice.currency" :options="currencyOptions" label="Currency" @input="updateCurrency" />
      </div>
      <Input v-model="invoice.meta.email" :placeholder="$store.state.user?.email" label="Email address (if different)" />
      <!-- <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input v-model="invoice.meta.firstName" label="First name" />
        <Input v-model="invoice.meta.lastName" label="Last name" />
      </div> -->
      <!-- <InputTextArea v-model="invoice.billingDetails" :rows="2" placeholder="I.e. 1247 Maple Street, Springfield, IL 62704" label="Billing details" /> -->
      <InputTextArea v-model="invoice.meta.notes" :rows="3" placeholder="Custom request, etc." label="Notes" />
      <ButtonPrimary size="sm" @click="submit">
        Get a custom quote
      </ButtonPrimary>
    </div>
    <div v-else class="flex flex-col gap-4">
      <div class="flex flex-col space-y-4">
        <div class="flex flex-col gap-0.5">
          <Heading class="!text-green-600">
            ✅ We've received your request
          </Heading>
          <Paragraph size="sm">
            We'll get back to you with a custom quote for your team as soon as possible. Depending on the timezone, it may take up to 24 hours.
          </Paragraph>
        </div>
        <ButtonPrimary size="sm" @click="redirectToDashboard">
          Go to dashboard
        </ButtonPrimary>
      </div>
    </div>
  </Popup>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import Heading from '@/components/landingpage/common/H5.vue'

export default {
  components: {
    Paragraph,
    Heading
  },
  props: {
    quantity: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      invoice: {
        quantity: 1,
        // billingDetails: '',
        meta: {
          // firstName: null,
          // lastName: null,
          companyName: null,
          email: null,
          notes: null
        },
        currency: 'usd'
      },
      isSubmitted: false
    }
  },
  computed: {
    currencyOptions () {
      return this.$store.state.allowedCurrencies.map(currency => ({
        title: currency.title,
        value: currency.code
      }))
    }
  },
  mounted () {
    this.invoice.currency = this.userCurrency || 'usd'
    this.invoice.meta.companyName = this.$store.state.organization.organization.name
    this.invoice.quantity = this.quantity
    // this.invoice.meta.firstName = this.$store.state?.user?.displayName?.split(' ')[0]
    // this.invoice.meta.lastName = this.$store.state?.user?.displayName?.split(' ')[1]
  },
  methods: {
    updateCurrency (currency) {
      this.invoice.currency = currency
    },
    async submit () {
      // Validate all fields
      if (!this.invoice.meta.companyName) {
        this.$toast.error('Please enter a company name')
        return
      }
      if (!this.invoice.quantity) {
        this.$toast.error('Please enter a quantity')
        return
      }

      this.$loading.show({
        title: 'Getting a custom quote...'
      })

      // Create a new invoice
      try {
        const { success, errorMessage } = await this.$axios.$post('/invoices/request-quote', this.invoice)
        if (!success) {
          this.$toast.error(errorMessage)
          return
        }

        this.isSubmitted = true
        this.$toast.success('Quote created successfully')
        // this.isSubmitted = true
      } catch (error) {
        console.log(error)
        this.$toast.error('An error occurred while creating the invoice')
      } finally {
        this.$loading.hide()
      }
    },
    redirectToDashboard () {
      this.$router.push('/app/admin')
    }
  }
}
</script>

<style>

</style>
