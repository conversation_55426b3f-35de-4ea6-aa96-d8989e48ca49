<template>
  <div class="space-y-2.5">
    <p class="text-lg font-bold tracking-tight text-primary-500">
      Pick a package
    </p>
    <div class="space-y-2.5">
      <template v-for="price in packages">
        <button v-if="price?.meta?.visible" :key="price.packageId" type="button" class="relative block w-full text-left transition-transform duration-150 ease-in-out hover:scale-105" @click="$emit('select', price.id)">
          <div
            class="absolute right-5 top-1/2 flex size-5 -translate-y-1/2 items-center justify-center rounded-full border border-gray-300"
            :class="{
              'border-teal-500 bg-teal-500': selectedPackageId === price.id,
              'bg-white': selectedPackageId !== price.id
            }"
          >
            <div class="h-2 w-2 rounded-full bg-white" />
          </div>

          <div
            class="border-ring-100 cursor-pointer rounded-xl py-4 pl-5 pr-12 shadow-sm ring-1"
            :class="{
              'bg-teal-50 ring-teal-500': selectedPackageId === price.id,
              'bg-white ring-black/5 hover:bg-blue-50': selectedPackageId !== price.id
            }"
          >
            <div class="flex items-center justify-between">
              <div
                class="flex flex-col gap-0.5"
              >
                <div class="flex space-x-1 items-center justify-start">
                  <span class="font-bold text-primary-500">{{ getLocalizedPrice(price.id, true, 0, false) }}</span>
                  <span class="text-gray-700">∙</span>
                  <h4 v-if="price.title" class="block text-sm font-bold tracking-tight text-primary-500 sm:text-base">
                    {{ price.title }}
                  </h4>
                  <div class="w-[4px]" />
                  <div v-if="price?.meta?.tag === 'popular'" class="px-1.5 leading-none h-[18px] py-0.5 rounded-full text-[9px] flex items-center justify-center text-white font-bold bg-green-500">
                    <!-- X% PICK THIS PLAN -->
                    {{ $t('percentPickThisPlan', { percent: 78 }) }}
                  </div>
                </div>
                <p class="text-[13px] text-gray-500 tracking-[-0.4px]">
                  Get <strike v-if="price?.meta?.regularStyles" class="text-red-500">
                    {{ price?.meta?.regularStyles * photoPerStyle }}
                  </strike> <strong>{{ (price?.meta?.styles + price?.meta?.additional) * photoPerStyle }}</strong>
                  headshots with <strike v-if="price?.meta?.regularStyles" class="text-red-500">
                    {{ price?.meta?.regularStyles }}
                  </strike>
                  <strong>{{ (price?.meta?.styles + price?.meta?.additional) }}</strong> unique
                  <template v-if="(price?.meta?.styles + price?.meta?.additional) === 1">
                    background and outfit
                  </template>
                  <template v-else>
                    backgrounds and outfits
                  </template>
                  + <strong>{{ price?.meta?.regenerationCredits }}</strong> edit credits.
                  <span>Done in <strong>{{ price?.meta?.turnAroundTime ?? '2 hours' }}.</strong></span>
                </p>
              </div>
            </div>
          </div>
        </button>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    selectedPackageId: {
      type: String,
      required: true
    }
  },
  computed: {
    packages () {
      return this.$store.state.packages
    }
  }
}
</script>

<style>

</style>

<i18n>
    {
      "en": {
        "Select a package": "Select a package",
        "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you’re unsatisfied": "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you’re unsatisfied",
        "percentPickThisPlan": "{percent}% PICK THIS PLAN",
        "Select": "Select",
        "Check reviews": "Check reviews",
        "usedByNHappyCustomers": "Used by {n}+ happy customers.",
        "AI generated": "AI generated",
        "14-day money back guarantee": "14-day money back guarantee"
      },
      "es": {
        "Select a package": "Selecciona un paquete",
        "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you’re unsatisfied": "Paga una vez, sin suscripciones ni tarifas ocultas. No ofrecemos prueba debido a los altos costos, pero te reembolsaremos si no estás satisfecho",
        "percentPickThisPlan": "{percent}% ELIGE ESTE PLAN",
        "Select": "Seleccionar",
        "Check reviews": "Ver reseñas",
        "usedByNHappyCustomers": "Usado por {n}+ clientes felices.",
        "AI generated": "Generado por IA",
        "14-day money back guarantee": "Garantía de devolución de 14 días"
      },
      "de": {
        "Select a package": "Paket auswählen",
        "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you're unsatisfied": "Einmalige Zahlung, keine Abos oder versteckte Gebühren. Wir bieten keine Testversion aufgrund hoher Kosten an, erstatten aber bei Unzufriedenheit",
        "percentPickThisPlan": "{percent}% WÄHLEN DIESEN PLAN",
        "Select": "Auswählen",
        "Check reviews": "Bewertungen ansehen",
        "usedByNHappyCustomers": "Von {n}+ zufriedenen Kunden genutzt.",
        "AI generated": "KI generiert",
        "14-day money back guarantee": "14-Tage Geld-zurück-Garantie"
      }
    }
  </i18n>
