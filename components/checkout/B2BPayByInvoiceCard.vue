<template>
  <CheckoutB2BPaymentCard
    :title="title"
    :subtitle="subtitle"
    :how-it-works="howItWorks"
  >
    <p class="text-[15px] font-semibold text-primary-500">
      {{ seatsLabel }}
    </p>
    <div class="flex items-center justify-start gap-4 mt-1 mb-10">
      <Input v-model="quantity" type="number" :min="1" class="w-[80px]" @input="checkForQuantity" />
      <input
        v-model="quantity"
        type="range"
        class="w-full bg-transparent cursor-pointer appearance-none disabled:opacity-50 disabled:pointer-events-none focus:outline-none [&::-webkit-slider-thumb]:w-6 [&::-webkit-slider-thumb]:h-6 [&::-webkit-slider-thumb]:-mt-2 [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:bg-[#1B145D] [&::-webkit-slider-thumb]:shadow-[0_0_0_3px_rgba(255,255,255,1)] [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:transition-all [&::-webkit-slider-thumb]:duration-150 [&::-webkit-slider-thumb]:ease-in-out [&::-moz-range-thumb]:w-6 [&::-moz-range-thumb]:h-6 [&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:bg-[#1B145D] [&::-moz-range-thumb]:border-3 [&::-moz-range-thumb]:border-white [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:transition-all [&::-moz-range-thumb]:duration-150 [&::-moz-range-thumb]:ease-in-out [&::-webkit-slider-runnable-track]:w-full [&::-webkit-slider-runnable-track]:h-2.5 [&::-webkit-slider-runnable-track]:bg-[#EAECF0] [&::-webkit-slider-runnable-track]:rounded-full [&::-moz-range-track]:w-full [&::-moz-range-track]:h-2.5 [&::-moz-range-track]:bg-[#EAECF0] [&::-moz-range-track]:rounded-full"
        :min="1"
        :max="teamMaxSeats"
      >
    </div>
    <!-- <div class="relative mt-1 mb-10 xs:hidden">
      <Input
        :value="quantity"
        type="number"
        :min="1"
        :max="teamMaxSeats"
        class="w-full"
        @input="quantity = parseInt($event) || 1"
      />
    </div> -->
    <CheckoutB2BSummaryCard
      :title="`${quantity}x photoshoot credits`"
      subtitle="1 credit equals 80 unique headshots for one team member"
      total-text="Total"
      :quantity="parseInt(quantity) || 0"
      :unit-price="price/100"
      :discount="discount"
    />
    <div class="mt-4 space-y-3">
      <CheckoutPaymentMethodSelection
        class="!bg-yellow-50/50"
        :payment-providers="[{ ...paymentProviders[0], isPrimary: true }]"
        theme="button"
        @select="selectPaymentProvider"
      />
      <CheckoutPaymentMethodItem
        name="Pay via bank transfer"
        theme="button"
        @select="showSalesModal = true"
      />
      <CheckoutPaymentMethodItem
        name="Get a quote"
        description="Receive a custom quote for your team and pay later."
        theme="button"
        @select="showCustomQuoteModal = true"
      />
    </div>
    <CheckoutCustomQuoteModal v-if="showCustomQuoteModal" :quantity="quantity" @close="showCustomQuoteModal = false" />
    <MarketingSalesModal v-if="showSalesModal" :form-message="`I would like to purchase ${quantity} credits for my team via bank transfer.`" @close="showSalesModal = false" />
  </CheckoutB2BPaymentCard>
</template>

<script>
import CheckoutMixin from '@/mixins/CheckoutMixin'

export default {
  mixins: [CheckoutMixin],
  props: {
    title: {
      type: String,
      default: 'Pay by invoice'
    },
    subtitle: {
      type: String,
      default: 'Purchase credits for your team. This is a one-time purchase. When your credits run out, you need to make another purchase.'
    },
    howItWorks: {
      type: Array,
      default: () => ['Pre-purchase credits for your team to use.', 'Purchased credits never expire, use them whenever you want.', 'Purchase new credits once your old ones run out.']
    },
    seatsLabel: {
      type: String,
      default: 'Total team members'
    }
  },
  data () {
    return {
      quantity: 10,
      existingQuantity: 0,
      selectedPaymentProvider: null,
      showSalesModal: false,
      showCustomQuoteModal: false,
      priceDetails: null,
      product: null
    }
  },
  computed: {
    totalQuantity () {
      return (parseInt(this.quantity) || 0) + (parseInt(this.existingQuantity) || 0)
    },
    discount () {
      const totalQuantity = parseInt(this.quantity) + parseInt(this.existingQuantity)
      for (const option of this.teamDiscountOptions) {
        if (totalQuantity >= option.from && totalQuantity <= option.to) {
          return option.discount
        }
      }

      return 0
    },
    priceId () {
      return 'medium'
    },
    price () {
      try {
        return this.priceDetails?.currency[this.userCurrency] ?? this.priceDetails?.price
      } catch (err) {
        return this.priceDetails?.price
      }
    }
  },
  mounted () {
    // this.getPrice()
    this.getPackage()

    if (this.$route.query.teamSize) {
      this.quantity = parseInt(this.$route.query.teamSize)
    }

    if (this.$store.state?.organization?.organization?.totalCreditsPurchased) {
      this.existingQuantity = this.$store.state.organization.organization.totalCreditsPurchased
    } else if (this.$store.state?.organization?.organization?.creditLog) {
      const previouslyPurchasedCredits = this.$store.state.organization.organization.creditLog
        .filter(item => item.mutation > 0)
        .filter(item => item.reason === 'Credits bought')
        .reduce((acc, item) => acc + parseInt(item.mutation), 0)
      this.existingQuantity = previouslyPurchasedCredits
    }

    if (this.$store.state?.organization?.organization?.teamSize) {
      const teamSize = parseInt(this.$store.state?.organization?.organization?.teamSize?.split('-')?.[0]?.trim())
      if (teamSize > 0) {
        this.quantity = teamSize
      }
    }
  },
  methods: {
    checkForQuantity ($event) {
      const quantity = parseInt($event)
      if (quantity < 1 || $event === '') {
        this.quantity = 1
      }
    },
    async getPackage (id = null) {
      try {
        this.$loading.show({
          title: 'Loading price...'
        })
        const { success, data, errorMessage } = await this.$axios.$get(`/checkout/price?productId=package&priceId=${this.priceId}`)

        if (!success) {
          throw new Error(errorMessage)
        }

        if (!data?.product) {
          throw new Error('Product not found')
        }

        if (!data?.price) {
          throw new Error('Price not found')
        }

        const { product, price } = data

        this.priceDetails = price

        if (product) {
          const { description, images, name } = product
          this.product = { description, name, image: images[0] }
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },
    selectPaymentProvider (paymentProviderId) {
      this.selectedPaymentProvider = paymentProviderId || this.paymentProviders[0].id
      this.toTeamCheckout()
    }
  }
}
</script>
