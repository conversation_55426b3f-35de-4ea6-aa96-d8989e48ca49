<template>
  <LoadingWrapper :is-loading="isLoading" :title="$t('Checking code...')">
    <div v-if="!isActive && !discountPercentage">
      <p class="text-sm text-gray-500 w-full">
        {{ $t('Got a coupon or discount?') }}
        <button class="underline" @click="isActive = true">
          {{ $t('Enter it here.') }}
        </button>
      </p>
    </div>
    <div v-else class="flex items-center justify-start w-full space-x-2 text-gray-700 text-sm ">
      <template v-if="(discountPercentage && discountPercentage > 0) || (discountAmount && discountAmount > 0)">
        <div class="flex items-center justify-between p-2 rounded-md bg-white shadow-sm border border-gray-100  w-full">
          <p v-if="discountPercentage && discountPercentage > 0" v-html="$t('appliedCodeFor', { code: couponCode, percentage: discountPercentage })" />
          <p v-else-if="discountAmount && discountAmount > 0" v-html="$t('appliedCodeForAmount', { code: couponCode, amount: formatPrice(discountAmount / 100, currency, 2, false) })" />
          <button class="text-red-500 underline text-xs" @click="$emit('couponApplied', { amount: 0, code: null })">
            {{ $t('Clear') }}
          </button>
        </div>
      </template>
      <template v-else>
        <Input v-model="couponCode" :placeholder="$t('Coupon code')" class="w-full" />
        <ButtonWhite class="py-2.5" size="sm" @click="checkCoupon">
          {{ $t('Apply') }}
        </ButtonWhite>
      </template>
    </div>
  </LoadingWrapper>
</template>

<script>
export default {
  props: {
    discountPercentage: {
      type: Number,
      default: 0
    },
    discountAmount: {
      type: Number,
      default: 0
    },
    currency: {
      type: String,
      default: 'usd'
    }
  },
  data () {
    return {
      isActive: false,
      couponCode: null,
      isLoading: false
    }
  },
  mounted () {
    if (this.$route.query.coupon) {
      this.couponCode = this.$route.query.coupon
      this.checkCoupon()
    }
    if (window?.Rewardful?.coupon?.id && this.userWasReferredViaInternalSystem) {
      this.couponCode = window?.Rewardful?.coupon?.id
      this.checkCoupon()
    }
  },
  methods: {
    checkCoupon () {
      const { couponCode } = this
      if (!couponCode || couponCode.length === 0) { return }
      this.isLoading = true
      this.$axios.$post('/checkout/coupon', { couponCode })
        .then((response) => {
          if (!response.success) {
            return this.$toast.open({
              message: response.message,
              type: 'error'
            })
          }

          const { coupon } = response
          this.$emit('couponApplied', coupon)
          this.$toast.success(this.$t('Coupon applied!'))
          this.isActive = true
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }

}
</script>

<i18n>
  {
    "en": {
      "Checking code...": "Checking code...",
      "Got a coupon or discount?": "Got a coupon or discount?",
      "Enter it here.": "Enter it here.",
      "Clear": "Clear",
      "Apply": "Apply",
      "Coupon code": "Coupon code",
      "Coupon applied!": "Coupon applied!",
      "appliedCodeFor": "Applied code <strong>{code}</strong> for {percentage}% off.",
      "appliedCodeForAmount": "Applied code <strong>{code}</strong> for {amount} off."
    },
    "es": {
      "Checking code...": "Comprobando código...",
      "Got a coupon or discount?": "¿Tienes un cupón o descuento?",
      "Enter it here.": "Introdúcelo aquí.",
      "Clear": "Limpiar",
      "Apply": "Aplicar",
      "Coupon code": "Código de cupón",
      "Coupon applied!": "¡Cupón aplicado!",
      "appliedCodeFor": "Código aplicado <strong>{code}</strong> para un {percentage}% de descuento.",
      "appliedCodeForAmount": "Código aplicado <strong>{code}</strong> para un {amount} de descuento."
    },
    "de": {
      "Checking code...": "Code wird überprüft...",
      "Got a coupon or discount?": "Hast du einen Gutschein oder Rabatt?",
      "Enter it here.": "Hier eingeben.",
      "Clear": "Löschen",
      "Apply": "Anwenden",
      "Coupon code": "Gutscheincode",
      "Coupon applied!": "Gutschein angewendet!",
      "appliedCodeFor": "Code <strong>{code}</strong> für {percentage}% Rabatt angewendet.",
      "appliedCodeForAmount": "Code <strong>{code}</strong> für {amount} Rabatt angewendet."
    }
  }
</i18n>
