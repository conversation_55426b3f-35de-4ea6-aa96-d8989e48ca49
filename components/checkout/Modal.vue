<template>
  <Popup size="lg" @closeModal="$emit('closeModal')">
    <h2 class="font-bold">
      Select a package
    </h2>
    <div class="flex flex-col rounded-xl border border-gray-200 overflow-hidden mt-2">
      <div class="w-full grid grid-cols-5 p-4 items-center border-b border-gray-200">
        <div class="flex flex-col col-span-3">
          <div class="flex items-center justify-start space-x-2">
            <h2 class="font-bold text-base">
              Large
            </h2>
            <div class="text-[9px] font-bold uppercase gradient-bg text-white px-1.5 py-0.5 rounded-lg">
              POPULAR
            </div>
          </div>
          <p class="text-sm text-gray-700">
            <!-- 120 photos in large (2048x2048) format -->
            {{ photoPerStyle * 3 }} photos, large 4K (4096x4096) format and 300 dpi, perfect for printing.
          </p>
        </div>
        <div class="col-span-2 flex items-start justify-end">
          <nuxt-link :to="`/checkout/?priceId=${prices['large'].id}`" class="block">
            <ButtonDark size="sm">
              Select (${{ prices['large'].price }})
            </ButtonDark>
          </nuxt-link>
        </div>
      </div>
      <div class="w-full grid grid-cols-5 p-4 items-center border-b border-gray-200">
        <div class="flex flex-col col-span-3">
          <h2 class="text-base">
            Small
          </h2>
          <p class="text-sm text-gray-700">
            104 photos in small (512x512) format
          </p>
        </div>
        <div class="flex justify-end col-span-2">
          <nuxt-link :to="`/checkout/?priceId=${prices['small'].id}`" class="block">
            <ButtonWhite size="sm">
              Select (${{ prices['small'].price }})
            </ButtonWhite>
          </nuxt-link>
        </div>
      </div>
    </div>
  </Popup>
</template>

<script>
export default {
  data () {
    return {
      prices: {
        small: {
          price: 24,
          id: (this.$config.NODE_ENV === 'development') ? 'price_1LxoECDeLNYZ5KRAFhDctm6G' : 'price_1M0YxwDeLNYZ5KRAzO4nKh9P'
        },
        large: {
          price: 34,
          id: (this.$config.NODE_ENV === 'development') ? 'price_1Lzur6DeLNYZ5KRAnICLs5lj' : 'price_1LzuoTDeLNYZ5KRAUOBPxIOc'
        }

      }
    }
  }

}
</script>

<style>

</style>
