<template>
  <div>
    <Selector label="Outfit" :clearable="settings?.outfitId !== null" @click="showPicker = true" @clear="clearValue">
      <div v-if="settings?.outfitId" class="flex items-center justify-start gap-2">
        <ImageDns v-if="outfits.find(outfit => outfit._id === settings?.outfitId)?.image" :src="outfits.find(outfit => outfit._id === settings?.outfitId)?.image" class="w-8 h-8 object-cover rounded-md" />
        <span>
          {{ outfits.find(outfit => outfit._id === settings?.outfitId)?.type }}
        </span>
      </div>
      <div v-else-if="settings?.outfitPrompt">
        <span>
          {{ settings?.outfitPrompt }}
        </span>
      </div>
      <template v-else>
        Use default outfit
      </template>
    </Selector>
    <Popup v-if="showPicker" :size="selectedTab === 'catalog' ? '7xl' : 'xl'" :show="showPicker" @close="showPicker = false">
      <div class="space-y-2">
        <div class="flex flex-col gap-0.5">
          <Heading>Select outfit</Heading>
          <Paragraph size="sm">
            Select the outfit you want to use for the photo.
          </Paragraph>
        </div>
        <div class="flex flex-col">
          <!--<Tab class="rounded-b-none">
            <TabItem v-for="tab in tabs" :key="tab" :active="selectedTab === tab" @click="selectedTab = tab">
              {{ tab }}
            </TabItem>
          </Tab> -->
          <div class="flex flex-row">
            <button v-for="tab in tabs" :key="tab" :class="selectedTab === tab ? 'active' : ''" class="example-button bg-white rounded-t text-md px-4 py-1.5 text-black/60" @click="selectedTab = tab">
              <span class="capitalize">{{ tab }}</span>
            </button>
          </div>
          <div class="bg-[#E9E9EC] p-4 rounded-b-lg rounded-tr-lg">
            <div v-if="selectedTab === 'catalog'" class="grid grid-cols-5 gap-4 max-h-[calc(50vh)] overflow-y-scroll">
              <div v-for="outfit in outfits" :key="outfit._id" class="w-full h-full cursor-pointer hover:scale-105 transition-all duration-200 bg-white rounded-md border border-gray-200 px-2.5 py-1.5" @click="selectValue(outfit._id)">
                <!-- <span>{{ outfit.type }}</span> -->
                <ImageDns :src="outfit.image" class="w-full h-full object-cover" />
              </div>
            </div>
            <div v-if="selectedTab === 'manual'">
              <!-- <Input :value="manualOutfit" placeholder="Enter outfit prompt" label="Describe your outfit" @input="updateOutfitPrompt" /> -->
              <Input :value="outfitPrompt" placeholder="Enter outfit prompt" label="Describe your outfit" @input="updateOutfitPrompt" />
            </div>
          </div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
import Selector from '../../common/Selector.vue'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Selector,
    Heading,
    Paragraph
  },
  data () {
    return {
      showPicker: false,
      tabs: ['catalog', 'manual'],
      selectedTab: 'catalog',
      manualOutfit: ''
    }
  },
  computed: {
    settings () {
      return this.$store.state.studio.settings
    },
    gender () {
      return this.$store.state.studio.settings.gender || this.$store.state.studio.model.gender
    },
    outfits () {
      return this.$store.state.studio.outfits.filter(outfit => outfit.gender.includes(this.gender)).map((outfit) => {
        return {
          ...outfit,
          image: outfit.images[this.settings.gender]
        }
      })
    },
    outfitPrompt: {
      get () {
        return this.$store.state.studio.settings.outfitPrompt
      },
      set (value) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitPrompt', value })
      }
    }
  },
  methods: {
    selectValue (value) {
      this.showPicker = false
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitId', value })
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitPrompt', value: null })
    },
    clearValue () {
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitId', value: null })
    },
    updateOutfitPrompt (value) {
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitPrompt', value })
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitId', value: null })
    }
  }
}
</script>

<style scoped>
/deep/.example-button.active {
  @apply text-primary-500 font-bold;
  background-color: #E9E9EC;
}
/deep/.example-button:not(.active) {
  @apply text-black/60;
  background-color: #fff;
}
/deep/.example-button:not(.active):hover {
  @apply scale-105 transition-all duration-300 bg-gray-100;
}

/* Fade transition for images */
/deep/.fade-enter-active,
/deep/.fade-leave-active {
  transition: opacity 0.3s ease;
}

/deep/.fade-enter,
  /* .fade-enter-from for Vue 3 */
  /deep/.fade-leave-to
/* .fade-leave-active in <2.1.8 */
{
  opacity: 0;
}
</style>
