<template>
  <portal to="modal">
    <Popup v-if="showPopup" :show="showPopup" size="lg" @close="showPopup = false">
      <div class="flex flex-col gap-2">
        <Heading>
          <IconArrowPathRoundedSquare class="w-5 h-5  text-yellow-500" />
          <span>Credits</span>
        </Heading>
        <Paragraph size="md">
          Credits are used to generate photos in our studio. <br>You have <strong>{{ credits }} credits</strong> left.
        </Paragraph>
        <div class="flex flex-col gap-2">
          <Paragraph size="md" class="!font-bold">
            Purchase more credits
          </Paragraph>
          <div class="flex items-center gap-4">
            <InputSelect v-model="quantity" class="w-[150px]" :options="creditsOptions" @change="handleCreditsChange" />
            <span class="font-bold text-green-500">
              {{ formatPrice(pricePerCredit/100 * quantity, selectedCurrency, 2, false) }}
            </span>
            <ButtonPrimary size="sm" @click="handlePurchase">
              Purchase
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </Popup>
  </portal>
</template>

<script>
import CheckoutMixin from '@/mixins/CheckoutMixin'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Heading,
    Paragraph
  },
  mixins: [CheckoutMixin],
  data () {
    return {
      creditsOptions: [
        { title: '50 credit', value: 50 },
        { title: '100 credit', value: 100 },
        { title: '200 credits', value: 200 },
        { title: '300 credits', value: 300 },
        { title: '400 credits', value: 400 },
        { title: '500 credits', value: 500 },
        { title: '600 credits', value: 600 },
        { title: '700 credits', value: 700 },
        { title: '800 credits', value: 800 }
      ],
      quantity: 100,
      pricePerCredit: 0,
      productId: 'studiocredits'
    }
  },
  computed: {
    showPopup: {
      get () {
        return this.$store.state.studio.popups.creditModal
      },
      set (value) {
        this.$store.commit('studio/SET_POPUP_STATUS', { key: 'creditModal', value })
      }
    },
    credits () {
      return this.$store.state.user.studio.credits
    }
  },
  mounted () {
    this.fetchCreditPrices()
  },
  methods: {
    handleCreditsChange (value) {
      this.creditsToPurchase = value
    },
    async fetchCreditPrices () {
      try {
        const { success, data } = await this.$axios.$get('/checkout/package/studiocredits')
        if (!success) {
          this.$emit('close')
          throw new Error('Failed to fetch credit prices')
        }
        const { packages } = data
        this.pricePerCredit = packages?.credit?.currency[this.selectedCurrency] ?? packages?.credit?.price ?? 19
      } catch (error) {
        console.error(error)
        this.$emit('close')
      }
    },
    async handlePurchase () {
      try {
        this.$loading.show({ title: 'Redirecting to checkout...' })
        await this.pay('studiocredits')
        this.$loading.hide()
      } catch (error) {
        console.error(error)
        this.$loading.hide()
      }
    }
  }

}
</script>
