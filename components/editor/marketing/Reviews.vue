<template>
  <section id="examples" class="py-12 bg-white sm:py-16 lg:py-20 xl:py-24">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-lg font-bold text-teal-500">
          Looking for a new profile picture?
        </h2>
        <p class="mt-6 text-3xl font-medium lg:leading-[54px] tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          Get your professional headshot, <br>without physical photo shoot
        </p>
        <p class="mt-4 text-base font-normal text-gray-600">
          You're in good company. <strong>3,439,182</strong> AI headshots already created for <strong>20,691</strong> happy customers!
        </p>
      </div>
      <div class="flex items-center justify-center py-8">
        <a target="_blank" title="HeadshotPro" href="https://www.headshotpro.com">
          <ButtonPrimary>
            Get your headshots now
          </ButtonPrimary>
        </a>
      </div>
      <div class="flex items-center justify-center w-full space-x-2 text-sm animate-pulse mt-4">
        <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
        <span>These photos are not real. All of them were created with our AI.</span>
        <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
      </div>
      <div class="grid grid-cols-4 md:grid-cols-8 gap-1 mt-4">
        <ImageDns v-for="index of 16" :key="index" :src="require(`@/assets/img/examples/editor/${index + 29}.jpg`)" />
      </div>
      <EditorMarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
    </div>
  </section>
</template>

<script>
export default {
  props: {
    tweets: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    items () {
      // Get first 2
      return this.$store.state.examples.slice(0, 2)
    }
  }
  // data () {
  //   return {
  //     tweets: []
  //   }
  // },
  // async fetch () {
  //   const tweets = await this.$axios.$get('/reviews/wall-of-love/twitter', { params: { limit: 8 } })
  //   this.tweets = tweets
  // }
}
</script>

<style>

</style>
