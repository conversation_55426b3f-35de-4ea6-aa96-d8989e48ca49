<template>
  <div class="p-4">
    <div class="space-y-2">
      <InputTextArea placeholder="#profilepicturemaker" label="Insert circular text" :value="pfp.borderText" @input="updatePfp('borderText', $event)" />
      <div class="grid grid-cols-2 gap-2">
        <InputRange label="Offset" :min="0" :value="pfp?.textOffset" :max="30" @input="updatePfp('textOffset', $event)" />
        <InputRange label="Rotation" :min="-180" :value="pfp?.textRotation" :max="180" @input="updatePfp('textRotation', $event)" />
        <InputRange label="Font size" :min="0" :value="pfp?.textSize" :max="32" @input="updatePfp('textSize', $event)" />
        <InputRange label="Letter spacing" :min="-2" :value="pfp?.textLetterSpacing" :max="10" @input="updatePfp('textLetterSpacing', $event)" />
        <div class="col-span-2 bg-gray-50  p-2 flex flex-col rounded-md border border-gray-200">
          <span class="text-sm text-gray-700">Text color:</span>
          <input type="color" class="w-full rounded-md" :value="pfp?.textColor" @input="updatePfp('textColor', $event.target.value)">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {

    }
  },
  computed: {
    pfp () {
      return this.$store.state.editor.pfp
    }
  },
  methods: {
    updatePfp (key, value) {
      this.$store.commit('editor/UPDATE_PFP', { key, value })
    }
  }

}
</script>

<style>

</style>
