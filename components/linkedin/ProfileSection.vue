<template>
  <div v-if="loading || items?.length > 0">
    <p class="font-bold text-xl mt-8">
      {{ title }}
    </p>
    <div class="divide-y divide-gray-200">
      <div v-for="(item, index) in items" :key="index" class="py-4 flex">
        <div v-if="showImage" class="flex-shrink-0">
          <img src="@/assets/img/linkedin-company.svg" class="w-12 h-12">
        </div>
        <div
          :class="{
            'ml-4 w-full': showImage
          }"
        >
          <template v-if="fields.length >= 1">
            <LinkedinParagraphPlaceholder :loading="loading" class="font-bold">
              {{ item[fields[0].key] || fields[0].placeholder }}
            </LinkedinParagraphPlaceholder>
          </template>
          <template v-if="fields.length >= 2">
            <LinkedinParagraphPlaceholder :loading="loading" size="sm" class="mt-1">
              {{ item[fields[1].key] || fields[1].placeholder }}
            </LinkedinParagraphPlaceholder>
          </template>
          <template v-if="fields.length >= 3">
            <LinkedinParagraphPlaceholder :loading="loading" size="sm" class="text-gray-500 mt-1">
              {{ item[fields[2].key] || fields[2].placeholder }}
            </LinkedinParagraphPlaceholder>
          </template>
          <template v-if="fields.length >= 4">
            <LinkedinParagraphPlaceholder :loading="loading" size="sm" class="mt-4">
              {{ item[fields[3].key] || fields[3].placeholder }}
            </LinkedinParagraphPlaceholder>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    showImage: {
      type: Boolean,
      required: false,
      default: true
    },
    fields: {
      type: Array,
      required: true
    },
    items: {
      type: Array,
      required: false,
			default: () => []
    }
  }
}
</script>
