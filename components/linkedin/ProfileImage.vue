<template>
  <div v-if="isLoading" class="w-40 h-40 flex items-center justify-center border-4 border-white bg-gray-100 rounded-full mt-[-110px]">
    <svg
      class="w-24 h-24 text-gray-300"
      fill="none"
      stroke-width="1.5"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
    </svg>
  </div>
  <div v-else class="flex flex-col group mt-[-110px] md:flex-row">
    <div class="relative w-40 h-40 flex items-center justify-center border-4 border-white gradient-bg rounded-full cursor-pointer" @click="goToApp">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="w-24 h-24 text-white"
      >
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m6-6H6" />
      </svg>
    </div>
    <div class="md:ml-4 md:opacity-0 md:pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto bg-white w-64 p-4 rounded-md shadow-md text-gray-700 text-sm">
      Create your own professional headshots for LinkedIn and your CV using the most advanced AI technology.
      <ButtonPrimary size="xs" class="mt-2" @click="goToApp">
        Create now
      </ButtonPrimary>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    goToApp () {
      window.open('/app/add', '_blank')
    }
  }
}
</script>
