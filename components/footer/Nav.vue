<template>
  <div>
    <p class="text-base font-medium text-gray-900">
      {{ title }}
    </p>
    <ul class="mt-6 space-y-5">
      <li v-for="item in items" :key="item.href">
        <template v-if="item.external">
          <a :href="item.href" target="_blank" title="" class="flex text-sm text-gray-800 transition-all duration-200 hover:text-orange-600 focus:text-orange-600">
            {{ item.title }}
          </a>
        </template>
        <template v-else>
          <nuxt-link :to="item.href" title="" class="flex text-sm text-gray-800 transition-all duration-200 hover:text-orange-600 focus:text-orange-600">
            {{ item.title }}
          </nuxt-link>
        </template>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    items: {
      type: Array,
      required: true
    }
  }

}
</script>

<style>

</style>
