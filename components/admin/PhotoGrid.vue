<template>
  <div class="grid gap-2" :class="gridCols">
    <AdminPhotoGridItem
      v-for="image in images"
      :key="image.thumbnail"
      :photo="image"
      :show-resemblance-score="showResemblanceScore"
      @select="$emit('select', ...arguments)"
      @download="$emit('download', ...arguments)"
      @approve-review="$emit('approve-review', ...arguments)"
      @remove-review="$emit('remove-review', ...arguments)"
      @set-as-nsfw="$emit('set-as-nsfw', ...arguments)"
      @upscale="$emit('upscale', ...arguments)"
      @delete="$emit('delete', ...arguments)"
    />
  </div>
</template>

<script>
export default {
  props: {
    images: {
      type: Array,
      required: true
    },
    gridCols: {
      type: String,
      default: 'grid-cols-2 md:grid-cols-3 2xl:grid-cols-4'
    },
    showResemblanceScore: {
      type: Boolean,
      default: true
    }
  }
}
</script>
