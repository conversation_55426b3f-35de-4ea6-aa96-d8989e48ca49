<template>
  <div class="w-full h-full border-r border-gray-200">
    <div class="p-2 space-y-2">
      <Input :value="post.title" class="w-full" placeholder="Title" @input="updateTitle($event)" />
      <Input :value="post.excerpt" class="w-full" placeholder="Excerpt" @input="updateExcerpt($event)" />
      <Input :value="post.slug" class="w-full" placeholder="Slug. Leave empty for slug based on title" @input="updateSlug($event)" />
      <InputSelect
        :options="['blog', 'headshot-types']"
        :value="post.category"
        class="w-full"
        placeholder="Category (default is 'blog')"
        @input="updateCategory($event)"
      />
      <InputTextArea
        :rows="20"
        :value="post.content"
        class="w-full h-[calc(100vh-400px-100px)]"
        style="width: 100%;"
        @input="updateContent($event)"
      />
    </div>
    <AdminBlogMarkdownTips @click="copyString($event)" />
  </div>
</template>

<script>
export default {
  computed: {
    post () {
      return this.$store.state.admin.post
    }
  },
  methods: {
    updateTitle (event) {
      this.$store.commit('admin/SET_POST_TITLE', event)
    },
    updateExcerpt (event) {
      this.$store.commit('admin/SET_POST_EXCERPT', event)
    },
    updateSlug (event) {
      this.$store.commit('admin/SET_POST_SLUG', event)
    },
    updateContent (event) {
      this.$store.commit('admin/SET_POST_CONTENT', event)
    },
    updateCategory (event) {
      this.$store.commit('admin/SET_POST_CATEGORY', event)
    }
  }
}
</script>

<style scoped>
#editor {
  margin: 0;
  font-family: "Helvetica Neue", Arial, sans-serif;
  color: #333;
}

textarea,
#editor div {
  display: inline-block;
  vertical-align: top;
  box-sizing: border-box;
  padding: 0 20px;
}

textarea {
  border: none;
  resize: none;
  outline: none;
  background-color: #f6f6f6;
  font-size: 14px;
  font-family: "Monaco", courier, monospace;
  padding: 20px;
}

code {
  color: #f66;
}

</style>
