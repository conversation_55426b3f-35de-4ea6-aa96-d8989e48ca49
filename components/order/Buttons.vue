<template>
  <div class="grid grid-cols-3 gap-4 mt-4 w-full">
    <ButtonWhite class="w-full" @click="previousStep()">
      <IconChevron class="w-3 h-3 text-black/40 mr-2 rotate-180 flex-shrink-0" />
      <span>Previous</span>
    </ButtonWhite>
    <ButtonPrimary class="w-full col-span-2" @click="nextStep()">
      <span>{{ next }}</span>
      <IconChevron class="w-3 h-3 text-white/70 ml-2  flex-shrink-0" />
    </ButtonPrimary>
  </div>
</template>

<script>
export default {
  props: {
    next: {
      type: String,
      default: 'Next'
    }
  },
  methods: {
    previousStep () {
      this.$store.commit('onboarding/SET_ACTIVE_STEP', this.$store.state.onboarding.activeStep - 1)
    },
    nextStep () {
      this.$store.commit('onboarding/SET_ACTIVE_STEP', this.$store.state.onboarding.activeStep + 1)
    }
  }

}
</script>

<style>

</style>
