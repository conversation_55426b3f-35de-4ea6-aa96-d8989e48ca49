<template>
  <div :id="user._id" class="sticky top-0 z-20 flex items-center justify-between space-x-5 rounded-md bg-white py-2 px-4 shadow">
    <div class="flex items-start space-x-5">
      <div v-if="user?.profileImage" class="flex-shrink-0">
        <div class="relative">
          <ImageDns class="h-12 w-12 rounded-full" :src="user.profileImage" alt="">
            <span class="absolute inset-0 rounded-full shadow-inner" aria-hidden="true" />
          </ImageDns>
        </div>
      </div>

      <div class="">
        <h1 class="text-xl font-bold text-gray-900">
          {{ user.title || titleCase(username) }}
        </h1>
        <p class="-mt-1 text-sm font-medium text-gray-500">
          {{ user.email }}
        </p>
      </div>
    </div>
    <div v-if="!$route.fullPath.includes('admin')" class=" items-center justify-center text-left flex">
      <!-- && downloads < 3 && createdDaysAgo <= 2-->
      <p class="text-xs text-gray-500 hidden md:block">
        In AI-photography, not every shot is a winner. That's why we provide a variety of options. <!--Not happy?-->
      </p>
      <!-- <NuxtLink to="/profile/refund?utm_source=result-refund-button" class="ml-4">
        <ButtonDark size="sm">
          <IconSolidRefund class="w-3 h-3 text-white mr-1.5" />
          <span>Request refund</span>
        </ButtonDark>
      </NuxtLink> -->
    </div>
    <div v-if="$route.fullPath.includes('admin')" class="justify-stretch mt-6 flex flex-col-reverse items-center justify-end space-y-4 space-y-reverse sm:flex-row-reverse sm:justify-end sm:space-y-0 sm:space-x-3 sm:space-x-reverse md:mt-0 md:flex-row md:space-x-3">
      <p class="hidden flex-shrink-0 text-lg font-medium text-gray-500 md:flex">
        {{ user?.images?.length || 0 }} headshots
      </p>
      <!-- <ButtonWhite size="sm" class="group flex items-center justify-center space-x-2" @click="$emit('retry', user._id)">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="h-4 w-4"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
        </svg>
        <span class="hidden group-hover:block">Retry</span>
      </ButtonWhite> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResultUserHeader',
  props: {
    downloads: {
      type: Number,
      default: 0
    },
    user: {
      type: Object,
      default: () => {}
    },
    show: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    createdDaysAgo () {
      const created = new Date(this.user.createdAt)
      const today = new Date()
      const diffTime = Math.abs(today - created)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    },
    username () {
      return this.user.email.split('@')[0].replace(/[^a-zA-Z0-9]/g, ' ')
    }
  }
}
</script>

<style></style>
