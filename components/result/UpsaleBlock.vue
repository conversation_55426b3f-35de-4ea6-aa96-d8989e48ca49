<template>
  <div class="col-span-2 md:col-span-1 row-span-1 p-2">
    <div class="relative flex h-48 w-full flex-col items-center justify-center overflow-hidden p-4 py-8 md:h-full md:p-0">
      <img src="@/assets/img/share-bg.jpg" class="absolute z-0 h-full object-cover blur">
      <div class="absolute z-10 flex h-full w-full flex-col items-center justify-center space-y-2 bg-gradient-to-b from-yellow-500/80 to-yellow-600/80 p-8 text-center">
        <h2 class="text-xl font-bold text-primary-500">
          <template v-if="!purchasedUpsell">
            Get more headshots for just $1!
          </template>
          <template v-else>
            Get more headshots for just $5!
          </template>
        </h2>
        <nuxt-link :to="`/app/styles/${$route.params.id}`">
          <ButtonPrimary class="mt-2 block" @click="$emit('click')">
            <IconSolidUnlock class="text-white w-4 h-4 mr-1.5" />
            <span>Order now</span>
          </ButtonPrimary>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    purchasedUpsell () {
      return this.$store.state.user.purchasedUpsell || false
    }
  }
}
</script>

<style></style>
