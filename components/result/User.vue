<template>
  <div>
    <ResultUserHeader :user="user" :show="show" @toggle="show = !show" @retry="$emit('retry', $event)" />
    <MasonryGrid v-if="show">
      <template v-for="photo in user.images">
        <button :key="photo.id" v-masonry-tile :class="showSidebar ? 'w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5' : 'w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/6'" class="item group relative p-2" @click="setSelectedImage(photo.id, photo.model)">
          <ImageDns :src="photo?.thumbnail" :class="selectedImage?.thumbnail === photo?.thumbnail ? 'border-[4px] border-blue-500' : 'border-white'" class="w-full border-[4px]" @load="refreshMasonry()" />
          <div class="absolute inset-x-0 bottom-5 z-10 items-center justify-center space-x-2 group-hover:flex lg:hidden">
            <button class="rounded-md border border-white bg-white/80 p-1.5 text-gray-900" @click.stop="toggleModal(photo.image)">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="h-4 w-4"
              >
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15" />
              </svg>
            </button>
            <button class="rounded-md border border-white bg-white/80 p-1.5 text-gray-900" @click.stop="downloadSingleFromUrl(photo.image, photo.id)">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="h-4 w-4"
              >
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
              </svg>
            </button>
          </div>
        </button>
      </template>
    </MasonryGrid>
    <Modal
      v-if="showModal"
      @close="
        showModal = false;
        previewImage = null;
      "
    >
      <ImageDns :src="previewImage" class="w-full max-w-xl" />
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'ResultUser',
  props: {
    user: {
      type: Object,
      default: () => {}
    },
    showSidebar: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      showModal: false,
      previewImage: null,
      show: true
    }
  },
  computed: {
    selectedImage () {
      const userId = this.user._id
      const teamMember = this.$store.state.organization.teamMembers.find(member => member._id === userId)
      return teamMember.selectedImage
    }
  },
  watch: {
    showSidebar () {
      this.refreshMasonry()
    }
  },
  methods: {
    toggleModal (image) {
      this.showModal = true
      this.previewImage = image
    },
    downloadSingleFromUrl (url, styleId) {
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileName = `${styleId}-HeadshotPro.jpg`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
    },
    async setSelectedImage (id, modelId) {
      try {
        const { success, errorMessage } = await this.$axios.$post('/model/select-image', {
          id,
          modelId
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$store.commit('organization/SET_SELECTED_IMAGE', { userId: this.user._id, imageId: id })
      } catch (err) {
        this.handleError(err)
      }
    }
  }
}
</script>

<style></style>
