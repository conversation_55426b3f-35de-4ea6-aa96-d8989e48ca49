<template>
  <div class="flex flex-col min-h-screen bg-gray-50">
    <InviteWarningBar v-if="hasInvites && hasInvites?.length > 0 && showInviteWarning" />
    <header class="bg-white border-b border-gray-100 z-10">
      <div class="relative max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <div class="absolute inset-0 flex items-center justify-center">
          <nuxt-link to="/app">
            <Logo class="w-auto h-6" />
          </nuxt-link>
        </div>

        <div class="relative flex items-center justify-between gap-4 h-16">
          <div class="space-x-2">
            <slot name="left" />
          </div>

          <div class="flex justify-end items-center space-x-2">
            <HeaderLanguageSelector />
            <slot name="right" />
          </div>
        </div>
      </div>
    </header>
    <div
      class="flex w-full flex-col items-center justify-start antialiased flex-1"
      :class="{
        'md:items-center md:justify-center': alignment === 'center',
        'md:items-center md:justify-start': alignment === 'top',
      }"
    >
      <LoadingWrapper :is-loading="isLoading">
        <Card v-if="error" class="flex flex-col items-center justify-center mt-12">
          <div class="text-red-500 text-center flex flex-col items-center gap-2">
            <IconExclamation class="w-16 h-16" />
            <p class="text-base pb-4">
              {{ error }}
            </p>
            <ButtonWhite size="sm" @click="reload">
              Try again
            </ButtonWhite>
          </div>
        </Card>
        <div v-else>
          <slot />
        </div>
      </LoadingWrapper>
    </div>
    <portal to="modal">
      <Modal v-if="$store.state.onboarding.showRequirementsModal" max-width="max-w-3xl" @close="closeRequirementsModal">
        <PostcheckoutRequirements />
      </Modal>
    </portal>
  </div>
</template>

<script>
export default {
  props: {
    backUrl: {
      type: String,
      default: null
    },
    alignment: {
      type: String,
      default: 'center'
    },
    skip: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isLoading: true,
      error: null
    }
  },
  computed: {
    availableLocales () {
      return this.$i18n.locales
    }
  },
  mounted () {
    if (this.$store.state.onboarding.modelId) {
      this.isLoading = false
      return
    }

    if (this.skip) {
      this.isLoading = false
    } else {
      this.$store.dispatch('onboarding/fetchModelInformation')
        .then(() => {
          this.isLoading = false
          if (this.$store.getters['onboarding/hasEnoughTotalPhotos']) {
            if (this.$store.state.onboarding.title) {
              this.$router.push({ path: this.localePath('/app/upload/select-style') })
            } else {
              this.$router.push({ path: this.localePath('/app/upload/personal-info') })
            }
          } else if (this.$store.state.onboarding.photos.length > 0) {
            this.$router.push({ path: this.localePath('/app/upload/photos') })
          } else {
            this.$router.push({ path: this.localePath('/app/upload/intro') })
          }
        }).catch((error) => {
          this.error = 'There was an error fetching the model information. Please, check your internet connection and try again.'
          this.isLoading = false
          this.handleError(error)
        })
    }
  },
  methods: {
    closeRequirementsModal () {
      this.$store.commit('onboarding/SET_SHOW_REQUIREMENTS_MODAL', false)
    },
    reload () {
      window.location.reload()
    }
  }
}
</script>
