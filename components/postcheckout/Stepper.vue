<template>
  <div :class="`flex items-center ${size === 'large' ? 'gap-4' : 'gap-2'} ${classes}`">
    <div
      v-for="i in (step - 1)"
      :key="`step-${i}`"
      class="h-2 bg-[#34C759] rounded-full"
      :class="{
        'w-10': size === 'large',
        'w-6': size === 'small',
      }"
    />
    <div
      :key="`active-step-${step}`"
      class="h-2 rounded-full"
      :class="{
        'w-10': size === 'large',
        'w-6': size === 'small',
        [active]: true,
      }"
    />
    <div
      v-for="i in maxSteps - step"
      :key="`uncompleted-step-${i}`"
      class="h-2 bg-[#EAE8FF] rounded-full"
      :class="{
        'w-10': size === 'large',
        'w-6': size === 'small',
      }"
    />
  </div>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'large'
    },
    step: {
      type: Number,
      required: true
    },
    classes: {
      type: String,
      default: ''
    },
    maxSteps: {
      type: Number,
      default: 5
    },
    active: {
      type: String,
      default: 'bg-[#34C759]'
    }
  }
}
</script>
