<template>
  <Modal max-width="max-w-3xl" @close="$emit('closeModal')">
    <div class="px-4 py-5 sm:p-6">
      <div class="space-y-1">
        <p class="text-lg font-bold tracking-tight text-primary-500">
          {{ $t('Which outfit do you want to wear?') }}
        </p>
        <p class="text-base text-gray-500" v-html="$t('matchYour', { title: styleItem.title.toLowerCase() })" />
        <!-- <p v-if="!isTeamMember" class="text-sm text-gray-500">
          {{ $t('Missing an outfit? Request a custom one') }} <a href="https://tally.so/r/mR857v" target="_blank" class="text-primary-500 underline">{{ $t('here') }}</a>.
        </p> -->
      </div>

      <div class="grid grid-cols-2 mt-5 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-5">
        <div v-for="(clothe, index) in clothingForGender" :key="`clothe-${clothe._id}-${index}`" class="space-y-2 group hover:scale-105 transition" data-testid="clothing-item">
          <div class="cursor-pointer relative overflow-hidden rounded-lg" @click="selectClothes(clothe)">
            <span
              v-if="clothe.mostPopular && !isTeamMember"
              class="absolute text-[8px] px-2 py-0.5 font-extrabold tracking-wide text-white uppercase bg-rose-500 rounded-full top-2 right-2"
            >
              {{ $t('Favorite') }}
            </span>
            <img class="object-cover w-full h-full" :src="clotheImage(clothe)" alt="">

            <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/10 to-transparent group-hover:hidden" />

            <div class="absolute inset-x-0 bottom-0 flex items-center justify-center p-4 group-hover:hidden">
              <p class="text-xs font-medium tracking-tight text-white">
                {{ clothe.type }}
              </p>
            </div>
          </div>

          <button
            type="button"
            class="text-sm w-full font-medium text-primary-500 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 pt-1.5 pb-2 px-2.5 hidden sm:inline-flex items-center gap-1.5 justify-center"
            @click="selectClothes(clothe)"
          >
            {{ $t('Select') }}
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
              <path
                d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
import ModelPersonalizationMixin from '@/mixins/ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  props: {
    styleItem: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      modalOpen: false
    }
  },
  computed: {
    gender () {
      return this.$store.state.results.item.trigger || this.$store.state.onboarding.selectedSex
    },
    clothingItems () {
      let filteredClothing = this.$store.state.clothing

      if (this.isTeamMember && this.$store.state?.organization?.organization.allowedOptions?.clothing) {
        const allowedClothingIds = [...this.$store.state?.organization?.organization.allowedOptions?.clothing?.male, ...this.$store.state?.organization?.organization.allowedOptions?.clothing?.female]
        filteredClothing = allowedClothingIds.map(id => filteredClothing.find(clothing => clothing._id === id))
      }
      return filteredClothing
    }
  },
  methods: {
    selectClothes (item) {
      this.$emit('select', item._id)
      this.$emit('closeModal')
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Which outfit do you want to wear?": "Which outfit do you want to wear?",
      "matchYour": "Match your <span class=\"font-medium text-primary-500\">{title} background</span> with an outfit you want to wear with.",
      "Favorite": "Favorite",
      "Select": "Select",
      "Missing an outfit? Request a custom one": "Missing an outfit? Request a custom one",
      "here": "here"
    },
    "es": {
      "Which outfit do you want to wear?": "¿Qué ropa quieres usar?",
      "matchYour": "Combina tu <span class=\"font-medium text-primary-500\">fondo {title}</span> con una prenda que quieras usar.",
      "Favorite": "Favorito",
      "Select": "Seleccionar",
      "Missing an outfit? Request a custom one": "¿Falta un outfit? Solicita uno personalizado",
      "here": "aquí"
    },
    "de": {
      "Which outfit do you want to wear?": "Welches Outfit möchtest du tragen?",
      "matchYour": "Kombiniere deinen <span class=\"font-medium text-primary-500\">{title} Hintergrund</span> mit einem Outfit, das du tragen möchtest.",
      "Favorite": "Favorit",
      "Select": "Auswählen",
      "Missing an outfit? Request a custom one": "Outfit fehlt? Beantrage ein individuelles",
      "here": "hier"
    }
  }
</i18n>
