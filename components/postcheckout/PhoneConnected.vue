<template>
  <div class="px-4 py-3 border border-green-200 rounded-lg bg-green-50">
    <div class="flex items-center gap-4">
      <div class="inline-flex items-center justify-center bg-green-200 rounded-full size-8 shrink-0">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          class="text-green-400 size-6"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z"
            clip-rule="evenodd"
          />
        </svg>
      </div>

      <div class="flex-1 min-w-0">
        <p class="text-base font-medium tracking-tight text-green-800">
          {{ $t('Phone connected') }}
        </p>
        <p class="text-sm text-green-800 mt-0.5">
          {{ $t('You can start taking pictures now') }}
        </p>
      </div>
    </div>
  </div>
</template>

<i18n>
  {
    "en": {
      "Phone connected": "Phone connected",
      "You can start taking pictures now": "You can start taking pictures now"
    },
    "es": {
      "Phone connected": "Teléfono conectado",
      "You can start taking pictures now": "Puedes empezar a tomar fotos ahora"
    },
    "de": {
      "Phone connected": "Handy verbunden",
      "You can start taking pictures now": "Du kannst jetzt Fotos aufnehmen"
    }
  }
</i18n>
