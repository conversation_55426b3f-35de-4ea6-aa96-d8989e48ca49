<template>
  <button class="relative block" type="button" @click="updateValue">
    <div
      class="relative px-2.5 pt-1.5 pb-2 cursor-pointer bg-white rounded-md ring-1 transition"
      :class="{
        'text-teal-500 ring-teal-500': value === identifier,
        'text-gray-500 ring-gray-200': value !== identifier
      }"
    >
      <div class="flex items-center gap-1.5">
        <slot />
        <p class="text-sm font-medium">
          {{ text }}
        </p>
      </div>
    </div>
  </button>
</template>

<script>
export default {
  props: {
    identifier: {
      type: String,
      required: true
    },
    text: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    }
  },
  methods: {
    updateValue () {
      this.$emit('input', this.identifier)
    }
  }
}
</script>
