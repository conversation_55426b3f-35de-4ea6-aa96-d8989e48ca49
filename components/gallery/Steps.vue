<template>
  <div class="flex items-center justify-center">
    <div class="flex items-center justify-center">
      <template v-for="step in steps">
        <div
          :key="'circle-' + step.id"
          class="w-4 h-4 rounded-full border-[1.5px] flex items-center justify-center relative"
          :class="{
            'bg-[#0EB567] border-[#0EB567]': step.id < currentStep,
            'bg-white border-primary-500': step.id === currentStep,
            'bg-white border-[#D4D4D8]': step.id > currentStep
          }"
        >
          <div v-if="step.id === currentStep" class="w-[6px] h-[6px] rounded-full bg-primary-500" />
          <IconCheck v-else class="w-4 h-4 text-white" />
          <span
            class="absolute -bottom-6 text-xs w-[80px] xs:w-[120px] md:w-[140px] text-center left-auto right-auto mx-auto md:hidden"
            :class="{
              'text-primary-500 font-bold': step.id === currentStep,
              'text-paragraph': step.id !== currentStep,
            }"
          >
            {{ step.mobileLabel }}
          </span>
          <span
            class="absolute -bottom-6 text-xs w-[80px] xs:w-[120px] md:w-[140px] text-center left-auto right-auto mx-auto hidden md:block"
            :class="{
              'text-primary-500 font-bold': step.id === currentStep,
              'text-paragraph': step.id < currentStep,
              'text-[#A1A1AA]': step.id > currentStep,
            }"
          >
            {{ step.label }}
          </span>
        </div>
        <div
          v-if="step !== steps[steps.length - 1]"
          :key="'line-' + step.id"
          class="w-[80px] xs:w-[120px] md:w-[140px] h-[1.5px]"
          :class="{
            'bg-[#0EB567]': step.id < currentStep,
            'bg-[#D4D4D8]': step.id >= currentStep,
          }"
        />
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentStep: {
      type: Number,
      required: false,
      default: 1
    },
    steps: {
      type: Array,
      required: false,
      default: () => [
        { id: 1, label: 'Create your headshots', mobileLabel: 'Headshots' },
        { id: 2, label: 'Pick your keepers', mobileLabel: 'Pick keepers' },
        { id: 3, label: 'Your results', mobileLabel: 'Results' }
      ]
    }
  }
}
</script>
