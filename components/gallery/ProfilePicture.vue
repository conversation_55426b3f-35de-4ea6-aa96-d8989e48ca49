<template>
  <div
    class="transition-opacity duration-300  w-[48px] h-[48px] rounded-full overflow-hidden border-2 border-white"
    :style="profileStyle"
  >
    <ImageDns :src="pfpUrl" class="w-full h-full object-cover" />
  </div>
</template>

<script>
export default {
  props: {
    pfpUrl: {
      type: String,
      required: true
    },
    branding: {
      type: Object,
      required: true,
      default: () => ({})
    },
    photoId: {
      type: String,
      required: true
    }
  },

  computed: {
    profileStyle () {
      if (this.branding?.backgroundColor) {
        return {
          backgroundColor: this.branding?.backgroundColor
        }
      }
      if (this.branding?.backgroundImage) {
        return {
          background: this.branding?.backgroundImage ? `url(${this.branding?.backgroundImage}) no-repeat center center` : null,
          backgroundSize: 'cover'
        }
      }
      return {
        backgroundColor: '#fff'
      }
    }
  },

  methods: {
    downloadPfp () {
      try {
        if (!this.pfpUrl) {
          return
        }
        this.$loading.show({
          title: 'Downloading PFP...'
        })

        // Create a canvas to merge the layers
        const canvas = document.createElement('canvas')
        const size = 600 // Standard size for PFP
        canvas.width = size
        canvas.height = size
        const ctx = canvas.getContext('2d')

        if (this.branding?.backgroundImage) {
          // Load and draw the background image
          const bgImage = new Image()
          bgImage.crossOrigin = 'anonymous'
          bgImage.onload = () => {
            // Draw background image
            ctx.drawImage(bgImage, 0, 0, size, size)

            // Load and draw the PFP layer
            const pfpImage = new Image()
            pfpImage.crossOrigin = 'anonymous'
            pfpImage.onload = () => {
              ctx.drawImage(pfpImage, 0, 0, size, size)

              // Convert to blob and download
              canvas.toBlob((blob) => {
                const url = window.URL.createObjectURL(blob)
                const link = document.createElement('a')
                link.href = url
                const fileName = `${this.photoId}-HeadshotPro.png`
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                window.URL.revokeObjectURL(url)
                this.$loading.hide()
              }, 'image/png')
            }
            pfpImage.src = this.pfpUrl
          }
          bgImage.src = this.branding.backgroundImage
        } else {
          // Draw solid background color
          ctx.fillStyle = this.branding?.backgroundColor || '#000'
          ctx.fillRect(0, 0, size, size)

          // Load and draw the transparent layer
          const pfpImage = new Image()
          pfpImage.crossOrigin = 'anonymous'
          pfpImage.onload = () => {
            ctx.drawImage(pfpImage, 0, 0, size, size)

            // Convert to blob and download
            canvas.toBlob((blob) => {
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              const fileName = `${this.photoId}-HeadshotPro.png`
              link.setAttribute('download', fileName)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              window.URL.revokeObjectURL(url)
              this.$loading.hide()
            }, 'image/png')
          }
          pfpImage.src = this.pfpUrl
        }
      } catch (error) {
        this.$loading.hide()
        this.$toast.error('Failed to download PFP.')
      }
    }
  }
}
</script>
