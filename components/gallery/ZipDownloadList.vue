<template>
  <div class="mt-6 border-t pt-4">
    <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center justify-between">
      <span class="hidden md:block">Ready to download ({{ zipFiles.length }} ZIP files)</span>
      <span class="block md:hidden">Ready ({{ zipFiles.length }} files)</span>
      <div v-if="zipFiles.length > 3" class="text-xs text-gray-500 flex items-center">
        <IconChevronDown class="w-3 h-3 mr-1" />
        Scroll for more
      </div>
      <div class="justify-center hidden md:flex">
        <!-- Success message or download all button -->
        <div v-if="downloadAllSuccess" class="flex items-center text-green-600">
          <IconCheck class="w-5 h-5 mr-2" />
          <span class="text-base font-medium">All files downloaded!</span>
        </div>
        <ButtonWhite v-else size="sm" @click="handleDownloadAllClick">
          <IconCloudDownload class="w-4 h-4 mr-2" />
          Download All
        </ButtonWhite>
      </div>
    </h4>

    <!-- Scrollable container with visual indicators -->
    <div class="relative">
      <!-- Top fade indicator -->
      <div
        v-if="showTopFade"
        class="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white to-transparent z-10 pointer-events-none"
      />

      <!-- Scrollable list -->
      <div
        ref="scrollContainer"
        class="space-y-2 max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-2"
        @scroll="handleScroll"
      >
        <div
          v-for="(zipData, index) in zipFiles"
          :key="index"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ zipData.filename }}
            </p>
            <p class="text-xs text-gray-500">
              {{ formatFileSize(zipData.size) }}
            </p>
          </div>

          <!-- Success message or download button -->
          <div v-if="downloadSuccessStates[index]" class="ml-3 flex-shrink-0 flex items-center text-green-600">
            <IconCheck class="w-4 h-4 mr-1" />
            <span class="text-sm font-medium">Downloaded!</span>
          </div>
          <ButtonWhite
            v-else
            size="sm"
            class="ml-3 flex-shrink-0"
            @click="handleDownloadClick(zipData, index)"
          >
            <IconCloudDownload class="w-4 h-4 mr-1" />
            Download
          </ButtonWhite>
        </div>
      </div>

      <!-- Bottom fade indicator -->
      <div
        v-if="showBottomFade"
        class="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white to-transparent z-10 pointer-events-none"
      />

      <!-- Scroll indicator -->
      <div v-if="zipFiles.length > 3" class="text-center mt-2">
        <div class="inline-flex items-center text-xs text-gray-400">
          <div class="w-1 h-1 bg-gray-300 rounded-full mr-1" :class="{ 'bg-primary-500': showTopFade }" />
          <div class="w-1 h-1 bg-gray-300 rounded-full mr-1" :class="{ 'bg-primary-500': !showTopFade && !showBottomFade }" />
          <div class="w-1 h-1 bg-gray-300 rounded-full" :class="{ 'bg-primary-500': showBottomFade }" />
        </div>
      </div>
    </div>

    <!-- Download all button -->
  </div>
</template>

<script>
export default {
  name: 'ZipDownloadList',
  props: {
    zipFiles: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      showTopFade: false,
      showBottomFade: false,
      downloadSuccessStates: {},
      downloadAllSuccess: false,
      timeouts: []
    }
  },
  watch: {
    zipFiles: {
      handler () {
        // Reset success states when zipFiles change
        this.downloadSuccessStates = {}
      },
      immediate: true
    }
  },
  beforeDestroy () {
    // Clear any pending timeouts
    this.timeouts.forEach(timeout => clearTimeout(timeout))
  },
  mounted () {
    this.$nextTick(() => {
      this.checkScrollability()
    })
  },
  updated () {
    this.$nextTick(() => {
      this.checkScrollability()
    })
  },
  methods: {
    formatFileSize (bytes) {
      if (bytes === 0) { return '0 Bytes' }
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    handleScroll () {
      this.checkScrollability()
    },
    checkScrollability () {
      const container = this.$refs.scrollContainer
      if (!container) { return }

      const { scrollTop, scrollHeight, clientHeight } = container

      this.showTopFade = scrollTop > 10
      this.showBottomFade = scrollTop < scrollHeight - clientHeight - 10
    },
    handleDownloadClick (zipData, index) {
      // Emit the download event
      this.$emit('download-zip', zipData)

      // Show success state
      this.$set(this.downloadSuccessStates, index, true)

      // Reset after 5 seconds
      const timeout = setTimeout(() => {
        this.$set(this.downloadSuccessStates, index, false)
      }, 5000)

      this.timeouts.push(timeout)
    },
    handleDownloadAllClick () {
      // Emit the download all event
      this.$emit('download-all')

      // Show success state
      this.downloadAllSuccess = true

      // Reset after 5 seconds
      const timeout = setTimeout(() => {
        this.downloadAllSuccess = false
      }, 5000)

      this.timeouts.push(timeout)
    }
  }
}
</script>
