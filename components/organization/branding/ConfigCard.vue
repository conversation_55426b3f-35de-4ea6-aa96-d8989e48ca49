<template>
  <div class="flex flex-col gap-4 relative">
    <template v-if="showHeader && !activeBackgroundColor && !activeBackgroundImage">
      <img src="@/assets/img/branding/preview.png" class="w-[250px] mx-auto">
      <div class="absolute flex-col items-center hidden gap-1 top-[-10px] right-[-30px] xl:flex">
        <p
          class="text-sm leading-4 rotate-[-11deg] py-4 text-right font-cursive text-paragraph tracking-[-0.056px]"
        >
          An example of a branded<br>
          profile picture
        </p>
        <svg
          class="w-auto h-10 text-paragraph absolute top-[38px] left-[8px]"
          width="24"
          height="25"
          viewBox="0 0 47 25"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M4.81908 19.3609C5.9942 20.8884 7.11094 22.3908 8.40942 24.0735C7.38964 24.4198 6.49228 24.0004 5.88212 23.3292C4.05163 21.3158 2.23622 19.2406 0.582761 17.0305C-0.367317 15.7701 0.109152 14.6109 1.46414 14.4767C3.72026 14.218 6.00463 14.1078 8.30406 13.9359C8.43589 13.9243 8.60445 13.8944 8.68448 13.9628C9.01969 14.175 9.30312 14.4671 9.62326 14.741C9.43117 14.9995 9.3257 15.4315 9.08371 15.4981C8.24755 15.7528 7.4048 15.9024 6.58371 16.0953C6.13644 16.2101 5.70423 16.2632 5.22494 16.7734C5.88878 17.0926 6.57427 17.4551 7.23811 17.7742C21.1326 23.7403 35.3256 19.3673 42.709 6.95583C43.6497 5.3481 44.3936 3.62178 45.2542 1.94559C45.5584 1.36018 45.8842 0.818157 46.1883 0.232744C46.3418 0.264552 46.4802 0.358081 46.6337 0.389889C46.7185 0.835425 46.9416 1.37449 46.8145 1.76315C46.4614 3.07764 46.1083 4.39213 45.58 5.63142C40.3708 17.9877 27.8529 24.6143 14.3228 22.0438C11.7352 21.5464 9.17311 20.5486 6.62415 19.7609C6.08367 19.597 5.59497 19.3531 5.06955 19.1275C4.97445 19.1208 4.88593 19.2191 4.81908 19.3609Z" fill="#807D96" />
        </svg>
      </div>
    </template>
    <Card inner-class="space-y-[10px]">
      <div class="flex flex-col">
        <h3 class="text-base font-bold text-primary-500">
          {{ title }}
        </h3>
        <p class="text-gray-500 text-sm mt-1">
          {{ subtitle }}
        </p>
      </div>
      <template v-if="activeBackgroundColor || activeBackgroundImage">
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
          <OrganizationBrandingSelectedPreview
            :background-color="activeBackgroundColor"
            :background-image="activeBackgroundImage"
          />
          <ButtonDelete size="sm" @click="handleDelete">
            Remove
          </ButtonDelete>
        </div>
      </template>
      <template v-else>
        <OrganizationBrandingUploadButton
          text="Upload custom background"
          mobile-text="Upload background"
          :recommended="true"
          @click="$refs.uploadModal.open()"
        >
          <IconCloudArrowUp class="w-5 h-5 text-paragraph" />
        </OrganizationBrandingUploadButton>
        <OrganizationBrandingUploadButton
          text="Pick a background color"
          mobile-text="Pick color"
          @click="$refs.colorModal.open()"
        >
          <IconSwatch class="w-5 h-5 text-paragraph" />
        </OrganizationBrandingUploadButton>
      </template>
    </Card>
    <OrganizationBrandingPickColorModal
      ref="colorModal"
      :start-color="selectedColor"
      @save="handleColorSave"
    />
    <OrganizationBrandingUploadImageModal
      ref="uploadModal"
      :is-uploading="isUploading"
      @upload="handleImageUpload"
    />
  </div>
</template>

<script>
export default {
  name: 'BrandingConfigCard',
  props: {
    showHeader: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: 'Background'
    },
    subtitle: {
      type: String,
      default: 'Select the background you want to use for your teams profile pictures.'
    }
  },
  data () {
    return {
      selectedColor: '#00CEF3',
      isUploading: false
    }
  },
  computed: {
    activeBackgroundColor () {
      return this.$store.state.organization?.organization?.branding?.backgroundColor || null
    },
    activeBackgroundImage () {
      return this.$store.state.organization?.organization?.branding?.backgroundImage || null
    }
  },
  methods: {
    async handleColorSave (color) {
      this.$loading.show({
        title: 'Saving color...'
      })

      try {
        await this.$axios.$post('/organization/branding/color', {
          color
        })
        this.$toast.success('Color saved')
        this.$store.commit('organization/SET_BRANDING_COLOR', color)
      } catch (err) {
        this.$toast.error('Error saving color')
        console.error(err)
      } finally {
        this.$refs.colorModal.close()
        this.$loading.hide()
      }
    },
    handleDelete () {
      this.$loading.show({
        title: 'Deleting branding...'
      })
      this.$axios.$delete('/organization/branding').then(() => {
        this.$toast.success('Branding deleted')
        this.$store.commit('organization/SET_BRANDING', null)
      }).catch((err) => {
        this.$toast.error('Error deleting branding')
        console.error(err)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    async handleImageUpload (file) {
      this.isUploading = true
      this.$loading.show({
        title: 'Uploading background...'
      })

      try {
        const formData = new FormData()
        formData.append('background', file)

        const { success, data } = await this.$axios.$post('/organization/branding/background', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (!success) {
          this.isUploading = false
          this.$loading.hide()
          throw new Error('Failed to upload background')
        }

        this.$store.commit('organization/SET_BRANDING_BACKGROUND', data.backgroundImage)

        this.$toast.success('Background uploaded successfully')
        this.$refs.uploadModal.close()
      } catch (error) {
        console.error('Upload error:', error)
        this.$toast.error('Failed to upload background')
      } finally {
        this.isUploading = false
        this.$loading.hide()
      }
    }
  }
}
</script>
