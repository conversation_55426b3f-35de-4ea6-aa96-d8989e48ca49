<template>
  <div
    class="relative p-4 transition-all duration-200 bg-white border border-gray-200 rounded-lg shadow-sm hover:translate-x-1 hover:bg-gray-50"
    role="button"
    @click="$emit('click')"
  >
    <div class="flex items-center justify-between gap-3 sm:gap-4">
      <div
        class="inline-flex items-center justify-center sm:border sm:border-gray-200 sm:rounded-lg size-6 sm:size-12 sm:bg-gray-50 shrink-0"
      >
        <slot />
      </div>

      <p class="text-base font-bold leading-tight tracking-tight text-primary-500">
        <span class="sm:hidden">
          {{ mobileText }}
        </span>
        <span class="hidden sm:block">
          {{ text }}
        </span>
      </p>

      <!-- <span
        v-if="recommended"
        class="px-2.5 py-1 text-xs font-extrabold tracking-wide text-white uppercase bg-teal-500 rounded-full"
      >
        <span class="sm:hidden">
          Recommended
        </span>
        <span class="hidden sm:block">
          Advised
        </span>
      </span> -->
      <div v-if="recommended" class="hidden xs:block bg-[#21B8BA] rounded-full px-3 py-1 text-[10px] font-bold uppercase text-white">
        {{ $t('Recommended') }}
      </div>

      <span
        class="inline-flex items-center gap-1 ml-auto text-sm font-medium text-gray-500 transition-all duration-150 hover:text-gray-700"
      >
        <span class="hidden sm:inline-flex">
          {{ $t('Select') }}
        </span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          class="size-5 -mb-0.5"
        >
          <path
            fill-rule="evenodd"
            d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
            clip-rule="evenodd"
          />
        </svg>
        <span class="absolute inset-0" aria-hidden="true" />
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    recommended: {
      type: Boolean,
      default: false
    },
    mobileText: {
      type: String,
      default: () => this.$t('Start capturing selfies')
    },
    text: {
      type: String,
      default: () => this.$t('Capture new selfies with your phone')
    }
  }
}
</script>

<style>

</style>
