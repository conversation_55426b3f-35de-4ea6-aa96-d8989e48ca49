<template>
  <div :id="item.id" class="">
    <p class="text-xl font-medium" :class="{'font-bold text-black':item.bold === 'true', 'text-gray-900': item?.bold !== 'true'}">
      {{ item.question }}
    </p>
    <p class="mt-3 text-base text-gray-600" v-html="item.answer" />
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  }

}
</script>

<style>

</style>
