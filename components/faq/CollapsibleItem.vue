<template>
  <div :id="item.id" class="bg-white rounded-md border border-black/10">
    <div class="flex items-center justify-between cursor-pointer px-4 py-3" @click="toggleOpen">
      <p class="text-base font-medium text-paragraphlp">
        {{ item.question }}
      </p>
      <div v-if="open" class="w-[13px] h-[1.5px] rounded-full bg-paragraph flex-shrink-0" />
      <IconPlus v-else class="text-paragraph flex-shrink-0" />
    </div>
    <div v-if="open" class="px-4 pb-3">
      <div class="h-px bg-black/5" />
      <p class="mt-3 text-base text-paragraph" v-html="item.answer" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      open: false
    }
  },
  methods: {
    toggleOpen () {
      this.open = !this.open
    }
  }
}
</script>
