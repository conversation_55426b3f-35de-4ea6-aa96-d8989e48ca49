<template>
  <div v-if="!isLoading">
    <slot />
  </div>
  <div v-else class="bg-gray-200 shadow rounded-md p-4 animate-pulse">
    <div class=" flex space-x-4">
      <div class="rounded-full bg-slate-700 h-10 w-10" />
      <div class="flex-1 space-y-6 py-1">
        <div class="h-2 bg-slate-700 rounded" />
        <div class="space-y-3">
          <div class="grid grid-cols-3 gap-4">
            <div class="h-2 bg-slate-700 rounded col-span-2" />
            <div class="h-2 bg-slate-700 rounded col-span-1" />
          </div>
          <div class="h-2 bg-slate-700 rounded" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingWrapper',
  props: {
    isLoading: {
      type: Boolean,
      required: true,
      default: false
    },
    title: {
      type: String,
      required: false,
      default: 'Loading...'
    }
  }
}
</script>

<style>

</style>
