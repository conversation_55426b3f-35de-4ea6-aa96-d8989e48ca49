<template>
  <Card data-testid="upsell-result-card" class="@container xs:aspect-result">
    <div v-if="upsellPhotos.length > 0" class="flex items-center justify-start -space-x-3">
      <template v-for="photo in upsellPhotos">
        <ImageDns :key="photo._id" :src="photo.thumbnail || photo.image" class="w-8 h-8 rounded-full shadow-upsell object-cover" />
      </template>
    </div>
    <p class="text-[15px] text-black font-bold mt-2 @[150px]:mt-2">
      Unlock more photos
    </p>
    <p class="text-sm text-black/45 line-clamp-2 overflow-ellipsis mt-4 @[150px]:mt-1 @[190px]:mt-2">
      Loved your photos? Unlock more with any backdrop and outfit combination you like.
    </p>
    <ButtonWhite class="w-full mt-4 @[150px]:mt-1 @[190px]:mt-2 @[210px]:mt-3 @[220px]:mt-4" size="sm" @click="goToUpsell">
      Unlock now
    </ButtonWhite>
  </Card>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    canUseStudio () {
      // return false
      // Dont show on mobile
      if (this.screenWidth < 768) { return false }
      if (this.isTeamMember) { return false }
      if (this.$store?.state?.results?.item?.images?.filter(image => image.likedStatus === 'favorite').length < 4) {
        return false
      }
      if (this.item?.meta?.canUseStudio) {
        return true
      }
      return false
    },
    gender () {
      return this.item?.trigger || 'male'
    },
    activePhotos () {
      return this.item?.images?.filter(photo => !photo?.likedStatus || photo?.status === 'active') || []
    },
    favoritePhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'favorite') || []
    },
    dislikedPhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'dud') || []
    },
    upsellPhotos () {
      return [...this.favoritePhotos, ...this.dislikedPhotos].slice(0, 3)
    }
  },
  methods: {
    goToUpsell () {
      this.$router.push(`/app/results/${this.item._id}/unlock-more?step=1`)
    }
  }
}
</script>
