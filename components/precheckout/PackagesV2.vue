<template>
  <div class="py-8">
    <section>
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
        <div class="relative max-w-2xl mx-auto text-left md:text-center">
          <PrecheckoutStepCounter :active="3" :total="4" />
          <h2
            class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500 max-w-xl mx-auto"
          >
            {{ $t('Select your package') }}
          </h2>
          <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg">
            {{ $t('One-time payment, no subscriptions, 100% money back guarantee.') }}
          </p>

          <div class="absolute flex-col items-center hidden gap-1 top-0 -right-24 xl:flex">
            <p
              class="text-base leading-4 rotate-[18deg] py-4 text-right font-cursive text-paragraph tracking-[-0.056px]"
            >
              {{ $t("We won't let you leave") }}<br>
              {{ $t('without good headshots') }}
            </p>
            <svg
              class="w-auto h-10 text-paragraph"
              viewBox="0 0 50 38"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.73613 31.9344C9.78808 33.6973 11.7607 35.4378 14.0258 37.3792C12.9909 37.9954 11.7571 37.608 10.7461 36.8492C7.71298 34.5728 4.66991 32.2147 1.75552 29.6559C0.085201 28.198 0.123864 26.6299 1.64443 26.2296C4.16052 25.5179 6.7758 24.9921 9.38111 24.3845C9.52971 24.3474 9.71296 24.2807 9.83685 24.3551C10.3225 24.5709 10.7833 24.8983 11.2789 25.1959C11.1701 25.56 11.2399 26.1324 10.9873 26.2585C10.1256 26.7261 9.20938 27.0599 8.33774 27.4458C7.86727 27.6684 7.38684 27.8093 7.05539 28.5449C7.97202 28.8428 8.93327 29.1928 9.8499 29.4907C28.7172 34.8107 43.321 26.8091 46.3967 9.63548C46.7767 7.41352 46.8744 5.07245 47.1306 2.77608C47.2242 1.97347 47.3625 1.22292 47.4561 0.420307C47.6493 0.435309 47.8525 0.532069 48.0457 0.547071C48.3432 1.1047 48.844 1.75908 48.869 2.27935C49.0435 4.02604 49.218 5.77273 49.1546 7.45236C48.5902 24.1893 36.9454 34.8016 20.0177 33.7789C16.7778 33.5759 13.3443 32.7262 10.02 32.1441C9.31644 32.0247 8.63763 31.7938 7.92417 31.5927C7.81024 31.6 7.75087 31.7412 7.73613 31.9344Z"
              />
            </svg>
          </div>
        </div>

        <div class="gap-4 mt-4">
          <CurrencyToggle />
          <PrecheckoutPricingTiers :key="currencyKey" class="mt-8" recommended="78% pick this option" />
        </div>

        <div class="flex flex-row justify-start md:justify-end items-start md:items-center gap-3 w-full mt-6">
          <TrustpilotRating class="md:mx-auto" />
        </div>

        <!-- <div class="max-w-2xl mx-auto mt-8">
          <MarketingLogoCloud />
        </div> -->

        <div class="mt-4 flex flex-col items-start md:items-center justify-start md:justify-center gap-4 sm:mt-6  sm:gap-4">
          <div class="flex flex-col items-start md:items-center gap-2">
            <p class="hidden md:flex -mt-0.5 text-sm font-normal text-[#474368]/80 gap-0.5">
              <span class="hidden md:inline-flex font-bold">18M</span>
              <span class="hidden md:inline-flex"> {{ $t('headshots created for') }}</span>
              <span class="font-bold">{{ $store.state.stats.users }}+</span>
              {{ $t('happy customers') }}
            </p>

            <div class="max-w-[100%] w-full md:w-[600px] mx-auto overflow-hidden relative">
              <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#F8FCFF] md:from-[#F8FCFF] to-transparent z-10" />
              <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#F8FCFF] md:from-[#F8FCFF] to-transparent z-10" />
              <img
                src="@/assets/img/logo-cloud-horizontal.png"
                class="w-[240%] max-w-[240%] grayscale opacity-60 animate-scroll"
              >
            </div>
          </div>
        </div>

        <!-- <div class="hidden md:grid md:grid-cols-3 gap-4 mt-8">
          <LandingpageRandomTrustpilotReview v-for="i in 9" :key="i" />
        </div> -->
        <div
          ref="masonryContainer"
          v-masonry
          transition-duration="0.1s"
          stagger="0s"
          item-selector=".item"
          class="w-full mt-4"
        >
          <template v-for="item in visibleReviews">
            <div :key="item.id || item._id" v-masonry-tile class="item w-full p-2 sm:w-1/2 lg:w-1/3 xl:w-1/4">
              <MarketingReviewItem
                v-if="item.type === 'review'"
                :key="item._id"
                :item="item"
                :badge="isCreatedAfterAugust21(item.createdAt) ? 'Updated model' : ''"
                @load="refreshMasonry"
              />
              <LandingpageV2ReviewTrustpilot
                v-else
                :key="item.id"
                :item="item"
                :clickable="false"
              />
            </div>
          </template>
        </div>
        <button ref="loadMoreTrigger" class="h-10">
          {{ $t('Load more') }}
        </button>
      </div>
    </section>
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-class="transform translate-y-full opacity-0"
      enter-to-class="transform translate-y-0 opacity-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-class="transform translate-y-0 opacity-100"
      leave-to-class="transform translate-y-full opacity-0"
    >
      <div
        v-if="showFixedCTA()"
        class="block md:hidden fixed bottom-0 left-0 right-0 z-[200] bg-white border-t border-gray-200 shadow-lg p-4 safe-area-pb"
      >
        <nuxt-link to="/checkout?productId=package&priceId=medium" class="w-full">
          <ButtonPrimary class="!bg-[#ff6600] border border-black/30 w-full">
            <span>{{ $t('Select most popular package') }}</span>
            <IconSmallArrow class="w-5 h-5 text-white" />
          </ButtonPrimary>
        </nuxt-link>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  data () {
    return {
      totalVisibleReviews: 30,
      currentIndex: 0,
      batchSize: 100,
      observer: null,
      scrollY: 0,
      currencyKey: 0
    }
  },
  computed: {
    reviews () {
      return this.$store.state.reviews
    },
    trustPilotReviews () {
      return this.$store.state.trustpilotReviews
    },
    newReviews () {
      // Any reviews after augst 19th
      return this.reviews.filter(review =>
        this.isCreatedAfterAugust21(review.createdAt) &&
        review?.review?.image != null
      )
    },
    mixedReviews () {
      // const mixed = []
      // const newReviews = [...this.newReviews]
      const trustpilotReviews = [...this.trustPilotReviews]

      return trustpilotReviews.map(review => ({ type: 'trustpilot', ...review }))

      // while (newReviews.length > 0 || trustpilotReviews.length > 0) {
      //   if (newReviews.length > 0) {
      //     mixed.push({ type: 'review', ...newReviews.shift() })
      //   }
      //   if (trustpilotReviews.length > 0) {
      //     mixed.push({ type: 'trustpilot', ...trustpilotReviews.shift() })
      //   }
      // }

      // return mixed
    },
    visibleReviews () {
      return this.mixedReviews.slice(0, this.totalVisibleReviews)
    }
  },
  mounted () {
    this.$posthog.capture('$funnel:package_selection')
    this.setupIntersectionObserver()
    window.addEventListener('scroll', this.handleScroll, { passive: true })

    this.$root.$on('currency-changed', this.handleCurrencyChange)
  },
  beforeDestroy () {
    if (this.observer) {
      this.observer.disconnect()
    }
    window.removeEventListener('scroll', this.handleScroll)

    this.$root.$off('currency-changed', this.handleCurrencyChange)
  },
  methods: {
    handleScroll () {
      this.scrollY = window.scrollY
    },
    showFixedCTA () {
      return this.scrollY > 100
    },
    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    },
    setupIntersectionObserver () {
      this.observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && this.currentIndex < this.mixedReviews.length) {
          this.loadMoreReviews()
        }
      }, { rootMargin: '100px' })

      this.observer.observe(this.$refs.loadMoreTrigger)
    },
    loadMoreReviews () {
      console.log('loadMoreReviews')
      this.totalVisibleReviews += 30
    },
    handleCurrencyChange () {
      this.currencyKey += 1

      this.$forceUpdate()

      this.$nextTick(() => {
        this.$children.forEach((child) => {
          if (child.$forceUpdate) {
            child.$forceUpdate()
          }
        })
      })
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Select your package": "Select your package",
      "One-time payment, no subscriptions, 100% money back guarantee.": "One-time payment, no subscriptions, 100% money back guarantee.",
      "We won't let you leave": "We won't let you leave",
      "without good headshots": "without good headshots",
      "headshots created for": "headshots created for",
      "happy customers": "happy customers",
      "Load more": "Load more"
    },
    "es": {
      "Select your package": "Selecciona tu paquete",
      "One-time payment, no subscriptions, 100% money back guarantee.": "Pago único, sin suscripciones, garantía de devolución del 100%.",
      "We won't let you leave": "No te dejaremos ir",
      "without good headshots": "sin buenas fotos profesionales",
      "headshots created for": "fotos profesionales creadas para",
      "happy customers": "clientes satisfechos",
      "Load more": "Cargar más"
    },
    "de": {
      "Select your package": "Paket auswählen",
      "One-time payment, no subscriptions, 100% money back guarantee.": "Einmalige Zahlung, keine Abos, 100% Geld-zurück-Garantie.",
      "We won't let you leave": "Wir lassen dich nicht gehen",
      "without good headshots": "ohne gute Bewerbungsfotos",
      "headshots created for": "Bewerbungsfotos erstellt für",
      "happy customers": "zufriedene Kunden",
      "Load more": "Mehr laden"
    }
  }
</i18n>
