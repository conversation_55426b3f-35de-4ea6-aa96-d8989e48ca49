const colors = require("tailwindcss/colors")
const defaultTheme = require("tailwindcss/defaultTheme")
const plugin = require("tailwindcss/plugin")
const screens = require("./tailwind.screens")

module.exports = {
  content: [
    "./app/**/*.php",
    "./config/**/*.php",
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
    "./resources/sass/_colorpicker.scss",
    "./resources/sass/_datatables.scss",
    "./trends/**/*.php",
  ],
  darkMode: "class",
  plugins: [
    require("@headlessui/tailwindcss"),
    require("@tailwindcss/typography"),
    plugin(({ addVariant }) => addVariant("focus-a", "&:has(a:focus-visible)")),
    plugin(({ addVariant }) => addVariant("group-expanded", '.group[aria-expanded="true"] &')),
    plugin(({ addVariant }) => addVariant("field", "& :is(.field, input, select, textarea)")),
    plugin(({ addVariant }) => addVariant("help", "& .help")),
    plugin(({ addVariant }) => addVariant("label", "& :is(label, legend)")),
    plugin(({ addVariant }) => addVariant("tooltip", "& .plyr__tooltip")),
  ],
  variants: {
    extend: {
      visibility: ["group-hover"],
    },
  },
  theme: {
    extend: {
      visibility: {
        collapse: null,
      },
      colors: {
        current: "currentColor",
        gray: {
          ...colors.slate,
          600: "#405364", // body text
        },
        blue: {
          DEFAULT: "#001A30",
          50: "#E7F7FA",
          100: "#D2EEF3",
          200: "#B6E7F0",
          300: "#A1E1ED",
          400: "#58CBE4",
          500: "#0097CE",
          600: "#0080CE",
          700: "#0F548C",
          800: "#103F64",
          900: "#001A30", // hero bg
        },
        orange: {
          DEFAULT: "#E25536",
          50: "#FFEFE8",
          100: "#FFE2D5",
          200: "#FFCBB4",
          300: "#FF9A7D",
          400: "#FF8B69",
          500: "#E56C51",
          600: "#E25536",
          700: "#D73E1E",
          800: "#A62F17",
        },
        green: {
          ...colors.lime,
          DEFAULT: "#438629",
          100: "#EAF5E6",
          200: "#C3E2B6",
          300: "#A1D58D",
          400: "#78BD5D",
          500: "#5E9E45",
          600: "#438629",
          700: "#307217",
        },
        inherit: "inherit",
        red: { DEFAULT: colors.red["600"] },
        yellow: {
          DEFAULT: "#FCBA4D",
          500: "#FCBA4D",
        },
        indigo: {
          500: "#5D63A6",
          600: "#5850D2",
          900: "#303672",
        },
        violet: {
          DEFAULT: "#6D28D9",
        },
        transparent: "transparent",
      },
      fontSize: {
        "xl": ["1.25rem", { lineHeight: "2rem" }],
        "8xl": ["5.5rem", { lineHeight: "5.5rem" }],
      },
      backgroundSize: {
        "50%": "50%",
      },
      screens: {
        ...screens,
        pl: { raw: "(orientation: landscape) and (min-width: 480px)" }, // player landscape
        pp: { raw: "(orientation: portrait), (max-width: 479px)" }, // player portait
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            "--tw-prose-body": theme("colors.gray[600]"),
            "--tw-prose-headings": theme("colors.blue[900]"),
            // '--tw-prose-lead': theme('colors.gray[700]'),
            // '--tw-prose-links': theme('colors.gray[900]'),
            "--tw-prose-bold": theme("colors.blue[900]"),
            // '--tw-prose-counters': theme('colors.gray[600]'),
            // '--tw-prose-bullets': theme('colors.gray[400]'),
            // '--tw-prose-hr': theme('colors.gray[300]'),
            // '--tw-prose-quotes': theme('colors.gray[900]'),
            // '--tw-prose-quote-borders': theme('colors.gray[300]'),
            // '--tw-prose-captions': theme('colors.gray[700]'),
            // '--tw-prose-code': theme('colors.gray[900]'),
            // '--tw-prose-pre-code': theme('colors.gray[100]'),
            // '--tw-prose-pre-bg': theme('colors.gray[900]'),
            // '--tw-prose-th-borders': theme('colors.gray[300]'),
            // '--tw-prose-td-borders': theme('colors.gray[200]'),
            "--tw-prose-invert-body": theme("colors.gray[400]"),
            "--tw-prose-invert-headings": theme("colors.white"),
            // '--tw-prose-invert-lead': theme('colors.gray[300]'),
            // '--tw-prose-invert-links': theme('colors.white'),
            "--tw-prose-invert-bold": theme("colors.gray[200]"),
            // '--tw-prose-invert-counters': theme('colors.gray[400]'),
            // '--tw-prose-invert-bullets': theme('colors.gray[600]'),
            // '--tw-prose-invert-hr': theme('colors.gray[700]'),
            // '--tw-prose-invert-quotes': theme('colors.gray[100]'),
            // '--tw-prose-invert-quote-borders': theme('colors.gray[700]'),
            // '--tw-prose-invert-captions': theme('colors.gray[400]'),
            // '--tw-prose-invert-code': theme('colors.white'),
            // '--tw-prose-invert-pre-code': theme('colors.gray[300]'),
            // '--tw-prose-invert-pre-bg': 'rgb(0 0 0 / 50%)',
            // '--tw-prose-invert-th-borders': theme('colors.gray[600]'),
            // '--tw-prose-invert-td-borders': theme('colors.gray[700]'),
            "a": {
              color: null, // link color is set in app.scss
              textDecoration: "none",
            },
            "b": {
              fontWeight: 600,
            },
            "h1": {
              fontFamily: theme("fontFamily.serif"),
              fontWeight: 800,
            },
            "h2": {
              fontWeight: 600,
            },
            "h3": {
              fontWeight: 600,
            },
            "h4": {
              fontSize: "1em",
              textTransform: "uppercase",
              fontWeight: 600,
              letterSpacing: "0.05em",
            },
            "h5": {
              fontSize: "1em",
              fontWeight: 600,
            },
            "maxWidth": null,
            "strong": {
              fontWeight: 600,
            },
            "ul > li::marker": {
              color: null,
            },
            ".note": {
              marginTop: "1.25em",
              marginBottom: "1.25em",
            },
            "table": {
              fontSize: "1rem",
              lineHeight: "1.25rem",
            },
          },
        },
      }),
      maxWidth: {
        "2xs": "10rem",
        "xs": "16rem",
      },
      spacing: {
        18: "4.5rem",
      },
    },
    fontFamily: {
      sans: ["'Source Sans 3'", defaultTheme.fontFamily.sans],
      serif: ["'Source Serif 4'", defaultTheme.fontFamily.serif],
    },
    container: {
      center: true,
      padding: {
        DEFAULT: "0.75rem",
        sm: "1rem",
      },
      screens: {
        lg: "1120px",
        xl: "1320px",
      },
    },
  },
}
