import { Priority } from "../resources/js/models/Priority"
import { expect, register, test } from "./support"

test("report", async ({ page }) => {
  await page.home()

  await page.nav("Find schools").click()

  await page.link("Build your personal report").click()
  await page.link("Get started for free").click()
  await page.link("Sign up").click()

  await test.step("Sign up", () => page.signup())

  test.slow()

  await test.step("Build report", () => page.buildReport({
    gpa: "4.2",
    highPrestige: true,
    jobPreferences: [
      { type: /Any law firm job/, priority: Priority.LOW },
      { type: /Public service/, priority: Priority.HIGH },
    ],
    lsat: "140",
    races: ["Caucasian/White"],
    states: ["Hawaii"],
  }))

  await page.text("Welcome to your personal report")

  await test.step("Edit location", async () => {
    await page.button("Settings").click()
    await page.text("Personal report settings")
    await page.button("Location").click()
    await page.field("Working in a high-prestige job is very important to me").uncheck()
    await page.field("Preferred states").first().fill("California")
    await page.keyboard.press("Enter")
    await page.field("Preferred states").first().fill("Iowa")
    await page.keyboard.press("Enter")
    await page.keyboard.press("Tab")
    await page.save()
    await page.title("Personal Report")
    await page.alert(/You successfully updated your location preferences/)
  })

  await test.step("Edit job priorities", async () => {
    await page.button("Settings").click()
    await page.text("Personal report settings")
    await page.button("Job priorities").click()
    await page.setJobPreferences([
      { type: /Any law firm job/, priority: Priority.LOW },
      { type: /Large firm/, priority: Priority.MEDIUM },
      { type: /Public service/, priority: Priority.HIGH },
    ])
    await page.save()
    await page.title("Personal Report")
    await page.alert(/You successfully updated your job priorities/)
  })

  await test.step("Edit LSAT and GPA", async () => {
    await page.button("Settings").click()
    await page.text("Personal report settings")
    await page.button("LSAT and GPA").click()
    await page.field("LSAT").fill("170")
    await page.field("GPA").fill("4.21")
    await page.save()
    await page.alert(/You successfully updated your GPA and LSAT/)
  })

  await test.step("Edit race/ethnicity", async () => {
    await page.button("Settings").click()
    await page.text("Personal report settings")
    await page.button("Race/ethnicity").click()
    await page.field("Race/ethnicity").first().fill("Caucasian/White")
    await page.keyboard.press("Enter")
    await page.field("Race/ethnicity").first().fill("Black")
    await page.keyboard.press("Enter")
    await page.keyboard.press("Tab")
    await page.save()
    await page.alert(/You successfully updated your race/)
  })

  await test.step("Edit debt payment", async () => {
    await page.button("Settings").click()
    await page.text("Personal report settings")
    await page.button("Debt payment").click()
    await page.select("Term").selectOption("20")
    await page.field("Target debt service").fill("8")
    await page.save()
    await page.title("Personal Report")
    await page.alert(/You successfully updated your debt payment preferences/)
  })

  await test.step("Add to list", async () => {
    await page.button("Save school to your list").first().click()
    await page.toast(/Saved school to your list/)
  })

  await test.step("List filter", async () => {
    await page.text("Your list").click()
    await page.waitForURL(url => url.searchParams.get("source") === "list")
    await expect(page.text("1 schools")).toBeVisible()
  })

  await test.step("Remove from list", async () => {
    await page.button("Remove school from your list").first().click()
    await page.toast(/Removed school from your list/)
  })
})

register("buildReport", async (page, opts = {}) => {
  await page.button(/Get started/).click()

  for (const job of opts.jobPreferences) {
    if (job.priority !== "No") {
      await page.field(job.type).check()
    }
  }
  await page.next()

  await page.radio(opts.highPrestige ? "Yes" : "No").check()
  await page.next()

  await page.setJobPreferences(opts.jobPreferences)

  await page.next()
  await page.next()

  await test.step("Location preferences", async () => {
    for (const state of opts.states) {
      await page.field("Select states").first().fill(state)
      await page.keyboard.press("Enter")
      await page.keyboard.press("Tab")
    }
    await page.next()
  })

  await test.step("Scores", async () => {
    await page.field("GPA").fill(opts.gpa)
    await page.field("LSAT").fill(opts.lsat)
    await page.next()
  })

  await test.step("Race/Ethnicity", async () => {
    for (const race of opts.races) {
      await page.field("Race/ethnicity").first().fill(race)
      await page.keyboard.press("Enter")
    }
    await page.keyboard.press("Tab")
    await page.next()
  })

  await page.link("Continue to report").click()

  await page.title("Personal Report")
})

register("setJobPreferences", async (page, jobPreferences) => {
  for (const { type, priority } of jobPreferences) {
    await page.field(type).fill(`${priority}`)
  }
})
