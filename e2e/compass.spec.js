import { expect, test } from "./support"

test("compass", async ({ page }) => {
  await page.home()

  await page.nav("Explore").click()

  await page.link("Legal Career Compass").first().click()

  await page.link("Get started for $39 + tax").first().click()

  await test.step("Sign up", () => page.signup())

  await page.title("Legal Career Compass")

  await page.button("Get started for $39 + tax").first().click()

  await page.dialog("Purchase Legal Career Compass", async (modal) => {
    const submit = modal.button("Pay $39 (one-time fee) + tax")

    await expect(submit).toBeDisabled()

    await modal.iframe("iframe[src*='inner-address']", async (address) => {
      await address.select("Country or region").selectOption("US")
      await address.field("Address").first().fill("354 Oyster Point Blvd")

      await page.keyboard.press("Tab")

      await address.field("City").fill("South San Francisco")
      await address.select("State").selectOption("CA")
      await address.field("ZIP").fill("94090")
    })

    await modal.iframe("iframe[src*='inner-payment']", async (payment) => {
      await payment.field("Card number").fill("4242 4242 4242 4242")
      await payment.field(/Expiration/).fill("4242")
      await payment.field("CVC").fill("424")
    })

    await expect(submit).toBeEnabled()

    await submit.click()
  })

  await page.waitForResponse("/compass/pay")

  await page.alert(/Thank you for your purchase/)

  await page.button(/Start the assessment/).first().click()

  await page.select(/Preferred pronoun/).selectOption("2")
  await page.button(/Save and continue/).click()

  await page.field("I agree to the Step Research Privacy Policy and Terms of use").check()

  await expect(page.button("Proceed to Step Research")).toBeVisible()
})
