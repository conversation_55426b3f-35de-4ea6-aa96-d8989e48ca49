const inlines = require("eslint-plugin-vue/lib/utils/inline-non-void-elements.json")

module.exports = {
  root: true,
  env: {
    node: true,
    jest: true,
  },
  extends: [
    "plugin:vue/vue3-recommended",
    "eslint:recommended",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:tailwindcss/recommended",
  ],
  globals: {
    $: true,
    Stripe: true,
  },
  plugins: [
    "sort-imports-es6-autofix",
    "tailwindcss",
    "vue",
  ],
  rules: {
    "array-bracket-spacing": ["error"],
    "arrow-spacing": ["error"],
    "block-spacing": ["error"],
    "brace-style": ["error"],
    "comma-dangle": ["error", "always-multiline"],
    "comma-spacing": ["error"],
    "computed-property-spacing": ["error"],
    "curly": ["error"],
    "dot-notation": ["error"],
    "eol-last": ["error"],
    "func-call-spacing": ["error"],
    "generator-star-spacing": ["error"],
    "import/default": ["off"],
    "import/extensions": ["error", "ignorePackages", { js: "never", ts: "never", vue: "always" }],
    "import/no-unused-modules": ["error", { src: ["resources/js"], unusedExports: true }],
    "indent": ["error", 2, { SwitchCase: 1 }],
    "key-spacing": ["error"],
    "keyword-spacing": ["error"],
    "linebreak-style": ["error", "unix"],
    "lines-between-class-members": ["error", "always", { exceptAfterSingleLine: true }],
    "no-confusing-arrow": ["error"],
    "no-console": ["error", { allow: ["warn", "error"] }],
    "no-duplicate-imports": ["error"],
    "no-else-return": ["error"],
    "no-extra-parens": ["error"],
    "no-multi-assign": ["error"],
    "no-multi-spaces": ["error"],
    "no-multiple-empty-lines": ["error", { max: 1 }],
    "no-restricted-imports": ["error", { paths: [{ name: "lodash", message: "Please use lodash-es instead." }], patterns: ["../*"] }],
    "no-trailing-spaces": ["error"],
    "no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
    "no-useless-computed-key": ["error"],
    "no-useless-constructor": ["error"],
    "no-useless-rename": ["error"],
    "no-var": ["error"],
    "no-whitespace-before-property": ["error"],
    "object-curly-spacing": ["error", "always"],
    "object-shorthand": ["error"],
    "one-var": ["error", "never"],
    "padded-blocks": ["error", "never"],
    "padding-line-between-statements": ["error", { blankLine: "always", prev: "*", next: "multiline-block-like" }, { blankLine: "always", prev: "*", next: "return" }],
    "prefer-arrow-callback": ["error"],
    "prefer-const": ["error"],
    "prefer-template": ["error"],
    "quote-props": ["error", "consistent-as-needed"],
    "quotes": ["error", "double", "avoid-escape"],
    "rest-spread-spacing": ["error"],
    "semi": ["error", "never"],
    "sort-imports-es6-autofix/sort-imports-es6": ["error", { memberSyntaxSortOrder: ["none", "all", "single", "multiple"] }],
    "space-before-blocks": ["error"],
    "space-before-function-paren": ["error", { anonymous: "always", named: "never", asyncArrow: "always" }],
    "space-in-parens": ["error"],
    "space-infix-ops": ["error"],
    "space-unary-ops": ["error"],
    "spaced-comment": ["error"],
    "switch-colon-spacing": ["error"],
    "tailwindcss/classnames-order": ["off", { prependCustom: true }],
    "tailwindcss/no-custom-classname": ["off"],
    "template-curly-spacing": ["error"],
    "template-tag-spacing": ["error"],
    "vue/component-definition-name-casing": ["error", "kebab-case"],
    "vue/component-name-in-template-casing": ["error", "kebab-case"],
    "vue/component-tags-order": ["error", { order: ["template", "script", "style"] }],
    "vue/define-macros-order": ["error", { order: ["defineOptions", "defineModel", "defineProps", "defineEmits", "defineSlots"] }],
    "vue/html-closing-bracket-spacing": ["error", { startTag: "never", endTag: "never", selfClosingTag: "never" }],
    "vue/html-indent": ["error", 2, { attribute: 1, alignAttributesVertically: false }],
    "vue/key-spacing": ["error"],
    "vue/match-component-file-name": ["error", { extensions: ["vue"], shouldMatchCase: true }],
    "vue/max-attributes-per-line": ["error", { singleline: { max: 4 }, multiline: { max: 1 } }],
    "vue/multi-word-component-names": ["off"],
    "vue/no-boolean-default": ["error"],
    "vue/no-mutating-props": ["warn"],
    "vue/no-side-effects-in-computed-properties": ["error"],
    "vue/no-use-v-if-with-v-for": ["error"],
    "vue/no-v-html": ["error"],
    "vue/object-curly-spacing": ["error", "always"],
    "vue/order-in-components": ["error"],
    "vue/padding-line-between-blocks": ["error"],
    "vue/require-default-prop": ["warn"],
    "vue/require-prop-types": ["warn"],
    "vue/require-v-for-key": ["error"],
    "vue/script-indent": ["error", 2, { switchCase: 1 }],
    "vue/singleline-html-element-content-newline": ["error", { ignores: ["dd", "dt", "pre", "textarea", ...inlines] }],
    "vue/valid-v-for": ["warn"],
  },
  parser: "vue-eslint-parser",
  parserOptions: {
    ecmaFeatures: { legacyDecorators: true },
    ecmaVersion: 2022,
  },
  overrides: [
    {
      files: ["*.vue"],
      rules: {
        "indent": ["off"],
        "no-duplicate-imports": ["off"],
        "no-extra-parens": ["off"],
        "vue/require-default-prop": ["off"],
        "vue/valid-v-slot": ["off"],
      },
    },
    {
      files: ["e2e/*.js"],
      rules: {
        "no-restricted-imports": ["off"],
      },
    },
  ],
  settings: {
    "import/extensions": [".js", ".vue"],
    "import/resolver": {
      alias: {
        map: [
          ["@", "resources/js"],
        ],
      },
      webpack: {
        config: require("./webpack.config"),
      },
    },
  },
}
