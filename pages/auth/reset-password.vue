<template>
  <section class="min-h-screen pb-16 mb:pb-0 flex content-center items-center justify-center bg-gray-100 text-black">
    <div class="xl:w-full xl:max-w-sm 2xl:max-w-md xl:mx-auto">
      <Logo class="w-[90px] text-black" />
      <h2 class="text-3xl font-bold leading-tight text-black sm:text-3xl mt-8">
        Reset password
      </h2>
      <p class="mt-2 text-base text-gray-600">
        Know your password?  <nuxt-link to="/auth/login" title="" class="font-medium text-blue-600 transition-all duration-200 hover:text-blue-700 focus:text-blue-700 hover:underline">
          Login
        </nuxt-link>
      </p>
      <div class="mt-4">
        <AuthResetPasswordForm />
      </div>
    </div>
    <MetaMain
      title="Reset your password"
      description="Landingfolio features the best landing page inspiration, templates, resources and examples on the web. Learn from a growing library of 1,982 landing page designs and 3,829 component examples, updated daily!"
    />
  </section>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      unsubscribeAuthChange: () => {}
    }
  },
  head () {
    return {
      title: 'Reset password | HeadshotPro'
    }
  },
  created () {
    // await this.$fire.authReady()
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged((user) => {
      if (user) {
        if (this.$route.query && this.$route.query.redirect) {
          this.$router.push(this.$route.query.redirect)
        } else {
          this.$router.push('/')
        }
      }
    })
  },

  beforeDestroy () {
    this.unsubscribeAuthChange()
  },
  methods: {

  }
}
</script>

<style>

</style>
