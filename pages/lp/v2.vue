<template>
  <div class="h-full overflow-x-hidden font-sans text-gray-900 antialiased">
    <!-- START WRAPPER -->
    <div class="isolate flex min-h-screen flex-col">
      <!-- START HEADER -->
      <div class="hidden border-b border-gray-200 bg-gray-50 pb-2.5 pt-2 lg:block">
        <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
          <div class="flex items-center gap-6">
            <p class="text-xs font-medium text-paragraph">
              Founded in Holland. We
              <span class="font-bold">respect your privacy.</span>
            </p>

            <p class="text-xs font-medium text-paragraph">
              Used by
              <span class="font-bold">80,261 happy</span>
              customers
            </p>

            <p class="text-xs font-medium text-paragraph">
              <span class="font-bold">11,241,760+</span>
              professional headshots already created
            </p>
          </div>
        </div>
      </div>

      <header class="sticky inset-x-0 top-0 z-50 border-b border-gray-100 bg-white/80 py-3 backdrop-blur-lg">
        <div class="mx-auto max-w-screen-xl px-4 sm:px-6 md:px-8 2xl:px-0">
          <div class="flex items-center justify-between gap-6 md:gap-8">
            <nuxt-link to="/lp/v2" title="" class="flex">
              <img class="h-6 w-auto" src="@/assets/img/logo.svg" alt="" loading="lazy">
            </nuxt-link>

            <div class="hidden min-w-0 flex-1 items-center justify-start gap-6 md:flex lg:gap-8">
              <nuxt-link to="/company-headshots" title="" class="text-base font-medium text-primary-500 transition-all duration-150 hover:text-opacity-80">
                Teams
              </nuxt-link>

              <nuxt-link to="/reviews" title="" class="text-base font-medium text-primary-500 transition-all duration-150 hover:text-opacity-80">
                Examples
              </nuxt-link>

              <nuxt-link to="#pricing" title="" class="text-base font-medium text-primary-500 transition-all duration-150 hover:text-opacity-80">
                Pricing
              </nuxt-link>
            </div>

            <div class="hidden items-center justify-end gap-4 lg:flex xl:gap-6">
              <nuxt-link to="#" title="" class="group inline-flex items-center gap-0.5 text-sm font-medium text-paragraph transition-all duration-150 hover:text-primary-500">
                Headshots
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
                <ul class="hidden group-hover:flex absolute top-[38px]  w-[250px] flex-col bg-white px-4 py-2.5 border-t-2 border-t-brand-500 rounded-b-md shadow-xl space-y-2 border border-black/10">
                  <li v-for="child in headshotNavigation" :key="child.url">
                    <nuxt-link :to="child.url" :title="child.title" class="text-sm hover:font-bold leading-6 text-gray-600 transition-all duration-150 hover:text-gray-900">
                      {{ child.title }}
                    </nuxt-link>
                  </li>
                </ul>
              </nuxt-link>

              <nuxt-link to="/blog" title="" class="inline-flex items-center gap-0.5 text-sm font-medium text-paragraph transition-all duration-150 hover:text-primary-500">
                Blog
              </nuxt-link>

              <a href="#" title="" class="group inline-flex items-center gap-0.5 text-sm font-medium text-paragraph transition-all duration-150 hover:text-primary-500">
                Tools
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
                <ul class="hidden group-hover:flex absolute top-[38px]  w-[250px] flex-col bg-white px-4 py-2.5 border-t-2 border-t-brand-500 rounded-b-md shadow-xl space-y-2 border border-black/10">
                  <li v-for="child in toolNavigation" :key="child.url">
                    <nuxt-link :to="child.url" :title="child.title" class="text-sm hover:font-bold leading-6 text-gray-600 transition-all duration-150 hover:text-gray-900">
                      {{ child.title }}
                    </nuxt-link>
                  </li>
                </ul>
              </a>
            </div>

            <div class="hidden items-center justify-end gap-3 md:flex">
              <client-only>
                <nuxt-link
                  v-if="!isLoggedIn"
                  to="/auth/login"
                  title=""
                  class="inline-flex items-center justify-center gap-1.5 rounded-lg border border-transparent bg-gray-200 px-3.5 pb-2 pt-1.5 text-sm font-medium text-gray-500 transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20"
                  role="button"
                >
                  Login
                </nuxt-link>
                <template v-if="!isLoggedIn">
                  <nuxt-link :to="'/auth/login?redirect=' + encodeURIComponent('/app')" title="" class="inline-flex items-center justify-center gap-1.5 rounded-lg border border-transparent bg-primary-500 px-3.5 pb-2 pt-1.5 text-sm font-medium text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20" role="button">
                    Get your headshots
                  </nuxt-link>
                </template>
                <template v-else>
                  <nuxt-link to="/app" title="" class="inline-flex items-center justify-center gap-1.5 rounded-lg border border-transparent bg-primary-500 px-3.5 pb-2 pt-1.5 text-sm font-medium text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20" role="button">
                    To dashboard
                  </nuxt-link>
                </template>
              </client-only>
            </div>

            <div class="md:hidden">
              <button type="button" class="inline-flex items-center justify-center gap-1.5 rounded-lg border border-transparent bg-gray-200 px-2.5 pb-1.5 pt-1 text-sm font-medium uppercase text-gray-500 shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20">
                Menu
              </button>
            </div>
          </div>
        </div>
      </header>
      <!-- END HEADER -->

      <!-- START MAIN -->
      <main class="flex-1">
        <!-- START HERO -->
        <section class="relative isolate overflow-hidden bg-white py-8 sm:py-12 lg:py-16 xl:py-20">
          <div class="absolute bottom-0 left-0 hidden lg:-ml-10 lg:block 3xl:left-36">
            <img class="w-full max-w-xs lg:max-w-sm 2xl:max-w-md" src="@/assets/img/landing-page/hero-image-woman.png" alt="" loading="lazy">
          </div>

          <div class="absolute bottom-0 right-0 mb-[-0.3rem] hidden lg:-mr-10 lg:block 3xl:right-36">
            <img class="w-full max-w-xs lg:max-w-sm 2xl:max-w-md" src="@/assets/img/landing-page/hero-image-man.png" alt="" loading="lazy">
          </div>

          <div class="absolute inset-x-0 bottom-0 h-24 w-full bg-gradient-to-t from-black/40 to-black/0" />

          <div class="relative mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="mx-auto md:max-w-2xl md:text-center">
              <h1 class="text-xs font-bold text-[#474368] sm:text-sm lg:text-base">
                The #1 AI Headshot Generator for Professional Headshots
              </h1>
              <h2 class="mt-3 text-balance text-2xl font-bold leading-tight tracking-tight text-primary-500 sm:mt-6 sm:text-4xl xl:text-[42px]">
                Professional business headshots, without a physical photo shoot
              </h2>
              <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg">
                Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.
              </p>
              <div class="mt-6">
                <client-only>
                  <template v-if="!isLoggedIn">
                    <nuxt-link
                      :to="'/auth/login?redirect=' + encodeURIComponent('/app')"
                      title=""
                      class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-primary-500 px-12 pb-3.5 pt-3 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                      role="button"
                    >
                      Get your headshots now
                      <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                      </svg>
                    </nuxt-link>
                  </template>
                  <template v-else>
                    <nuxt-link
                      to="/app"
                      title=""
                      class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-primary-500 px-12 pb-3.5 pt-3 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                      role="button"
                    >
                      Access your photos
                      <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                      </svg>
                    </nuxt-link>
                  </template>
                </client-only>
              </div>
            </div>

            <div class="mt-8 flex flex-col items-center justify-center gap-4 sm:mt-12 sm:flex-row sm:gap-6">
              <div class="flex items-center gap-3">
                <img class="h-6 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
                <img class="h-6 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="" loading="lazy">
              </div>

              <div class="flex items-center gap-2">
                <div class="flex -space-x-2 overflow-hidden">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-1.jpg" alt="" loading="lazy">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-2.jpg" alt="" loading="lazy">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-3.jpg" alt="" loading="lazy">
                </div>
                <p class="-mt-0.5 text-sm font-normal text-[#474368]">
                  <span class="font-bold">{{ $store.state.stats.users }}+</span>
                  happy customers
                </p>
              </div>
            </div>

            <div class="mt-8 flex items-center justify-center gap-x-5">
              <span class="hidden text-sm text-[#A6A4B5] md:flex">As seen on:</span>
              <div class="flex items-center justify-center gap-x-8">
                <img class="h-6" src="@/assets/img/landing-page/logo-cnn.svg">
                <img class="h-3.5" src="@/assets/img/landing-page/logo-new-york-post.svg">
                <img class="h-4" src="@/assets/img/landing-page/logo-vice.svg">
                <img class="h-5" src="@/assets/img/landing-page/logo-bloomberg.svg">
                <img class="h-6" src="@/assets/img/landing-page/logo-fashionista.svg">
              </div>
            </div>

            <div class="-mb-24 mt-8 sm:mt-16 md:px-8 lg:hidden">
              <img class="max-w-[640px] mx-auto w-full" src="@/assets/img/landing-page/hero-image-combined.png" alt="" loading="lazy">
            </div>
          </div>
        </section>
        <!-- END HERO -->

        <!-- START STEPS -->
        <div class="relative z-40 hidden md:block">
          <div aria-hidden="true" class="absolute inset-0 hidden sm:grid">
            <div class="bg-[#E0FAFA]" />
          </div>

          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="relative -top-11 isolate z-20 mx-auto max-w-5xl rounded-lg border-2 border-[#DAF1FE] bg-[#FAFAFA] px-6 py-5 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] sm:px-8">
              <div class="flex flex-col gap-6 lg:flex-row lg:items-end">
                <img width="956" height="48" src="@/assets/img/landing-page/steps-hero.png">
              </div>
            </div>
          </div>
        </div>
        <!-- END STEPS -->

        <!-- START BASIC PRICING -->
        <section class="relative bg-[#E0FAFA] py-6 sm:py-10 md:pt-0 lg:-mt-2">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="gap-8 sm:flex sm:items-center sm:justify-center sm:gap-16 md:flex-row">
              <div>
                <h3 class="text-md font-bold text-primary-500 lg:text-lg">
                  All photoshoots include:
                </h3>
                <ul class="mt-3 space-y-1 text-sm font-medium text-primary-500 md:space-y-2 xl:text-base">
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Done in 2 hours or less
                  </li>

                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    8x cheaper than a photographer
                  </li>

                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Hundreds of results to choose from
                  </li>
                </ul>
              </div>

              <div class="hidden space-y-2 md:block">
                <div class="flex items-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
                  <p class="text-5xl font-bold text-primary-500">
                    $29
                  </p>
                </div>
                <div class="flex items-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
                  <div class="flex items-center gap-2">
                    <p class="text-5xl font-bold text-primary-500">
                      2
                    </p>
                    <p class="text-lg font-bold leading-none text-primary-500">
                      hours
                      <br>
                      done
                    </p>
                  </div>
                </div>
              </div>

              <div class="hidden sm:block">
                <h3 class="text-md font-bold text-primary-500 lg:text-lg">
                  Every package includes:
                </h3>
                <ul class="mt-3 space-y-1 text-sm font-medium text-primary-500 md:space-y-2 xl:text-base">
                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Indistinguishable from real photos
                  </li>

                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Business expense-ready invoice
                  </li>

                  <li class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-[#00B67A]">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Discounts up to 60% for teams
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>
        <!-- END BASIC PRICING -->

        <!-- START SECONDARY NAVIGATION -->
        <section class="sticky border-b border-t border-[#EEEEEE] bg-[#FAFAFA]">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8">
            <nav class="flex w-full items-center gap-8 overflow-x-auto pb-5 pt-4 md:justify-center">
              <nuxt-link to="#showcase" title="" class="whitespace-nowrap text-base font-bold text-[#474368] transition-all duration-150">
                Examples
              </nuxt-link>

              <nuxt-link to="#reviews" title="" class="whitespace-nowrap text-base font-normal text-[#474368] transition-all duration-150 hover:text-primary-500">
                Reviews
              </nuxt-link>

              <nuxt-link to="#how-it-works" title="" class="whitespace-nowrap text-base font-normal text-[#474368] transition-all duration-150 hover:text-primary-500">
                How it works
              </nuxt-link>

              <!-- <a href="#features" title="" class="whitespace-nowrap text-base font-normal text-[#474368] transition-all duration-150 hover:text-primary-500">Tools</a> -->

              <nuxt-link to="#pricing" title="" class="whitespace-nowrap text-base font-normal text-[#474368] transition-all duration-150 hover:text-primary-500">
                Pricing
              </nuxt-link>

              <!-- <a href="#faq" title="" class="whitespace-nowrap text-base font-normal text-[#474368] transition-all duration-150 hover:text-primary-500">FAQ</a> -->

              <nuxt-link :to="'/auth/login?redirect=' + encodeURIComponent('/app')" title="" class="whitespace-nowrap text-base font-normal text-[#474368] transition-all duration-150 hover:text-primary-500">
                Get your headshots
              </nuxt-link>
            </nav>
          </div>
        </section>
        <!-- END SECONDARY NAVIGATION -->

        <!-- START SHOWCASE -->
        <section id="showcase" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="text-left md:text-center">
              <div class="flex items-center justify-start gap-x-3 md:justify-center">
                <div class="flex -space-x-2 overflow-hidden">
                  <img class="inline-block size-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-1.jpg" alt="" loading="lazy">
                  <img class="inline-block size-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-2.jpg" alt="" loading="lazy">
                  <img class="inline-block size-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-3.jpg" alt="" loading="lazy">
                </div>
                <p class="-mt-0.5 text-sm font-medium tracking-tighter text-paragraph sm:text-base lg:text-lg">
                  Trusted by
                  <span class="font-bold text-[#00B67A]">{{ $store.state.stats.users }}+</span>
                  happy customers
                </p>
              </div>
              <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
                Look More Professional in 2 Hours or Less
              </h2>
              <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-xl">
                The fastest way to get professional headshots you can use anywhere
              </p>
            </div>

            <div class="relative mt-8 sm:mt-12 md:px-20 lg:px-0">
              <div class="absolute -top-24 right-8 hidden flex-col items-center gap-1 xl:flex">
                <p class="rotate-[-12deg] py-4 text-right font-cursive text-base leading-4 tracking-[-0.056px] text-paragraph">
                  These photos are
                  <br>
                  100% AI generated
                </p>
                <svg class="h-10 w-auto text-paragraph" viewBox="0 0 22 41" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M6.53796 38.6866C8.57188 38.4947 10.5521 38.3342 12.7948 38.1201C12.5882 39.0022 11.7313 39.4684 10.7889 39.6202C7.96176 40.0756 5.08487 40.4938 2.1662 40.7376C0.498109 40.8818 -0.335102 40.0427 0.243298 39.0102C1.1735 37.2759 2.2568 35.5848 3.29038 33.8565C3.34802 33.7567 3.40765 33.6226 3.51302 33.5943C3.8848 33.4439 4.30429 33.365 4.72578 33.2518C4.86698 33.5007 5.21497 33.7615 5.15138 33.9642C4.95466 34.6749 4.65654 35.3454 4.41012 36.0189C4.28492 36.3899 4.11001 36.7236 4.33677 37.2869C4.9789 36.9458 5.67274 36.6077 6.31487 36.2666C19.0899 28.8215 22.3824 16.7359 14.6464 6.10992C13.6362 4.74479 12.4132 3.47051 11.2976 2.13367C10.9098 1.66421 10.5737 1.19774 10.186 0.728278C10.2953 0.631442 10.4543 0.571868 10.5637 0.475033C11.023 0.604761 11.6413 0.674917 11.9376 0.932763C12.9796 1.74954 14.0215 2.56633 14.9025 3.47695C23.7158 12.5147 23.3937 24.3595 13.973 32.9958C12.1659 34.645 9.90557 36.0617 7.84801 37.5588C7.41463 37.8776 6.93352 38.1249 6.45043 38.4064C6.39477 38.472 6.44052 38.5778 6.53796 38.6866Z"
                  />
                </svg>
              </div>

              <div class="mx-auto flex w-full snap-x gap-6 overflow-x-auto lg:grid lg:grid-cols-3 xl:max-w-6xl">
                <LandingpageV2ShowcaseItem v-for="item in showcaseItems" :key="item.name" :item="item" />
              </div>
            </div>

            <!-- <div class="mt-4 flex items-center justify-center gap-1.5">
              <div class="size-2.5 rounded-full bg-gray-600" />
              <div class="size-2.5 rounded-full bg-gray-200" />
              <div class="size-2.5 rounded-full bg-gray-200" />
            </div> -->

            <div class="mt-8 text-left sm:mt-12 md:text-center">
              <p class="text-base font-bold tracking-tighter text-[#474368]">
                Our customers also use their AI headshots for:
              </p>

              <div class="mt-4 flex flex-wrap items-center justify-start gap-3 md:justify-center md:gap-6 xl:gap-x-16">
                <div class="inline-flex items-center gap-3">
                  <svg aria-hidden="true" class="size-7 text-[#77C3EC]" viewBox="0 0 90 90" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21.807 55.554C21.807 60.1794 18.0287 63.9579 13.4035 63.9579C8.77832 63.9579 5 60.1794 5 55.554C5 50.9286 8.77832 47.1501 13.4035 47.1501H21.807V55.554Z" />
                    <path d="M26.0393 55.5542C26.0393 50.9288 29.8176 47.1503 34.4428 47.1503C39.068 47.1503 42.8463 50.9288 42.8463 55.5542V76.5965C42.8463 81.2219 39.068 85.0004 34.4428 85.0004C29.8176 85.0004 26.0393 81.2219 26.0393 76.5965V55.5542Z" />
                    <path d="M34.4428 21.8078C29.8176 21.8078 26.0393 18.0293 26.0393 13.4039C26.0393 8.7785 29.8176 5 34.4428 5C39.068 5 42.8463 8.7785 42.8463 13.4039V21.8078H34.4428Z" />
                    <path d="M34.4448 26.0423C39.07 26.0423 42.8483 29.8208 42.8483 34.4462C42.8483 39.0716 39.07 42.8501 34.4448 42.8501H13.4035C8.77832 42.8501 5 39.0716 5 34.4462C5 29.8208 8.77832 26.0423 13.4035 26.0423H34.4448Z" />
                    <path d="M68.1904 34.4461C68.1904 29.8207 71.9687 26.0422 76.5939 26.0422C81.2191 26.0422 84.9974 29.8207 84.9974 34.4461C84.9974 39.0715 81.2191 42.85 76.5939 42.85H68.1904V34.4461Z" />
                    <path d="M63.9582 34.4462C63.9582 39.0716 60.1798 42.8501 55.5547 42.8501C50.9295 42.8501 47.1512 39.0716 47.1512 34.4462V13.4039C47.1512 8.7785 50.9295 5 55.5547 5C60.1798 5 63.9582 8.7785 63.9582 13.4039V34.4462Z" />
                    <path d="M55.5547 68.1927C60.1798 68.1927 63.9582 71.9712 63.9582 76.5966C63.9582 81.222 60.1798 85.0005 55.5547 85.0005C50.9295 85.0005 47.1512 81.222 47.1512 76.5966V68.1927H55.5547Z" />
                    <path d="M55.5547 63.9582C50.9295 63.9582 47.1512 60.1797 47.1512 55.5543C47.1512 50.9289 50.9295 47.1504 55.5547 47.1504H76.596C81.2211 47.1504 84.9995 50.9289 84.9995 55.5543C84.9995 60.1797 81.2211 63.9582 76.596 63.9582H55.5547Z" />
                  </svg>
                  <p class="text-base font-medium text-[#474368]">
                    Slack/Microsoft Teams
                  </p>
                </div>

                <div class="inline-flex items-center gap-3">
                  <svg aria-hidden="true" class="size-7 text-[#77C3EC]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M12.75 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM7.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM8.25 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM9.75 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM10.5 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM12.75 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM14.25 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 13.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
                    />
                    <path
                      fill-rule="evenodd"
                      d="M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <p class="text-base font-medium text-[#474368]">
                    Conference Bio Photos
                  </p>
                </div>

                <div class="inline-flex items-center gap-3">
                  <svg aria-hidden="true" class="size-7 text-[#77C3EC]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      fill-rule="evenodd"
                      d="M7.5 5.25a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0 1 12 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 0 1 7.5 5.455V5.25Zm7.5 0v.09a49.488 49.488 0 0 0-6 0v-.09a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5Zm-3 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
                      clip-rule="evenodd"
                    />
                    <path d="M3 18.4v-2.796a4.3 4.3 0 0 0 .713.31A26.226 26.226 0 0 0 12 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 0 1-6.477-.427C4.047 21.128 3 19.852 3 18.4Z" />
                  </svg>
                  <p class="text-base font-medium text-[#474368]">
                    Business Cards
                  </p>
                </div>

                <div class="inline-flex items-center gap-3">
                  <svg aria-hidden="true" class="size-7 text-[#77C3EC]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      d="M4.913 2.658c2.075-.27 4.19-.408 6.337-.408 2.147 0 4.262.139 6.337.408 1.922.25 3.291 1.861 3.405 3.727a4.403 4.403 0 0 0-1.032-.211 50.89 50.89 0 0 0-8.42 0c-2.358.196-4.04 2.19-4.04 4.434v4.286a4.47 4.47 0 0 0 2.433 3.984L7.28 21.53A.75.75 0 0 1 6 21v-4.03a48.527 48.527 0 0 1-1.087-.128C2.905 16.58 1.5 14.833 1.5 12.862V6.638c0-1.97 1.405-3.718 3.413-3.979Z"
                    />
                    <path
                      d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 7.797 9 9.103 9 10.609v4.285c0 1.507 1.128 2.814 2.67 2.94 1.243.102 2.5.157 3.768.165l2.782 2.781a.75.75 0 0 0 1.28-.53v-2.39l.33-.026c1.542-.125 2.67-1.433 2.67-2.94v-4.286c0-1.505-1.125-2.811-2.664-2.94A49.392 49.392 0 0 0 15.75 7.5Z"
                    />
                  </svg>
                  <p class="text-base font-medium text-[#474368]">
                    Social Media
                  </p>
                </div>

                <div class="inline-flex items-center gap-3">
                  <svg aria-hidden="true" class="size-7 text-[#77C3EC]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path
                      fill-rule="evenodd"
                      d="M3 2.25a.75.75 0 0 0 0 1.5v16.5h-.75a.75.75 0 0 0 0 1.5H15v-18a.75.75 0 0 0 0-1.5H3ZM6.75 19.5v-2.25a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h.75a.75.75 0 0 1 0 1.5h-.75A.75.75 0 0 1 6 6.75ZM6.75 9a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM6 12.75a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 6a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75Zm-.75 3.75A.75.75 0 0 1 10.5 9h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 12a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM16.5 6.75v15h5.25a.75.75 0 0 0 0-1.5H21v-12a.75.75 0 0 0 0-1.5h-4.5Zm1.5 4.5a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 2.25a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75v-.008a.75.75 0 0 0-.75-.75h-.008ZM18 17.25a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <p class="text-base font-medium text-[#474368]">
                    Real Estate
                  </p>
                </div>
              </div>
            </div>

            <div class="mt-8 gap-6 sm:mt-12 sm:flex sm:items-center sm:justify-center">
              <nuxt-link
                :to="`/auth/login?redirect=${encodeURIComponent('/app')}`"
                title=""
                class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-6 pb-3.5 pt-2.5 text-lg font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                role="button"
              >
                Choose your headshot package
              </nuxt-link>

              <nuxt-link
                to="/reviews"
                title=""
                class="mt-4 inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-gray-200 px-4 pb-3.5 pt-2.5 text-base font-medium text-gray-600 shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:mt-0 sm:w-auto"
                role="button"
              >
                See 100+ real examples
              </nuxt-link>
            </div>

            <div class="mx-auto mt-8 grid max-w-5xl grid-cols-1 gap-6 sm:mt-12 md:grid-cols-3">
              <div class="flex items-center gap-4">
                <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-7.jpg" alt="" loading="lazy">
                <div class="min-w-0 flex-1 space-y-1.5">
                  <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-4.png" alt="" loading="lazy">
                  <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                    “I really like the way I look in many of the pictures. I am otherwise very self-critical.”
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                    Lisa Lavaysse
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-4">
                <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-8.jpg" alt="" loading="lazy">
                <div class="min-w-0 flex-1 space-y-1.5">
                  <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                  <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                    “I needed some headshots for a retirement article that was being written. I did not have the time or the energy. I am glad that you did.”
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                    Byron Veasey
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-4">
                <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-9.jpg" alt="" loading="lazy">
                <div class="min-w-0 flex-1 space-y-1.5">
                  <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                  <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                    “I was able to get several headshot photos with different backgrounds and outfits easily, online, without having to make time for a professional photo shoot.”
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                    Mark
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- END SHOWCASE -->

        <!-- START REVIEWS -->
        <section id="reviews" class="overflow-hidden bg-[#F8FCFF] pt-12 sm:pt-16 lg:pt-20 xl:pt-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="text-left md:text-center">
              <div class="flex items-center justify-start gap-x-3 md:justify-center">
                <div class="flex -space-x-2 overflow-hidden">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-10.jpg" alt="" loading="lazy">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-11.jpg" alt="" loading="lazy">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-12.jpg" alt="" loading="lazy">
                </div>
                <p class="-mt-0.5 text-sm font-medium tracking-tight text-paragraph sm:text-base lg:text-lg">
                  Already
                  <span class="font-bold text-[#00B67A]">12 million+</span>
                  headshots generated
                </p>
              </div>
              <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
                The Most Photorealistic AI Headshot Generator
              </h2>
              <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-lg">
                We generate tens of thousands of professional headshots every day.
              </p>
            </div>

            <div class="mt-8 gap-6 sm:mt-4 sm:flex sm:items-center md:justify-center">
              <nuxt-link
                :to="`/auth/login?redirect=${encodeURIComponent('/app')}`"
                title=""
                class="inline-flex h-11 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-6 pb-2.5 pt-2.5 text-base font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                role="button"
              >
                Choose your headshot package
              </nuxt-link>

              <nuxt-link
                to="/reviews"
                title=""
                class="mt-4 inline-flex h-11 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-gray-200 px-4 pb-2.5 pt-2.5 text-base font-medium text-gray-600 shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:mt-0 sm:w-auto"
                role="button"
              >
                See more reviews
              </nuxt-link>
            </div>
          </div>

          <div class="relative max-h-[900px] overflow-hidden w-full bg-[#F8FCFF]">
            <LandingpageV2Reviews />

            <div class="absolute inset-x-0 bottom-0 h-32 w-full bg-gradient-to-t from-white to-transparent" />

            <div class="absolute inset-y-0 left-0 h-full w-10 bg-gradient-to-r from-white to-transparent sm:w-16 md:w-32 xl:w-full xl:max-w-xs 3xl:max-w-sm" />

            <div class="absolute inset-y-0 right-0 h-full w-10 bg-gradient-to-l from-white to-transparent sm:w-16 md:w-32 xl:w-full xl:max-w-xs 3xl:max-w-sm" />
          </div>
        </section>
        <!-- END REVIEWS -->

        <!-- START COMPARISON -->
        <section id="how-it-works" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="text-left md:text-center">
              <div class="flex items-center justify-start gap-x-3 md:justify-center">
                <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
                <img class="h-4 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="" loading="lazy">
              </div>
              <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
                How Your Selfies Become Professional Headshots
              </h2>
              <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-xl">
                Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.
              </p>
            </div>

            <div class="mt-8 gap-6 sm:mt-12 md:flex md:justify-center">
              <div class="w-full rounded-lg border border-primary-500/15 bg-white p-8 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] lg:max-w-lg">
                <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/landing-page/with-headshotpro.png" alt="" loading="lazy">

                <div class="mt-6 flex items-center gap-2">
                  <p class="text-xl font-bold text-primary-500">
                    With
                  </p>
                  <img class="-mb-0.5 h-6 w-auto" src="@/assets/img/logo.svg" alt="" loading="lazy">
                </div>

                <ul class="mt-6 space-y-8">
                  <li class="flex items-start gap-4">
                    <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-profile.svg" alt="" loading="lazy">
                    <div class="min-w-0 flex-1">
                      <p class="text-lg font-bold leading-none tracking-tighter text-primary-500">
                        1. Upload your photos
                        <span class="font-normal">(5 minutes)</span>
                      </p>
                      <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                        Use your favorite existing photos or take fresh selfies on the spot.
                      </p>
                    </div>
                  </li>

                  <li class="flex items-start gap-4">
                    <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-magic.svg" alt="" loading="lazy">
                    <div class="min-w-0 flex-1">
                      <p class="text-lg font-bold leading-none tracking-[-0.056px] text-primary-500">
                        2. Let our AI work its magic
                        <span class="font-normal">(1-2 hours)</span>
                      </p>
                      <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                        Our AI will pull your most photogenic qualities from the photos you uploaded.
                      </p>
                    </div>
                  </li>

                  <li class="flex items-start gap-4">
                    <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-user.svg" alt="" loading="lazy">
                    <div class="min-w-0 flex-1">
                      <p class="text-lg font-bold leading-none tracking-[-0.056px] text-primary-500">
                        3. Download your favorites
                        <span class="font-normal">(5 minutes)</span>
                      </p>
                      <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                        That was easy! Download your keepers and enjoy your new professional photos.
                      </p>
                    </div>
                  </li>
                </ul>
              </div>

              <div class="relative hidden min-h-full w-px shrink-0 bg-[#92A0B5] bg-opacity-45 md:block">
                <span class="absolute left-1/2 top-1/2 w-5 -translate-x-1/2 -translate-y-1/2 bg-[#F5F5F5] py-2.5 text-center text-sm font-medium tracking-[-0.3px] text-slate-400">vs</span>
              </div>

              <div class="mt-4 w-full rounded-lg border border-primary-500/15 bg-white p-8 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] md:mt-0 lg:max-w-lg">
                <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/landing-page/physical-photoshoot.png" alt="" loading="lazy">

                <p class="mt-5 text-xl font-bold text-primary-500">
                  Physical photoshoot
                </p>

                <ul class="mt-4 space-y-3 text-base font-normal tracking-[-0.056px] text-paragraph">
                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Find a photographer you like
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Contact them and wait for a reply
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Decide on a date and time you’re both available
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Find the right clothing to wear to the photo shoot
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Get in your car and drive to the photo studio
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Pose for your photos and pick your favorite shots
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Wait for the photographer to send you the photos
                  </li>

                  <li class="flex items-center gap-2.5">
                    <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
                    </svg>
                    Update your professional profiles with your new photos
                  </li>
                </ul>
              </div>
            </div>

            <div class="relative mt-8 text-center sm:mt-12">
              <nuxt-link
                :to="`/auth/login?redirect=${encodeURIComponent('/app')}`"
                title=""
                class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-6 pb-3.5 pt-2.5 text-lg font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                role="button"
              >
                Get your headshots within 2 hours
              </nuxt-link>

              <blockquote class="mx-auto mt-6 max-w-md">
                <img class="mx-auto h-4 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                <p class="mt-3 text-sm font-medium italic tracking-[-0.2px] text-paragraph">
                  “I updated my Linkedin profile image with this, cheaper than a studio pro with quality level better than home pro.”
                </p>
                <div class="mt-3 flex items-center justify-center gap-2">
                  <img class="size-8 rounded-full object-cover" src="@/assets/img/landing-page/avatar-13.jpg" alt="" loading="lazy">
                  <p class="text-sm font-bold tracking-[-0.2px] text-paragraph">
                    Mark
                  </p>
                </div>
              </blockquote>

              <div class="absolute left-auto top-0 hidden gap-4 lg:flex lg:translate-x-8 lg:items-center xl:translate-x-40">
                <p class="rotate-[-12deg] py-4 text-right font-cursive text-base leading-4 tracking-[-0.056px] text-paragraph">
                  Same day results!
                </p>
                <svg class="-mt-12 h-12 w-auto text-paragraph" viewBox="0 0 56 51" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M47.1541 44.8739C44.8708 46.3571 42.6765 47.8339 40.156 49.466C41.3327 50.3929 42.7167 50.2496 43.8436 49.641C47.2242 47.8151 50.6146 45.9006 53.8566 43.7329C55.7149 42.5001 55.6452 40.7462 53.9247 39.9462C51.0771 38.5676 48.1207 37.3728 45.1742 36.0892C45.0061 36.0133 44.7985 35.8963 44.6601 35.9502C44.1164 36.0772 43.6024 36.334 43.0488 36.5497C43.1775 36.9802 43.1084 37.6008 43.3952 37.8C44.3742 38.5213 45.4124 39.1064 46.4012 39.7389C46.9352 40.0964 47.479 40.3651 47.8648 41.2609C46.8367 41.3787 45.7591 41.544 44.731 41.6618C23.5551 43.1825 6.96224 30.8738 3.20946 11.047C2.74404 8.4859 2.59496 5.85814 2.26794 3.24315C2.14903 2.32823 1.98071 1.46082 1.8618 0.545898C1.6443 0.517543 1.41693 0.577842 1.19943 0.549487C0.873343 1.1006 0.319895 1.71201 0.300293 2.28508C0.132791 4.18802 -0.0347098 6.09095 0.0647132 7.97471C0.979821 26.7301 14.2812 41.2539 33.3427 44.0627C36.9909 44.5923 40.8465 44.4473 44.5836 44.5747C45.3745 44.6059 46.1357 44.5072 46.9365 44.4497C47.065 44.4845 47.1342 44.6554 47.1541 44.8739Z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </section>
        <!-- END COMPARISON -->

        <!-- START FEATURES -->
        <section id="features" class="bg-[#F8FCFF] py-12 sm:py-16 lg:py-20 xl:py-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="flex items-start justify-between gap-8">
              <div>
                <p class="text-sm font-normal tracking-[-0.3px] text-paragraph sm:text-base">
                  ✨ Used by individuals, small teams and Fortune 500 companies
                </p>
                <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
                  Fix Your Professional Branding Overnight
                </h2>
                <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:max-w-2xl lg:text-xl">
                  Your AI headshots includes access to a suite of useful professional branding tools.
                </p>
              </div>

              <blockquote class="hidden max-w-sm lg:block xl:max-w-md">
                <img class="h-4 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                <p class="mt-2 text-sm font-medium italic leading-5 tracking-[-0.056px] text-[#474368]">
                  “Your product created INCREDIBLE pics. I have been trying for months to get professional pics, but there were no studios near me.”
                </p>
                <div class="mt-2 flex items-center gap-2">
                  <img class="size-10 rounded-full object-cover" src="@/assets/img/landing-page/avatar-2.jpg" alt="" loading="lazy">
                  <p class="text-base font-bold leading-4 tracking-[-0.056px] text-[#474368]">
                    Bart
                  </p>
                </div>
              </blockquote>
            </div>

            <div class="mt-8 grid grid-cols-1 gap-4 sm:mt-12 sm:gap-6 lg:grid-cols-2">
              <div class="border-primary-50/15 rounded-lg border bg-white p-6 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)]">
                <div class="sm:flex sm:items-center sm:gap-6">
                  <img class="w-full shrink-0 rounded-[10px] object-cover sm:h-36 border border-gray-100 sm:w-auto" src="@/assets/img/landing-page/feature-1.png" alt="" loading="lazy">
                  <div class="mt-3 min-w-0 flex-1 sm:mt-0">
                    <h3 class="text-xl font-bold leading-8 tracking-[-0.3px] text-primary-500">
                      Create Email Signature
                    </h3>
                    <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                      With only a few clicks, get your new headshot automatically formatted into a professional email signature.
                    </p>
                  </div>
                </div>
              </div>

              <div class="border-primary-50/15 rounded-lg border bg-white p-6 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)]">
                <div class="sm:flex sm:items-center sm:gap-6">
                  <img class="w-full shrink-0 rounded-[10px] object-cover sm:h-36 border border-gray-100 sm:w-auto" src="@/assets/img/landing-page/feature-2.png" alt="" loading="lazy">
                  <div class="mt-3 min-w-0 flex-1 sm:mt-0">
                    <h3 class="text-xl font-bold leading-8 tracking-[-0.3px] text-primary-500">
                      Customize Your Headshots
                    </h3>
                    <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                      You can make last minute tweaks to your completed headshots, including adjustments to the clothing, pose, and style.
                    </p>
                  </div>
                </div>
              </div>

              <div class="border-primary-50/15 rounded-lg border bg-white p-6 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)]">
                <div class="sm:flex sm:items-center sm:gap-6">
                  <img class="w-full shrink-0 rounded-[10px] object-cover sm:h-36 border border-gray-100 sm:w-auto" src="@/assets/img/landing-page/feature-3.png" alt="" loading="lazy">
                  <div class="mt-3 min-w-0 flex-1 sm:mt-0">
                    <h3 class="text-xl font-bold leading-8 tracking-[-0.3px] text-primary-500">
                      Create Profile Pictures
                    </h3>
                    <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                      Swap in dozens of unique backdrops to keep your new headshot looking fresh across all your social profiles.
                    </p>
                  </div>
                </div>
              </div>

              <div class="border-primary-50/15 rounded-lg border bg-white p-6 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)]">
                <div class="sm:flex sm:items-center sm:gap-6">
                  <img class="w-full shrink-0 rounded-[10px] object-cover sm:h-36 border border-gray-100 sm:w-auto" src="@/assets/img/landing-page/feature-4.png" alt="" loading="lazy">
                  <div class="mt-3 min-w-0 flex-1 sm:mt-0">
                    <h3 class="text-xl font-bold leading-8 tracking-[-0.3px] text-primary-500">
                      LinkedIn Preview
                    </h3>
                    <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                      Preview exactly how each of your headshots will look like as your LinkedIn profile picture before you even download them.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- END FEATURES -->

        <!-- START PRICING -->
        <section id="pricing" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="relative mx-auto max-w-2xl text-left md:text-center">
              <div class="flex items-center justify-start gap-x-3 md:justify-center">
                <img class="h-6 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
                <img class="h-6 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="" loading="lazy">
              </div>
              <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
                Professional headshots for 8x less than a physical photo shoot
              </h2>
              <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-lg">
                The average cost of professional headshots in the United States is
                <a target="_blank" href="/blog/how-much-does-a-headshot-cost" class="underline">$232.50*.</a>
                Our packages start from $29.
              </p>

              <div class="absolute left-auto top-0 hidden -translate-y-24 translate-x-6 flex-col items-end gap-4 lg:flex">
                <p class="rotate-[12deg] font-cursive leading-4 tracking-[-0.056px] text-paragraph text-[12px]">
                  We won't let you leave without at least
                  <br>
                  a handful of good headshots
                </p>
                <svg class="h-12 w-auto -translate-x-24 text-paragraph" viewBox="0 0 36 51" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M29.1188 47.8875C27.1192 48.4038 25.1874 48.9399 22.9811 49.5058C23.6782 50.5531 24.7413 50.827 25.7038 50.668C28.5914 50.1911 31.5038 49.648 34.3552 48.867C35.9876 48.4269 36.2813 47.0455 35.1526 45.9455C33.2952 44.0824 31.3203 42.3316 29.3703 40.5148C29.2596 40.4091 29.1274 40.2605 29.0133 40.2638C28.5817 40.2111 28.1468 40.2675 27.6904 40.2809C27.7017 40.651 27.5277 41.1137 27.7029 41.3482C28.2926 42.1808 28.9536 42.9241 29.5682 43.6905C29.897 44.1167 30.2506 44.4768 30.3625 45.2797C29.5705 45.0851 28.7323 44.9135 27.9403 44.7189C11.8074 40.0059 1.82763 25.8296 2.93039 9.38873C3.08729 7.27041 3.49387 5.18835 3.76488 3.06669C3.85634 2.32312 3.90149 1.60269 3.99295 0.859119C3.83591 0.776563 3.65403 0.760107 3.49699 0.677551C3.14453 1.01475 2.61019 1.3355 2.48256 1.77507C1.98217 3.20615 1.48178 4.63724 1.18475 6.12773C-1.82848 20.9468 5.2536 35.9272 18.9521 43.4136C21.5754 44.8402 24.4868 45.8008 27.2558 46.9398C27.8411 47.1841 28.4296 47.3194 29.0397 47.4976C29.129 47.5604 29.1471 47.7124 29.1188 47.8875Z"
                  />
                </svg>
              </div>
            </div>

            <div class="mx-auto mt-8 grid max-w-5xl grid-cols-1 items-center gap-4 sm:mt-12 md:grid-cols-3">
              <div class="divide-y divide-[#E4E4E7] divide-opacity-80 rounded-lg border border-primary-500/15 bg-white shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)]">
                <div class="px-5 pb-4 pt-5">
                  <h2 class="text-sm font-medium leading-6 tracking-[-0.2px] text-primary-500">
                    Basic
                  </h2>
                  <p class="mt-4 text-[40px] font-bold leading-6 tracking-[-0.2px] text-primary-500">
                    $29
                  </p>
                  <p class="mt-6 text-sm font-medium leading-5 tracking-[-0.3px] text-paragraph">
                    Get 40 headshots with 4 unique backgrounds and outfits.
                  </p>
                </div>

                <div class="space-y-4 px-5 pb-5 pt-4">
                  <ul class="space-y-2.5 text-sm font-medium leading-4 text-paragraph">
                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-paragraph" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      3 hours turnaround time
                    </li>

                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-paragraph" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      40 headshots
                    </li>

                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-paragraph" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      4 unique styles
                    </li>
                  </ul>
                  <div>
                    <nuxt-link :to="`/auth/login?redirect=${encodeURIComponent('/app/add?step=3')}`">
                      <button type="button" class="flex w-full items-center justify-center rounded-lg bg-white px-4 py-2 text-sm font-medium leading-6 text-primary-500 shadow-sm ring-1 ring-gray-200 transition-all duration-150 hover:bg-gray-50 lg:h-10">
                        Get 40 headshots in 3 hours
                      </button>
                    </nuxt-link>
                  </div>
                </div>
              </div>

              <div class="relative">
                <div class="absolute inset-x-0 top-0 translate-y-px transform">
                  <div class="flex -translate-y-1/2 transform justify-center">
                    <span class="rounded-full bg-[#21B8BA] px-3 py-1 text-xs font-extrabold uppercase tracking-wide text-white">
                      100% Money Back Guaranteed
                    </span>
                  </div>
                </div>

                <div class="divide-y divide-gray-200 rounded-lg border border-[#21B8BA] bg-white shadow-lg">
                  <div class="px-5 pb-4 pt-8">
                    <h2 class="text-sm font-medium leading-6 tracking-[-0.2px] text-primary-500">
                      Professional
                    </h2>
                    <p class="mt-4 text-[40px] font-bold leading-6 tracking-[-0.2px] text-primary-500">
                      $39
                    </p>
                    <p class="mt-6 text-sm font-medium leading-5 tracking-[-0.3px] text-paragraph">
                      Get 100 headshots with 10 unique backgrounds and outfits.
                    </p>
                  </div>

                  <div class="space-y-4 px-5 pb-8 pt-4">
                    <ul class="space-y-2.5 text-sm font-bold text-primary-500">
                      <li class="flex items-center gap-1">
                        <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                          />
                        </svg>
                        2 hours turnaround time
                      </li>

                      <li class="flex items-center gap-1">
                        <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                          />
                        </svg>
                        100 headshots
                      </li>

                      <li class="flex items-center gap-1">
                        <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                          />
                        </svg>
                        10 unique backgrounds
                      </li>

                      <li class="flex items-center gap-1">
                        <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                          />
                        </svg>
                        10 unique clothing styles
                      </li>

                      <li class="flex items-center gap-1">
                        <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                          />
                        </svg>
                        10 edit credits
                      </li>
                    </ul>
                    <div>
                      <nuxt-link :to="`/auth/login?redirect=${encodeURIComponent('/app/add?step=3')}`">
                        <button type="button" class="inline-flex w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-primary-500 px-4 py-2 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 lg:h-11">
                          Get 100 headshots in 2 hours!
                        </button>
                      </nuxt-link>
                    </div>
                  </div>
                </div>
              </div>

              <div class="divide-y divide-[#E4E4E7] divide-opacity-80 rounded-lg border border-primary-500/15 bg-white shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)]">
                <div class="px-5 pb-4 pt-5">
                  <h2 class="text-sm font-medium leading-6 tracking-[-0.2px] text-primary-500">
                    Executive
                  </h2>
                  <p class="mt-4 text-[40px] font-bold leading-6 tracking-[-0.2px] text-primary-500">
                    $59
                  </p>
                  <p class="mt-6 text-sm font-medium leading-5 tracking-[-0.3px] text-paragraph">
                    Get 200 headshots with 20 unique backgrounds and outfits.
                  </p>
                </div>

                <div class="space-y-4 px-5 pb-5 pt-4">
                  <ul class="space-y-2.5 text-sm font-medium leading-4 text-paragraph">
                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      1 hour turnaround time
                    </li>

                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      200 headshots
                    </li>

                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      20 unique styles
                    </li>
                    <li class="flex items-center gap-1">
                      <svg aria-hidden="true" class="size-5 shrink-0 text-green-500" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M16.7038 4.65254C16.7824 4.71217 16.8484 4.7867 16.8981 4.87187C16.9478 4.95704 16.9802 5.05117 16.9935 5.14889C17.0068 5.2466 17.0007 5.34598 16.9755 5.44132C16.9503 5.53667 16.9066 5.62612 16.8468 5.70455L8.84685 16.2045C8.78195 16.2896 8.69962 16.3598 8.60536 16.4104C8.51111 16.461 8.40712 16.4909 8.30038 16.498C8.19363 16.5051 8.0866 16.4892 7.98647 16.4516C7.88635 16.4139 7.79544 16.3552 7.71985 16.2795L3.21985 11.7795C3.08737 11.6374 3.01524 11.4493 3.01867 11.255C3.0221 11.0607 3.10081 10.8753 3.23822 10.7379C3.37564 10.6005 3.56102 10.5218 3.75532 10.5184C3.94963 10.5149 4.13767 10.5871 4.27985 10.7195L8.17385 14.6125L15.6538 4.79555C15.7742 4.6375 15.9524 4.53367 16.1493 4.50686C16.3461 4.48005 16.5456 4.53245 16.7038 4.65254Z"
                        />
                      </svg>
                      20 edit credits
                    </li>
                  </ul>
                  <div>
                    <nuxt-link :to="`/auth/login?redirect=${encodeURIComponent('/app/add?step=3')}`">
                      <button type="button" class="flex w-full items-center justify-center rounded-lg bg-white px-4 py-2 text-sm font-medium leading-6 text-primary-500 shadow-sm ring-1 ring-gray-200 transition-all duration-150 hover:bg-gray-50 lg:h-10">
                        Get 200 headshots in 1 hour
                      </button>
                    </nuxt-link>
                  </div>
                </div>
              </div>
            </div>

            <div class="mx-auto mt-8 grid max-w-2xl grid-cols-3 gap-5 sm:mt-12 sm:grid-cols-4 xl:grid-cols-6">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-hubspot.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-shopify.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-ebay.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-dell.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-box.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-stack-overflow.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-harsco.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-rogers.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-berkeley.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-ncr.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-okta.png" alt="" loading="lazy">
              <img class="h-8 w-auto object-contain" src="@/assets/img/landing-page/logo-warner-media.png" alt="" loading="lazy">
            </div>

            <div class="mx-auto mt-8 max-w-5xl sm:mt-12">
              <p class="text-left text-base font-bold tracking-tight text-primary-500 sm:text-lg md:text-center">
                Trusted by {{ $store.state.stats.users }}+ happy customers
                <span class="hidden md:inline">and counting.</span>
              </p>

              <div class="mx-auto mt-6 grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3">
                <div class="flex items-center gap-4">
                  <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-14.jpg" alt="" loading="lazy">
                  <div class="min-w-0 flex-1 space-y-1.5">
                    <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-4.png" alt="" loading="lazy">
                    <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                      “I needed a business photo with a 24 hour turnaround - you beat it by 22 hours!”
                    </p>
                    <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                      Brian Foster
                    </p>
                  </div>
                </div>

                <div class="flex items-center gap-4">
                  <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-15.jpg" alt="" loading="lazy">
                  <div class="min-w-0 flex-1 space-y-1.5">
                    <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                    <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                      “Didn't have time to get a new headshot (and I really hate taking pictures!)”
                    </p>
                    <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                      Sally
                    </p>
                  </div>
                </div>

                <div class="flex items-center gap-4">
                  <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-16.jpg" alt="" loading="lazy">
                  <div class="min-w-0 flex-1 space-y-1.5">
                    <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                    <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                      Headshot Pro saved me hundreds of dollars and a lot of time. The pictures were realistic and looked like me. I'm very happy with the quality of the photos.
                    </p>
                    <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                      Rebecca Lane
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <!-- END PRICING -->

        <!-- START FAQS -->
        <section id="faq" class="bg-[#F8FCFF] py-12 sm:py-16 lg:py-20 xl:py-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="mx-auto max-w-2xl text-left md:text-center">
              <p class="text-sm font-normal tracking-[-0.3px] text-paragraph sm:text-base">
                <span class="lg:hidden">👑 You have full ownership of your photos</span>
                <span class="hidden lg:block">👑 You have full commercial rights and ownership of your photos</span>
              </p>
              <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
                Frequently Asked Questions
              </h2>
              <p class="mt-3 text-base font-medium text-paragraph sm:text-lg">
                Answers to common questions about our professional AI generated
                <br class="hidden lg:block">
                headshot service for individuals and remote teams.
              </p>
            </div>

            <div class="mx-auto mt-8 grid max-w-5xl grid-cols-1 gap-6 sm:mt-12 md:grid-cols-2 md:gap-11">
              <!-- <div class="space-y-6"> -->
              <div v-for="item in faqItems" :key="item.question">
                <p class="text-base font-bold leading-8 tracking-[-0.3px] text-primary-500 sm:text-lg">
                  {{ item.question }}
                </p>
                <p class="mt-2 text-base font-normal leading-[26px] text-paragraph" v-html="item.answer" />
              </div>
              <!-- </div> -->
            </div>
          </div>
        </section>
        <!-- END FAQS -->

        <!-- START CTA -->
        <section id="cta" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-24">
          <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
            <div class="mx-auto max-w-3xl text-left md:text-center">
              <p class="text-sm font-normal text-paragraph sm:text-base lg:text-lg">
                🇳🇱 Founded in
                <span class="font-bold">Holland.</span>
                We respect your privacy.
              </p>
              <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-4xl">
                We're Not Perfect. But We're The Best.
              </h2>
            </div>

            <div class="mt-8 grid grid-cols-1 gap-4 sm:mt-12 md:grid-cols-3">
              <div class="rounded-lg border border-primary-500/15 bg-white px-6 pb-6 pt-8 shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)]">
                <img class="size-12" src="@/assets/img/landing-page/icon-thumb.svg" alt="" loading="lazy">
                <h3 class="mt-5 text-lg font-bold leading-tight tracking-[-0.3px] text-primary-500">
                  AI Headshots You Can Actually Use
                </h3>
                <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                  If you don't get a single profile-worthy headshot, we'll
                  <span class="font-bold">refund your entire purchase.</span>
                </p>
              </div>

              <div class="rounded-lg border border-primary-500/15 bg-white px-6 pb-6 pt-8 shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)]">
                <img class="size-12" src="@/assets/img/landing-page/icon-user-secure.svg" alt="" loading="lazy">
                <h3 class="mt-5 text-lg font-bold leading-tight tracking-[-0.3px] text-primary-500">
                  No Recurring Charges
                </h3>
                <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                  No subscriptions or recurring charges here. You have
                  <span class="font-bold">full commercial rights and ownership</span>
                  of your photos, allowing you to use them wherever you desire without any restrictions.
                </p>
              </div>

              <div class="rounded-lg border border-primary-500/15 bg-white px-6 pb-6 pt-8 shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)]">
                <img class="size-12" src="@/assets/img/landing-page/icon-lock-secure.svg" alt="" loading="lazy">
                <h3 class="mt-5 text-lg font-bold leading-tight tracking-[-0.3px] text-primary-500">
                  Your Photos Will Never Be Sold
                </h3>
                <p class="mt-2 text-base font-normal tracking-[-0.3px] text-paragraph">
                  We are an independently owned company that
                  <span class="font-bold">takes privacy seriously</span>
                  . All input photos are deleted after 7 days. Users have access to a ‘Delete’ button to instantly erase their data faster.
                </p>
              </div>
            </div>

            <div class="mx-auto mt-8 hidden max-w-5xl grid-cols-1 gap-6 sm:mt-12 md:grid md:grid-cols-3">
              <div class="flex items-center gap-4">
                <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-14.jpg" alt="" loading="lazy">
                <div class="min-w-0 flex-1 space-y-1.5">
                  <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-4.png" alt="" loading="lazy">
                  <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                    “I needed a business photo with a 24 hour turnaround - you beat it by 22 hours!”
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                    Brian Foster
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-4">
                <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-15.jpg" alt="" loading="lazy">
                <div class="min-w-0 flex-1 space-y-1.5">
                  <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                  <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                    “Didn't have time to get a new headshot (and I really hate taking pictures!)”
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                    Sally
                  </p>
                </div>
              </div>

              <div class="flex items-center gap-4">
                <img class="size-14 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-16.jpg" alt="" loading="lazy">
                <div class="min-w-0 flex-1 space-y-1.5">
                  <img class="h-3 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="" loading="lazy">
                  <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                    Headshot Pro saved me hundreds of dollars and a lot of time. The pictures were realistic and looked like me. I'm very happy with the quality of the photos.
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                    Rebecca Lane
                  </p>
                </div>
              </div>
            </div>

            <div class="relative mt-8 flex items-center justify-center sm:mt-12">
              <nuxt-link
                :to="`/auth/login?redirect=${encodeURIComponent('/app')}`"
                title=""
                class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-6 pb-3.5 pt-2.5 text-lg font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
                role="button"
              >
                Choose your headshot package
              </nuxt-link>

              <div class="absolute right-auto top-0 hidden translate-x-80 lg:block">
                <p class="rotate-[12deg] font-cursive leading-4 tracking-[-0.056px] text-paragraph text-[12px]">
                  If you're not happy, we will
                  <br>
                  refund your full purchase
                </p>
                <svg class="-ml-6 -mt-4 h-10 w-auto text-paragraph" viewBox="0 0 59 42" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.66614 22.083C8.61245 23.967 9.50382 25.809 10.5502 27.8855C9.46822 27.9516 8.62906 27.273 8.11869 26.4189C6.58755 23.8566 5.08123 21.2357 3.75924 18.5229C2.99812 16.9739 3.65927 15.9282 5.04612 16.172C7.36079 16.5421 9.68076 17.0712 12.0256 17.5417C12.1602 17.5669 12.3348 17.5838 12.4048 17.6759C12.7097 17.9858 12.9498 18.3626 13.2298 18.7311C12.9958 18.9402 12.8221 19.3502 12.5678 19.35C11.6851 19.3744 10.8123 19.29 9.95444 19.2559C9.48565 19.2471 9.04169 19.1798 8.47894 19.5644C9.09834 20.0754 9.7328 20.6367 10.3522 21.1477C23.4279 31.1179 38.4176 30.6525 47.7967 20.0973C48.9958 18.7256 50.015 17.178 51.1441 15.7141C51.5421 15.2039 51.955 14.7439 52.353 14.2337C52.5027 14.3091 52.6277 14.4431 52.7774 14.5186C52.7934 14.9956 52.9342 15.6067 52.7454 15.9665C52.1844 17.2048 51.6234 18.443 50.8975 19.5556C43.7187 30.665 30.0661 33.8934 16.8279 27.4803C14.2971 26.248 11.87 24.5135 9.42336 22.9967C8.90409 22.6783 8.44951 22.2929 7.95505 21.9159C7.86023 21.8823 7.75566 21.9576 7.66614 22.083Z"
                  />
                </svg>
              </div>
            </div>

            <div class="mt-6 space-y-3 px-12 text-center md:hidden">
              <img class="mx-auto h-3 w-auto" src="@/assets/img/landing-page/ratings-4.png" alt="" loading="lazy">
              <p class="text-xs font-medium italic leading-4 tracking-[-0.2px] text-paragraph">
                “This service made the price simple and made my photos look great!”
              </p>
              <div class="flex items-center justify-center gap-2">
                <img class="size-8 shrink-0 rounded-full" src="@/assets/img/landing-page/avatar-17.jpg" alt="" loading="lazy">
                <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph">
                  Amber Durnal
                </p>
              </div>
            </div>
          </div>
        </section>
        <!-- END CTA -->
      </main>
      <!-- END MAIN -->

      <!-- START FOOTER -->
      <footer id="footer" class="bg-zinc-900 py-12 sm:py-16 lg:pt-20">
        <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
          <div class="max-w-5xl space-y-6">
            <a href="#" title="" class="">
              <img class="h-6 w-auto" src="@/assets/img/logo-white.svg" alt="" loading="lazy">
            </a>

            <p class="text-base font-medium text-white/80">
              © 2021-2024, All Rights Reserved. Get professional business headshots in minutes with our AI headshot generator. Upload photos, pick your styles & receive 100+ headshots.
            </p>

            <div class="flex flex-wrap items-center gap-6">
              <div class="flex items-center gap-3">
                <img class="h-6 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
                <img class="h-6 w-auto" src="@/assets/img/landing-page/logo-trustpilot-white.png" alt="" loading="lazy">
              </div>

              <div class="flex items-center gap-3">
                <div class="flex -space-x-2 overflow-hidden">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-gray-900" src="@/assets/img/landing-page/avatar-1.jpg" alt="" loading="lazy">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-gray-900" src="@/assets/img/landing-page/avatar-2.jpg" alt="" loading="lazy">
                  <img class="inline-block h-6 w-6 rounded-full ring-2 ring-gray-900" src="@/assets/img/landing-page/avatar-3.jpg" alt="" loading="lazy">
                </div>
                <p class="-mt-0.5 text-base font-normal text-white">
                  Trusted by
                  <span class="font-bold">{{ $store.state.stats.users }}+</span>
                  happy customers
                </p>
              </div>
            </div>
          </div>

          <hr class="mt-8 border-white/10 sm:mt-12">

          <div class="mt-8 grid grid-cols-2 gap-8 sm:mt-12 sm:grid-cols-3 sm:gap-12 xl:grid-cols-5">
            <LandingpageV2FooterNav
              title="Links"
              :items="[
                { title: 'Teams', href: '/corporate-headshots' },
                { title: 'Example', href: '/reviews' },
                { title: 'Pricing', href: '/#pricing' },
                { title: 'Blog', href: '/blog' },
                { title: 'Login', href: '/auth/login' },
                { title: 'Reset password', href: '/auth/reset-password' },

              ]"
            />

            <LandingpageV2FooterNav
              title="Support"
              :items="[
                { title: 'Contact us', href: 'mailto:<EMAIL>', external: true },
                { title: 'Affilaite', href: '/affiliate' },
                { title: 'API', href: '/api' },
                { title: 'Compare HeadshotPro', href: '/best-ai-headshot-generators' },
              ]"
            />

            <LandingpageV2FooterNav
              title="Legal"
              :items="[
                { title: 'Refund Policy', href: '/legal/refund-policy' },
                { title: 'Terms & Conditions', href: '/legal/terms-and-conditions' },
                { title: 'Privacy Policy', href: '/legal/privacy-policy' },
                { title: 'Sub-processors', href: '/legal/sub-processors' },
              ]"
            />

            <LandingpageV2FooterNav title="Free Tools" :items="toolNavigation.map((item) => ({ title: item.title, href: item.url }))" />
            <LandingpageV2FooterNav title="Headshot Types" :items="headshotNavigation.map((item) => ({ title: item.title, href: item.url }))" />
          </div>

          <!-- <hr class="mt-8 border-white/10 sm:mt-12">

          <div class="mt-8 sm:mt-12 md:flex md:items-center md:justify-between md:gap-6">
            <div />
            <p class="mt-12 text-base font-normal text-white md:mt-0">
              © 2021-2024, All Rights Reserved
            </p>
          </div> -->
        </div>
      </footer>
      <!-- END FOOTER -->
    </div>
    <!-- END WRAPPER -->
  </div>
</template>

<script>
export default {
  head () {
    return {
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com' + this.$route.path
        }
      ]
    }
  },
  computed: {
    toolNavigation () {
      return [
        { title: 'Free PFP Generator', url: '/tools/free-headshot-generator', alt: 'Free PFP Generator' },
        { title: 'Free LinkedIn Headline Generator', url: '/tools/free-linkedin-headline-generator', alt: 'Free LinkedIn Headline Generator' },
        { title: 'Free LinkedIn Bio Generator', url: '/tools/free-linkedin-bio-generator', alt: 'Free LinkedIn Bio Generator' },
        { title: 'Free LinkedIn Profile Generator', url: '/tools/free-linkedin-profile-generator', alt: 'Free LinkedIn Profile Generator' },
        { title: 'Free Email Signature Generator', url: '/tools/free-email-signature-generator', alt: 'Free Email Signature Generator' },
        { title: 'Free Team Page Generator', url: '/tools/free-team-page-generator', alt: 'Free Team Page Generator' }
      ]
    },
    headshotNavigation () {
      return this.$store.state?.navigation?.headshotTypes || []
    },
    showcaseItems () {
      return [
        {
          usecase: 'Use on your LinkedIn profile',
          quote: 'I was looking for professional work photos for my LinkedIn so this is perfect',
          name: 'Douglas Burd',
          title: 'Master of Business Administration, MBA at Universidad de la Tercera Edad',
          avatar: require('@/assets/img/landing-page/avatar-4.jpg'),
          photo: require('@/assets/img/landing-page/portrait-1.jpg')
        },
        {
          usecase: "Use on your company's website",
          quote: 'In a jam to send a prof headshot to my company and this worked amazingly.',
          name: 'Cheryl Heilman',
          title: 'President at Bankers Life Securities',
          avatar: require('@/assets/img/landing-page/avatar-5.jpg'),
          photo: require('@/assets/img/landing-page/portrait-2.jpg')
        },
        {
          usecase: 'Use on your resume/CV',
          quote: 'Easy to use + done in time as promised. Would recommend!',
          name: 'Byron Veasey',
          title: 'Data Quality Engineering Leader at Southern Company',
          avatar: require('@/assets/img/landing-page/avatar-6.jpg'),
          photo: require('@/assets/img/landing-page/portrait-3.jpg')
        }
      ]
    },
    faqItems () {
      return [
        {
          question: 'What kind of photos do I need to upload?',
          answer: 'Make variety a priority. Varied facial expressions and varied backgrounds, taken at various times of the day, are the keys to high quality input photos. Oh, and minimal makeup and accessories, please!'
        },
        {
          question: 'What do you do with my uploaded photos?',
          answer: 'The photos you upload are used to train our AI model so it can create realistic AI headshots. These input photos are deleted within 7 days, but you can instantly delete them at any time with our ‘Delete’ button.'
        },
        {
          question: 'Who owns my AI photos?',
          answer: 'You do. We grant you full commercial license and ownership over your photos.'
        },
        {
          question: 'What if I don’t like my photos?',
          answer: 'No problem. If you don\'t get a single profile-worthy headshot, we\'ll refund your entire purchase. It\'s our Profile-Worthy guarantee.'
        },
        {
          question: 'Why should I choose HeadshotPro?',
          answer: 'HeadshotPro is the <a href="https://www.headshotpro.com/best-ai-headshot-generators" target="_blank" class="underline">best AI headshot generator</a>. Our AI headshots are photorealistic, include a profile-worthy guarantee, and are protected by customer-first privacy policies. You get full ownership over your photos.'
        },
        {
          question: 'How much do AI headshots cost?',
          answer: 'The final price of AI headshots depends on the amount of headshots and speed of turnaround time. The more headshots you purchase, the more likely you are to find multiple profile-worthy headshots. Click here to see how much HeadshotPro costs.'
        },
        {
          question: 'How long does an AI headshot take?',
          answer: 'We don’t cut corners when it comes to generating photorealistic AI headshots. We’re not the fastest, but you’ll always get same-day results with HeadshotPro. Our Executive package is delivered in 1 hour or less. '
        },
        {
          question: 'What do people misunderstand about AI headshots?',
          answer: 'Not every photo is perfect. Due to the nature of AI, you might see some strange photos. HeadshotPro tries to make this clear from the start: not every photo is perfect, but we promise you’ll find a profile-worthy headshot in every order to make it all worth it. '
        },
        {
          question: 'How many good photos can I expect?',
          answer: 'The amount of keeper headshots you get back will largely depend on the photos you provide us with. Customers who make an effort to follow the instructions closely often walk away with 8-10+ incredible photos. At the very least, we guarantee you’ll get a Profile-Worthy headshot back.'
        },
        {
          question: 'Is there a free AI headshot generator?',
          answer: 'HeadshotPro has a free AI headshot generator. The free version simply adds a new background to your existing photo, whereas our paid product creates hundreds of professional business headshots from scratch.'
        },
        {
          question: 'Can ChatGPT generate headshots?',
          answer: 'Try it! While it’s technically possible to use ChatGPT, DALL-E, or vanilla versions of DreamBooth to generate your own headshots, you may be disappointed to see the end results. HeadshotPro is the result of thousands of hours spent optimizing towards photorealistic AI headshots, and it shows. '
        },
        {
          question: 'Can I use AI headshots on LinkedIn?',
          answer: 'More than a quarter of HeadshotPro customers report using their AI headshots on LinkedIn. Our best AI headshots are photorealistic to the point of being indistinguishable from real photos. You can expect at least 3-6 photos of this quality in every order.'
        }
      ]
    }
  },
  mounted () {
    this.$posthog.capture('$page:homepage')
  },
  methods: {
    scrollToReviews (selector) {
      const reviewsSection = this.$el.querySelector(selector)
      if (reviewsSection) {
        reviewsSection.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }
}
</script>

<style></style>
