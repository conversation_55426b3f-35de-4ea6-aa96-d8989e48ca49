<template>
  <div>
    <AppTitle
      title="Clothing"
      sub-title="Select the clothing your team members are allowed to wear."
      with-mobile-actions
    >
      <ButtonWhite size="sm" @click="showFaq = true">
        <IconSmallQuestionMark class="size-4 text-[#ADB0B5] mr-1.5" />
        FAQ
      </ButtonWhite>
      <ButtonPrimary size="sm" @click="confirm()">
        Save changes
      </ButtonPrimary>
      <template #mobile>
        <ButtonPrimary size="sm" class="w-full" @click="confirm()">
          Save changes
        </ButtonPrimary>
      </template>
    </AppTitle>
    <main class="mt-6">
      <Card>
        <div class="flex items-center justify-between gap-2">
          <p class="flex-1 text-base font-medium tracking-tight text-primary-500">
            Team wardrobe
          </p>
          <div class="hidden sm:flex items-center justify-end gap-3">
            <template v-if="tab === 'selected'">
              <ButtonWhite v-if="numberOfSelectedClothingItems === 0" size="sm" @click="addRecommended">
                Add recommended
              </ButtonWhite>
              <ButtonWhite v-else size="sm" @click="removeAll">
                Remove all
              </ButtonWhite>
            </template>
            <template v-else>
              <ButtonWhite size="sm" @click="addAll">
                Add all
              </ButtonWhite>
            </template>
          </div>
          <div class="sm:hidden">
            <ButtonWhite size="sm" @click="expandFilters">
              <IconFilter class="size-4 mr-2" />
              Filters
            </ButtonWhite>
          </div>
        </div>

        <hr class="mt-4 border-gray-200">

        <OnboardingTeamClothingPicker
          ref="clothingPicker"
          @selected="selectedSomething"
          @count="numberOfSelectedClothingItems = $event"
          @next="showSuccessMessage"
          @tab="tab = $event"
        />
      </Card>
    </main>
    <FaqModal v-if="showFaq" :faqs="faqItems" @closeModal="showFaq = false" />
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      showFaq: false,
      tab: 'selected',
      numberOfSelectedClothingItems: 0,
      faqItems: [
        {
          question: 'What selected clothing do?',
          answer: 'Your team members will wear one of the selected clothing items for each backdrop you selected in the previous step.'
        },
        {
          question: 'Does everyone get the same?',
          answer: 'No, each team member can select their own clothing.'
        },
        {
          question: 'Can I let my team pick theirself?',
          answer: 'Yes, you can let your team pick their own clothing.'
        }
      ]
    }
  },
  head () {
    return {
      title: 'Clothing | HeadshotPro'
    }
  },
  computed: {
    clothing () {
      return this.$store.state.clothing
    }
  },
  mounted () {
    if (this.clothing.length === 0) {
      this.$store.dispatch('getClothing')
    }
  },
  methods: {
    selectedSomething () {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', true)
    },
    saveChangesBeforeLeaving () {
      this.confirm()
    },
    removeAll () {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', true)
      this.$refs.clothingPicker.removeAll()
    },
    confirm () {
      this.$refs.clothingPicker.confirm()
    },
    addRecommended () {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', true)
      this.$refs.clothingPicker.addRecommended()
    },
    addAll () {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', true)
      this.$refs.clothingPicker.addAll()
    },
    expandFilters () {
      this.$refs.clothingPicker.expandFilters()
    },
    showSuccessMessage () {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', false)
      this.$toast.success('Your backdrop selections have been saved')
    }
  }
}
</script>

<style></style>
