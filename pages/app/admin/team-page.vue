<template>
  <div class="w-full bg-gray-50 min-h-3/4">
    <HeaderApp class="z-10 relative" />
    <div class="py-10">
      <AppTitle
        title="Your team page"
        sub-title="Get a free professional and stylized team page for your website."
      >
        <LoadingSpinner v-if="isLoading" title="Loading..." />
      </AppTitle>
    </div>
    <div class="mx-auto mt-4 max-w-7xl space-y-4 px-4">
      <aside class="px-4 mb-12">
        <h2 class="font-bold">
          Your team members
        </h2>
        <p class="text-sm text-gray-600 mt-2 mb-4">
          You can only use the pictures they have marked as favorite.
        </p>
        <div class="grid gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
          <button
            v-for="member in teamMembers"
            :key="member._id"
            class="w-full flex flex-row items-center justify-start rounded-md border border-gray-100 bg-white px-4 py-2"
            :class="{
              'opacity-50': isAdded(member),
            }"
            @click="addMember(member)"
          >
            <img v-if="member?.thumbnail" class="w-8 h-8 rounded-full object-cover md:h-10 md:w-10 flex-shrink-0" :src="member.thumbnail" alt="">
            <div v-else class="w-8 h-8 rounded-full object-cover md:h-10 md:w-10 bg-gray-200 flex-shrink-0" />
            <p class="text-base text-left font-bold text-gray-900 ml-2 md:ml-3 flex-1 truncate">
              <span class="block truncate">{{ member.title }}</span>
              <span class="block text-xs text-gray-500 text-left font-normal">{{ member.pictures?.length || 0 }} pictures</span>
            </p>
            <div class="ml-auto pl-4 flex-shrink-0">
              <IconSolidCheck v-if="isAdded(member)" class="h-6 w-6 text-green-500" />
              <IconPlus v-else class="h-4 w-4 text-gray-500" />
            </div>
          </button>
        </div>
      </aside>
      <div v-if="teamPage?.members?.length > 0" class="pb-12">
        <ToolsTeamPageGenerator
          v-if="teamPage"
          :key="teamPage._id + '_' + teamPage.updatedAt"
          :team-page="teamPage"
          :team-id="teamPage._id"
          :max-members="100"
          :can-add="false"
          :has-stylizer="true"
          :members="teamMembers || []"
          @updated-team="start"
        />
      </div>
      <div v-else class="pb-12">
        <p class="text-center text-gray-600">
          Add your first team member to start creating your team page.
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      isLoading: false,
      teamPage: null,
      teamMembers: null
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com' + this.$route.path
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return 'Stylized Team Page Generator'
    },
    description () {
      return 'Get a free professional and stylized team page for your website.'
    }
  },
  mounted () {
    this.start()
  },
  methods: {
    start () {
      this.isLoading = true
      this.$axios.get('/tools/team-page/organization')
        .then((res) => {
          this.teamPage = res.data.data.page
          this.teamMembers = res.data.data.members
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error(err.response?.data?.errorMessage || 'Failed to create team. Please, try again later.')
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    isAdded (member) {
      return !!this.teamPage?.members?.find(m => m.id === member._id)
    },
    addMember (member) {
      if (this.isLoading || this.isAdded(member)) {
        return
      }

      const style = this.$route.query.style || 'default'
      if (style !== 'default') {
        this.$toast.error('You can only add new members when using the default style.')
        return
      }

      this.isLoading = true
      this.$axios.post(`/tools/team-page/page/${this.teamPage._id}/upsert`, {
        id: member._id,
        name: member.title,
        role: '',
        basePictureUrl: member.thumbnail || ''
      })
        .then((res) => {
          this.start()
        })
        .catch(() => {
          this.$toast.error('Error updating member')
          this.isLoading = false
        })
    }
  }
}
</script>

    <style>

    </style>
