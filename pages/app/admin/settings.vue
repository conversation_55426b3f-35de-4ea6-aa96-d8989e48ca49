<template>
  <div>
    <AppTitle title="Settings" sub-title="Everything related to settings here." />
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <div>
        <div>
          <OnboardingOrganization
            ref="organization"
            title="Organization information"
            subtitle="Tell us about your company"
            version="settings"
            @next="savedOrganization"
          />
        </div>
      </div>
      <div>
        <div>
          <TeamSettingsNotifications />
        </div>
      </div>
      <div>
        <div>
          <TeamSettingsAdminManagement />
        </div>
      </div>
      <div>
        <div>
          <TeamSettingsDataDeletion />
        </div>
      </div>
      <div>
        <div>
          <OrganizationBrandingConfigCard
            :show-header="false"
            title="Make your team's headshot match your brand"
            subtitle="Add a custom background to create matching profile pictures for your organisation. Every  headshot  includes an additional branded profile picture."
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  head () {
    return {
      title: 'Settings | HeadshotPro'
    }
  },
  methods: {
    savedOrganization () {
      this.$toast.success('Organization details saved')
    }
  }
}
</script>

<style></style>
