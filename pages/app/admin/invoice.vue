<template>
  <div>
    <LoadingWrapper :is-loading="isLoading" class="text-black" title="Generating invoice...">
      <AppTitle :title="'Invoice ' + charge.id" with-mobile-actions>
        <template #description>
          <div class="flex items-center gap-4 mt-2">
            <InvoiceStatusBadge :invoice="charge" />
            <div class="flex items-center gap-2 text-sm text-slate-500 font-medium">
              <p>{{ formatPrice(charge.amountCaptured / 100, charge.currency, 2, false) }}</p>
              <p>·</p>
              <p v-if="charge.paymentStatus === 'paid'">
                Paid on {{ formatDate(charge.updatedAt) }}
              </p>
            </div>
          </div>
        </template>
        <ButtonWhite size="sm" @click="print">
          <IconDocumentArrowDown class="w-4 h-4 mr-2.5" />
          Export
        </ButtonWhite>
        <template #mobile>
          <ButtonWhite size="sm" class="w-full" @click="print">
            <IconDocumentArrowDown class="w-4 h-4 mr-2.5" />
            Export
          </ButtonWhite>
        </template>
      </AppTitle>
      <div class="grid grid-cols-1 items-start md:grid-cols-3 gap-6 mt-6">
        <div class="md:order-last md:sticky md:top-0">
          <Card>
            <h2 class="text-lg font-bold text-primary-500">
              Billing information
            </h2>
            <p class="text-slate-500 font-medium">
              Add your billing data below to add it to the invoice.
            </p>
            <InputTextArea
              v-model="billingInformation"
              autocomplete="street-address"
              label="Bill to:"
              placeholder="i.e. 20 W 34th St., New York, NY 10001, United States"
              class="mt-4"
            />
          </Card>
        </div>
        <Card class="md:col-span-2 md:aspect-[210/297] w-full">
          <Invoice :charge="charge" :billing-information="billingInformation" :quantity-header="quantityHeader" />
        </Card>
      </div>
      <portal to="print">
        <Invoice :charge="charge" :billing-information="billingInformation" :quantity-header="quantityHeader" />
      </portal>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      billingInformation: null,
      charge: {
        id: null,
        status: null,
        transactionId: null,
        amountCaptured: null,
        created: null,
        currency: null,
        refunded: false,
        amountRefunded: 0
      },
      quantityHeader: 'Qty',
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Invoice | HeadshotPro',
      meta: [
        { name: 'robots', content: 'noindex' }
      ]
    }
  },
  watch: {
    billingInformation (newValue) {
      if (newValue) {
        this.billingInformation = this.sanitizeInput(newValue)
      }
    }
  },
  async mounted () {
    const invoices = await this.$axios.$get('/organization/invoice/all')
    const invoice = invoices.find(invoice => invoice._id === this.$route.query.id)

    if (!invoice) {
      return this.$router.push('/app/admin/invoices')
    }

    this.charge.id = invoice._id
    this.charge.transactionId = invoice.transactionId
    this.charge.amountCaptured = invoice.amountTotal
    this.charge.created = invoice.createdOn
    this.charge.currency = invoice.currency || 'usd'
    this.charge.refunded = invoice.status === 'refunded'
    this.charge.amountRefunded = this.charge.refunded ? invoice.amountRefunded : 0
    this.charge.status = invoice.status
    this.charge.updatedAt = invoice.updatedAt
    this.charge.paymentStatus = invoice.paymentStatus

    const isPackage = invoice.items?.length === 1 && invoice.items?.[0]?.productId === 'package'
    if (isPackage && invoice.items?.[0]?.id) {
      this.quantityHeader = 'Users'
      this.charge.items = [{
        id: invoice.items[0].id,
        name: invoice.items[0].name || 'Package',
        quantity: invoice.items[0].quantity || 1,
        amount: this.charge.amountCaptured / (invoice.items[0].quantity || 1),
        currency: invoice.currency
      }]
    }

    this.isLoading = false
  },
  methods: {
    print () {
      window.print()
    },
    sanitizeInput (input) {
      if (!input) { return input }
      return input.replace(/[<>]/g, match => match === '<' ? '&lt;' : '&gt;')
    }
  }
}
</script>
