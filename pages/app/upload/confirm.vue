<template>
  <PostcheckoutWrapper alignment="top">
    <template #left>
      <PostcheckoutNavWhiteButton @click="$router.push(localePath('/app/upload/select-style'))">
        {{ $t('Back') }}
      </PostcheckoutNavWhiteButton>
    </template>
    <template #right>
      <PostcheckoutNavBlueButton v-if="!isSaving" :disabled="!canSubmit" @click="submit">
        {{ $t('Submit my photos') }}
      </PostcheckoutNavBlueButton>
      <PostcheckoutNavBlueButton v-else class="cursor-not-allowed">
        <LoadingSpinnerWhite />
        {{ $t('Submitting...') }}
      </PostcheckoutNavBlueButton>
    </template>
    <section class="relative pt-8 pb-56 sm:pt-12 md:pb-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <PostcheckoutStepper :step="5" classes="md:justify-center" />

        <div class="mx-auto mt-4 text-left sm:mt-6 md:max-w-xl md:text-center">
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            <span class="md:hidden">
              {{ $t('Confirm your info') }}
            </span>
            <span class="hidden md:block">
              {{ $t('All done!') }}
            </span>
          </h1>
          <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
            <span class="md:hidden">
              {{ $t('Double check if everything is correct before we continue.') }}
            </span>
            <span class="hidden md:block">
              {{ $t('No changes can be made once we pass this page over to our AI photographer.') }}
            </span>
          </p>
        </div>

        <div class="max-w-4xl mx-auto mt-8">
          <div
            class="flex flex-col gap-6 md:p-6 md:bg-white md:border md:border-gray-200 md:rounded-lg md:shadow-sm lg:p-8"
          >
            <div class="md:order-3">
              <div class="flex items-center justify-between">
                <p class="text-base font-bold tracking-tight text-primary-500">
                  {{ $t('Your photos') }}
                </p>

                <button
                  type="button"
                  class="text-sm font-medium text-primary-500 gap-1.5 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 px-3 pt-1 pb-1.5 inline-flex items-center justify-center"
                  @click="changePhotos"
                >
                  {{ $t('Change photos') }}
                </button>
              </div>

              <!-- Helpful microcopy about photos visibility -->
              <div class="mt-3 p-2 bg-gray-50 border border-gray-200 rounded-lg">
                <p class="text-sm text-gray-600">
                  {{ $t('All your uploaded photos are shown below. Missing photos? Go back to add more or refresh if you just uploaded via phone.') }}
                </p>
              </div>

              <div class="grid grid-cols-5 sm:grid-cols-6 lg:grid-cols-8 xl:grid-cols-12 gap-2.5 mt-4">
                <ImageDns v-for="photo in goodPhotos" :key="photo.md5" class="object-cover w-full h-full rounded-lg aspect-square" :src="photo.url" alt="" />
              </div>
            </div>

            <div class="grid md:grid-cols-2 md:order-1">
              <div class="md:pr-6">
                <div class="flex items-center justify-between">
                  <p class="text-base font-bold tracking-tight text-primary-500">
                    {{ $t('Your info') }}
                  </p>

                  <button
                    type="button"
                    class="text-sm font-medium text-primary-500 gap-1.5 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 px-3 pt-1 pb-1.5 inline-flex items-center justify-center"
                    @click="changeInfo"
                  >
                    {{ $t('Edit info') }}
                  </button>
                </div>

                <div class="grid grid-cols-2 gap-5 mt-5">
                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Name') }}
                    </p>
                    <p class="mt-1 text-base font-medium text-gray-500">
                      {{ personalInfoSummary.name }}
                    </p>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Age') }}
                    </p>
                    <p class="mt-1 text-base font-medium text-gray-500">
                      {{ ageFromId(personalInfoSummary.age)?.title }}
                    </p>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Body Type') }}
                    </p>
                    <p class="mt-1 text-base font-medium text-gray-500">
                      {{ bodyTypeFromId(personalInfoSummary.bodyType)?.title || '-' }}
                    </p>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Height') }}
                    </p>
                    <p class="mt-1 text-base font-medium text-gray-500">
                      {{ heightFromId(personalInfoSummary.height)?.title || '-' }}
                    </p>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Weight') }}
                    </p>
                    <p class="mt-1 text-base font-medium text-gray-500">
                      {{ weightFromId(personalInfoSummary.weight)?.title || '-' }}
                    </p>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Ethnicity') }}
                    </p>
                    <p class="mt-1 text-base font-medium text-gray-500">
                      {{ ethnicityFromId(personalInfoSummary.ethnicity)?.title || '-' }}
                    </p>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Gender') }}
                    </p>
                    <div class="flex items-center gap-2 mt-1">
                      <IconMale v-if="personalInfoSummary.gender === 'male'" class="size-5 -mb-0.5" />
                      <IconMale v-if="personalInfoSummary.gender === 'female'" class="size-5 -mb-0.5" />
                      <p class="text-base font-medium text-gray-500">
                        {{ genderFromId(personalInfoSummary.gender) }}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Eye color') }}
                    </p>
                    <div class="flex items-center gap-2 mt-1">
                      <div class="rounded-full size-5 -mb-0.5" :style="{ backgroundColor: eyeColorFromId(personalInfoSummary.eyeColor)?.color }" />
                      <p class="text-base font-medium text-gray-500">
                        {{ eyeColorFromId(personalInfoSummary.eyeColor)?.label }}
                      </p>
                    </div>
                  </div>
                  <div>
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ $t('Glasses') }}
                    </p>
                    <p class="text-base font-medium text-gray-500">
                      {{ glassesFromId(personalInfoSummary.glasses)?.title }}
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-6 space-y-5 md:mt-0 md:pl-6 md:border-l md:border-gray-200">
                <div class="flex items-center justify-between">
                  <p class="text-base font-bold tracking-tight text-primary-500">
                    {{ $t('Your styles') }}
                  </p>

                  <button
                    type="button"
                    class="text-sm font-medium text-primary-500 gap-1.5 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 px-3 pt-1 pb-1.5 inline-flex items-center justify-center"
                    @click="changeStyles"
                  >
                    {{ $t('Edit styles') }}
                  </button>
                </div>

                <div>
                  <p class="text-sm font-medium tracking-tight text-primary-500">
                    {{ $t('Your selection') }}
                  </p>
                  <ul
                    class="grid grid-cols-1 mt-1.5 text-base font-medium list-outside pl-5 text-gray-500 list-disc gap-x-2 gap-y-0.5"
                  >
                    <li v-for="style in selectedStyles" :key="style.style + '_' + style.clothing">
                      {{ styleName(style.style) }} - {{ clothingName(style.clothing) }}
                    </li>
                  </ul>
                </div>

                <div class="mt-6 md:mt-0">
                  <p class="text-sm font-medium tracking-tight text-primary-500">
                    {{ $t('Pre-selected by us') }}
                  </p>
                  <ul
                    class="grid grid-cols-1 sm:grid-cols-2 mt-1.5 text-base font-medium list-outside pl-5 text-gray-500 list-disc gap-x-2 gap-y-0.5"
                  >
                    <li v-for="option in preselectedOptions" :key="option.style + '_' + option.clothing">
                      {{ option.style }} - {{ option.clothing }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <hr class="hidden border-gray-200 md:order-2 md:block">
          </div>

          <div class="items-center justify-between hidden gap-6 mt-4 md:flex">
            <div class="flex-1 min-w-0">
              <div class="relative flex items-center">
                <div class="flex items-center h-6">
                  <input
                    id="terms"
                    v-model="acceptsTerms"
                    type="checkbox"
                    name="terms"
                    class="w-4 h-4 text-teal-500 border-gray-300 rounded focus:ring-teal-500"
                  >
                </div>
                <div class="ml-3">
                  <label for="terms" class="text-sm font-normal leading-6 text-gray-900">
                    {{ $t('I agree to the') }}
                    <a href="/legal/terms-and-conditions" target="_blank" class="underline">{{ $t('terms and conditions') }}</a>, <a
                      href="/legal/privacy-policy"
                      title=""
                      class="underline"
                    >{{ $t('privacy policy') }}</a> {{ $t('and the') }} <button
                      type="button"
                      class="underline"
                      @click="openRequirementsModal"
                    >{{ $t('upload requirements') }}</button>.
                  </label>
                </div>
              </div>

              <div class="relative flex items-center mt-1">
                <div class="flex items-center h-6">
                  <input
                    id="upload-photos"
                    v-model="uploadsPhotos"
                    type="checkbox"
                    name="upload-photos"
                    class="w-4 h-4 text-teal-500 border-gray-300 rounded focus:ring-teal-500"
                  >
                </div>
                <div class="ml-3">
                  <label for="upload-photos" class="text-sm font-normal leading-6 text-gray-900">
                    {{ $t('I have uploaded my best photos and understand these will influence the final result.') }}
                  </label>
                </div>
              </div>
            </div>

            <div>
              <button
                v-if="!isSaving"
                type="button"
                class="text-sm inline-flex w-full sm:w-auto font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-12 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
                :disabled="!canSubmit"
                @click="submit"
              >
                {{ $t('Submit my photos') }}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  class="size-5 -mb-0.5"
                >
                  <path
                    d="M9.25 13.25a.75.75 0 0 0 1.5 0V4.636l2.955 3.129a.75.75 0 0 0 1.09-1.03l-4.25-4.5a.75.75 0 0 0-1.09 0l-4.25 4.5a.75.75 0 1 0 1.09 1.03L9.25 4.636v8.614Z"
                  />
                  <path
                    d="M3.5 12.75a.75.75 0 0 0-1.5 0v2.5A2.75 2.75 0 0 0 4.75 18h10.5A2.75 2.75 0 0 0 18 15.25v-2.5a.75.75 0 0 0-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5Z"
                  />
                </svg>
              </button>
              <button
                v-else
                type="button"
                class="text-sm inline-flex w-full sm:w-auto font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-12 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center cursor-not-allowed"
              >
                <LoadingSpinnerWhite />
                {{ $t('Submitting...') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
          <div class="relative flex items-start sm:items-center">
            <div class="flex items-center h-6">
              <input
                id="terms"
                v-model="acceptsTerms"
                type="checkbox"
                name="terms"
                class="w-4 h-4 text-teal-500 border-gray-300 rounded focus:ring-teal-500"
              >
            </div>
            <div class="ml-3">
              <label for="terms" class="text-sm font-normal leading-6 text-gray-900">
                {{ $t('I agree to the') }}
                <a href="/legal/terms-and-conditions" class="underline">{{ $t('terms and conditions') }}</a>, <a
                  href="/legal/privacy-policy"
                  class="underline"
                >{{ $t('privacy policy') }}</a> {{ $t('and the') }} <button type="button" class="underline" @click="openRequirementsModal">
                  {{ $t('upload requirements') }}
                </button>.
              </label>
            </div>
          </div>

          <div class="relative flex items-start mt-1 sm:items-center">
            <div class="flex items-center h-6">
              <input
                id="upload-photos"
                v-model="uploadsPhotos"
                type="checkbox"
                name="upload-photos"
                class="w-4 h-4 text-teal-500 border-gray-300 rounded focus:ring-teal-500"
              >
            </div>
            <div class="ml-3">
              <label for="upload-photos" class="text-sm font-normal leading-6 text-gray-900">
                {{ $t('I have uploaded my best photos and understand these will influence the final result.') }}
              </label>
            </div>
          </div>

          <div class="mt-4">
            <button
              v-if="!isSaving"
              type="button"
              class="text-sm inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
              :disabled="!canSubmit"
              @click="submit"
            >
              {{ $t('Submit my photos') }}
            </button>
            <button
              v-else
              type="button"
              class="text-sm inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center cursor-not-allowed"
            >
              <LoadingSpinnerWhite />
              {{ $t('Submitting...') }}
            </button>
          </div>
        </div>
      </div>
    </section>
  </PostcheckoutWrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  data () {
    return {
      acceptsTerms: false,
      uploadsPhotos: false,
      isSaving: false
    }
  },
  computed: {
    canSubmit () {
      if (this.isSaving) {
        return false
      }

      return this.photos.length >= this.minimumPhotos && this.personalInfoSummary.name && this.personalInfoSummary.age && this.personalInfoSummary.eyeColor && this.personalInfoSummary.gender && this.selectedStyles.length > 0 && this.acceptsTerms && this.uploadsPhotos
    },
    preselectedOptions () {
      // const additionalStylesPerPackage = {
      //   tiny: 0,
      //   small: 2,
      //   medium: 4,
      //   large: 10
      // }
      // return this.$store.state.onboarding.preselectedOptions.filter(option => option.genders.includes(this.$store.state.onboarding.selectedSex || 'male')).slice(0, additionalStylesPerPackage[this.$store.state.user.packages[0] || 'medium'])
      const preselectedOptions = this.$store.state.onboarding.preselectedOptions
      // TeamMembers get their non-selected styles prefilled until a max of 8 styles
      if (this.isTeamMember) {
        return preselectedOptions.slice(0, 8 - this.selectedStyles.length)
      }
      return preselectedOptions
    }
  },
  mounted () {
    if (this.photos.length < this.minimumPhotos) {
      this.$router.push(this.localePath('/app/upload/photos'))
    } else if (!this.$store.state.onboarding.selectedSex) {
      this.$router.push(this.localePath('/app/upload/personal-info'))
    } else if (this.$store.state.onboarding.selectedStyles.length === 0) {
      this.$router.push(this.localePath('/app/upload/select-style'))
    }

    this.$store.dispatch('onboarding/fetchPreselectedOptions')
    this.$posthog.capture('$post_checkout:confirmation')
  },
  methods: {
    changePhotos () {
      this.$router.push(this.localePath('/app/upload/photos'))
    },
    changeInfo () {
      this.$router.push(this.localePath('/app/upload/personal-info'))
    },
    changeStyles () {
      this.$router.push(this.localePath('/app/upload/select-style'))
    },
    submit () {
      if (!this.canSubmit) {
        return
      }

      try {
        if (this.isSaving) {
          this.$toast.error(this.$t('Please wait for the previous request to complete'))
          return
        }

        this.isSaving = true

        const body = {
          title: this.personalInfoSummary.name,
          gender: this.personalInfoSummary.gender,
          trainingImages: this.goodPhotos.map(photo => photo.path),
          ethnicity: this.personalInfoSummary.ethnicity || '',
          style: this.selectedStyles,
          eyeColor: this.personalInfoSummary.eyeColor,
          average: 0,
          age: this.personalInfoSummary.age,
          bodyType: this.personalInfoSummary.bodyType,
          height: this.personalInfoSummary.height,
          weight: this.personalInfoSummary.weight,
          glasses: this.personalInfoSummary.glasses
        }

        this.$axios.$post('/model/create', body)
          .then((response) => {
            const { success, errorMessage, message } = response
            if (!success) {
              if (message) {
                this.handleError({ message: 'Unauthenticated. Please, refresh the page and try again.' })
              } else {
                this.handleError(errorMessage ? { message: errorMessage } : {})
              }
            } else {
              this.handleSuccess()
              this.$posthog.capture('$post_checkout:model_submitted')
            }
            this.isSaving = false
          })
          .catch((err) => {
            if (err?.response?.data?.errorMessage) {
              switch (err?.response?.data?.errorMessage) {
                case 'Your organization has run out of credits.':
                  this.$popup.show({
                    title: this.$t('Not enough credits'),
                    message: this.$t('Your organization has run out of credits. Please request your team owner to purchase more to continue. Don\'t worry, we will save all your data so you can easily continue later.'),
                    buttonText: this.$t('Close'),
                    onButtonClick: () => this.$popup.hide()
                  })
                  break

                case 'You already did a photo shoot.':
                  this.$popup.show({
                    title: 'You already did a photo shoot.',
                    message: 'You have already done a photo shoot. If you need to create a new one, please contact your administrator to request a retry.',
                    buttonText: 'Close',
                    onButtonClick: () => this.$popup.hide()
                  })
                  break
                default:
                  if (err?.response?.data?.errorMessage) {
                    this.$toast.error(err?.response?.data?.errorMessage)
                  }
                  this.handleError(err?.response?.data?.errorMessage)
                  break
              }
            }
            this.isSaving = false
          })
      } catch (err) {
        this.handleError(err)
        this.isSaving = false
      }
    },
    handleSuccess () {
      this.$store.dispatch('onboarding/finish')
      this.$store.commit('user/SET_RETRY', false)
      this.$store.commit('user/DEDUCT_PACKAGE')
      this.$toast.success('Model created successfully')
      this.$router.push(this.localePath('/app/upload/waiting'))
    }
  }
}
</script>

<style>
  @media screen and (max-width: 767px) {
    .chat-button {
      bottom: 13rem;
    }
  }
</style>

<i18n>
  {
    "en": {
      "Submit my photos": "Submit my photos",
      "Submitting...": "Submitting...",
      "Confirm your info": "Confirm your info",
      "All done!": "All done!",
      "Double check if everything is correct before we continue.": "Double check if everything is correct before we continue.",
      "No changes can be made once we pass this page over to our AI photographer.": "No changes can be made once we pass this page over to our AI photographer.",
      "Your photos": "Your photos",
      "Change photos": "Change photos",
      "Your info": "Your info",
      "Edit info": "Edit info",
      "Name": "Name",
      "Age": "Age",
      "Ethnicity": "Ethnicity",
      "Gender": "Gender",
      "Eye color": "Eye color",
      "Your styles": "Your styles",
      "Edit styles": "Edit styles",
      "Your selection": "Your selection",
      "Pre-selected by us": "Pre-selected by us",
      "I have uploaded my best photos and understand these will influence the final result.": "I have uploaded my best photos and understand these will influence the final result.",
      "Submit my photos": "Submit my photos",
      "Not enough credits": "Not enough credits",
      "Your team has run out of credits. Please request your team owner to purchase more to continue.": "Your team has run out of credits. Please request your team owner to purchase more to continue.",
      "Please wait for the previous request to complete": "Please wait for the previous request to complete",
      "I agree to the": "I agree to the",
      "terms and conditions": "terms and conditions",
      "privacy policy": "privacy policy",
      "and the": "and the",
      "upload requirements": "upload requirements",
      "All your uploaded photos are shown below. Missing photos? Go back to add more or refresh if you just uploaded via phone.": "All your uploaded photos are shown below. Missing photos? Go back to add more or refresh if you just uploaded via phone."
    },
    "es": {
      "Submit my photos": "Enviar mis fotos",
      "Submitting...": "Enviando...",
      "Confirm your info": "Confirma tu información",
      "All done!": "¡Todo listo!",
      "Double check if everything is correct before we continue.": "Verifica si todo es correcto antes de continuar.",
      "No changes can be made once we pass this page over to our AI photographer.": "No se pueden hacer cambios una vez que pasemos esta página a nuestro fotógrafo de IA.",
      "Your photos": "Tus fotos",
      "Change photos": "Cambiar fotos",
      "Your info": "Tu información",
      "Edit info": "Editar información",
      "Name": "Nombre",
      "Age": "Edad",
      "Ethnicity": "Etnia",
      "Gender": "Género",
      "Eye color": "Color de ojos",
      "Your styles": "Tus estilos",
      "Edit styles": "Editar estilos",
      "Your selection": "Tu selección",
      "Pre-selected by us": "Preseleccionado por nosotros",
      "I have uploaded my best photos and understand these will influence the final result.": "He subido mis mejores fotos y entiendo que influirán en el resultado final.",
      "Not enough credits": "No hay suficientes créditos",
      "Your team has run out of credits. Please request your team owner to purchase more to continue.": "Tu equipo se ha quedado sin créditos. Por favor, solicita al propietario de tu equipo que compre más para continuar.",
      "Please wait for the previous request to complete": "Por favor, espera a que se complete la solicitud anterior",
      "I agree to the": "Estoy de acuerdo con los",
      "terms and conditions": "términos y condiciones",
      "privacy policy": "política de privacidad",
      "and the": "y los",
      "upload requirements": "requisitos de subida",
      "All your uploaded photos are shown below. Missing photos? Go back to add more or refresh if you just uploaded via phone.": "Todas tus fotos subidas se muestran a continuación. ¿Faltan fotos? Regresa para agregar más o actualiza si acabas de subir desde el móvil."
    },
    "de": {
      "Submit my photos": "Meine Fotos absenden",
      "Submitting...": "Wird gesendet...",
      "Confirm your info": "Bestätige deine Daten",
      "All done!": "Geschafft!",
      "Double check if everything is correct before we continue.": "Prüfe noch einmal alles, bevor wir fortfahren.",
      "No changes can be made once we pass this page over to our AI photographer.": "Danach sind keine Änderungen mehr möglich - unsere KI übernimmt ab hier.",
      "Your photos": "Deine Fotos",
      "Change photos": "Fotos ändern",
      "Your info": "Deine Daten",
      "Edit info": "Daten bearbeiten",
      "Name": "Name",
      "Age": "Alter",
      "Ethnicity": "Ethnie",
      "Gender": "Geschlecht",
      "Eye color": "Augenfarbe",
      "Your styles": "Deine Stile",
      "Edit styles": "Stile bearbeiten",
      "Your selection": "Deine Auswahl",
      "Pre-selected by us": "Von uns vorausgewählt",
      "I have uploaded my best photos and understand these will influence the final result.": "Ich habe meine besten Fotos hochgeladen und verstehe, dass diese das Endergebnis beeinflussen.",
      "Not enough credits": "Nicht genügend Credits",
      "Your organization has run out of credits. Please request your team owner to purchase more to continue. Don't worry, we will save all your data so you can easily continue later.": "Deine Organisation hat keine Credits mehr. Bitte den Team-Inhaber um weitere Credits bitten. Keine Sorge - alle deine Daten bleiben gespeichert.",
      "Close": "Schließen",
      "Your team has run out of credits. Please request your team owner to purchase more to continue.": "Dein Team hat keine Credits mehr. Bitte den Team-Inhaber um weitere Credits bitten.",
      "Please wait for the previous request to complete": "Bitte warten, bis die vorherige Anfrage abgeschlossen ist",
      "I agree to the": "Ich stimme den",
      "terms and conditions": "Nutzungsbedingungen",
      "privacy policy": "Datenschutzbestimmungen",
      "and the": "und den",
      "upload requirements": "Upload-Anforderungen",
      "All your uploaded photos are shown below. Missing photos? Go back to add more or refresh if you just uploaded via phone.": "Alle deine hochgeladenen Fotos werden unten angezeigt. Fotos fehlen? Gehe zurück, um weitere hinzuzufügen oder aktualisiere, wenn du gerade über das Handy hochgeladen hast."
    }
  }
</i18n>
