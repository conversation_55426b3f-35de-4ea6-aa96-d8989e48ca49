<template>
  <div class="flex min-h-screen flex-col bg-gray-50">
    <Header />
    <section class="py-12 bg-[#F8FCFF] sm:pb-16 lg:pb-20">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
        <div class="lg:flex lg:items-start lg:gap-8">
          <div class="space-y-4 lg:order-2 lg:sticky lg:top-20 lg:max-w-xs">
            <div
              class="p-6 bg-white border rounded-xl border-primary-500/15 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)]"
            >
              <div class="flex items-center gap-4">
                <img class="rounded-full shrink-0 size-16" src="@/assets/img/avatar-danny.jpeg" alt="">
                <div class="flex-1 min-w-0 space-y-1">
                  <p class="tracking-[-0.056px] text-xs font-medium text-paragraph">
                    Written by:
                  </p>
                  <nuxt-link to="/author/danny-postma" class="text-base truncate tracking-[-0.056px] font-bold text-primary-500">
                    Danny Postma
                  </nuxt-link>
                  <p class="text-sm truncate tracking-[-0.056px] text-[#474368] font-normal">
                    Founder of HeadshotPro
                  </p>
                </div>
              </div>
            </div>

            <div
              class="p-6 bg-white border rounded-xl border-primary-500/15 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)]"
            >
              <p class="text-base font-bold text-primary-500 tracking-[-0.4px]">
                Table of contents:
              </p>

              <ul
                class="mt-4 space-y-2 text-base text-[#474368] tracking-[-0.4px] list-disc list-outside pl-4 underline font-medium"
              >
                <li v-for="item in tableOfContents" :key="item">
                  <nuxt-link itemprop="associatedMedia" :to="'#' + toSlug(item)" class="">
                    {{ item }}
                  </nuxt-link>
                </li>
              </ul>
            </div>
          </div>

          <div
            class="lg:order-1 lg:mt-0 p-6 mt-4 sm:p-8  xl:px-24 xl:py-16 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] bg-white border rounded-xl border-primary-500/15 flex-1 min-w-0"
          >
            <p class="text-base font-medium text-paragraph">
              {{ formatDate(article.createdAt) }}
            </p>
            <h1
              class="text-2xl sm:text-4xl xl:text-[42px] leading-tight font-bold tracking-tighter text-primary-500 mt-4"
            >
              {{ article.title }}
            </h1>
            <p class="mt-4 text-base font-medium sm:text-lg text-[#474368]">
              {{ article.excerpt }}
            </p>

            <img class="object-cover w-full mt-4" :src="`https://storage.googleapis.com/headshotpro-public-content/${article.thumbnail.large}`" alt="">
            <div class="prose text-[#474368] mx-auto mt-4 prose-headings:text-primary-500" v-html="compiledMarkdown" />
          </div>
        </div>
      </div>
    </section>
    <MarketingUpgradeYourHeadshots />
    <MarketingFooter />
  </div>
</template>

<script>
import { marked } from 'marked'
export default {
  async asyncData ({ params, $axios, error }) {
    try {
      const { data } = await $axios.$get('/blog/single/' + params.slug)
      return {
        article: data
      }
    } catch (err) {
      if (err.response && err.response.status === 404) {
        return error({ statusCode: 404, message: 'Article not found' })
      } else {
        return error({ statusCode: 500, message: 'Server error' })
      }
    }
  },
  data () {
    return {
      tableOfContents: []
    }
  },
  head () {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'thumbnail',
          name: 'thumbnail',
          content: this.image
        },
        {
          hid: 'twitter:image:src',
          name: 'twitter:image:src',
          content: this.image
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: this.image
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.image
        },
        {
          hid: 'og:image:secure_url',
          property: 'og:image:secure_url',
          content: this.image
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:creator',
          name: 'twitter:creator',
          content: '@dannypostmaa'
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:card',
          name: 'twitter:card',
          content: 'summary_large_image'
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/blog/' + this.$route.params.slug
        }
      ]
    }
  },
  computed: {
    compiledMarkdown () {
      return marked(this.article.content || '', { sanitize: true })
    },
    title () {
      return this.article.title
    },
    description () {
      return this.article.excerpt
    },
    image () {
      return this.article.thumbnail.large
    }
  },
  created () {
    this.tableOfContents = this.getTitlesFromMarkdown(this.article.content)
  },
  methods: {
    getTitlesFromMarkdown (markdown) {
      const regex = /^##\s(.+)/gm
      const titles = []
      let match

      while ((match = regex.exec(markdown)) !== null) {
        if (match.index === regex.lastIndex) {
          regex.lastIndex++
        }
        titles.push(match[1].replace(/\*\*/g, ''))
      }

      return titles
    }

  }

}
</script>

<style>

</style>
