<template>
  <div class="flex flex-col min-h-screen">
    <Header />
    <main class="flex-1 bg-white">
      <section class="py-4 md:py-12">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
          <div class="relative max-w-7xl text-left md:text-left ">
            <div class="flex-col md:flex-row flex gap-4 md:gap-32 items-center content-center justify-between">
              <div class="w-full md:max-w-[500px]">
                <video
                  ref="demoVideo"
                  src="https://storage.googleapis.com/headshotpro-public-content/headshotpro/videos/demo-clothingpicker-v2.mp4"
                  :placeholder="require('@/assets/img/style-picker-demo-wallpaper.jpg')"
                  class="rounded-lg shadow-lg"
                  :autoplay="!isMobile"
                  loop
                  muted
                  playsinline
                />
              </div>
              <div class="flex flex-col">
                <div class="py-4 flex items-center justify-start w-full">
                  <img src="@/assets/img/backdrop-clothing-combo.png" alt="Backdrop and Attire" class="h-full object-cover md:w-[250px] w-full">
                </div>
                <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500 max-w-xl ">
                  Pick your backdrop and outfit from a wide range of options
                </h2>
                <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg">
                  Choose the location of your shoot and pick an outfit from a wide range of options. You'll get 10 headshots per combination, to make sure you get the perfect shot.
                </p>
                <div class="flex items-center justify-start">
                  <nuxt-link to="/app/add">
                    <ButtonPrimary class="mt-4 !bg-[#ff6600]">
                      Start your photo shoot
                    </ButtonPrimary>
                  </nuxt-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="py-12 bg-[#F8FCFF]">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0 space-y-8">
          <div class="flex flex-col  text-left md:text-center items-start md:items-center justify-center space-y-2">
            <h2 class="text-2xl font-medium text-left md:text-center tracking-tight sm:text-4xl lg:text-3xl text-primary-500">
              Choose from a wide range of options
            </h2>
            <p class="text-base text-gray-600">
              Customize exactly how you want your headshots to look like.
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- <div v-for="item in clothing" :key="item._id" class="flex flex-col">
              <img v-if="item?.images?.male" :src="item.images.male" :alt="item.name" class="w-full h-full object-cover">
              <img v-if="item?.images?.female" :src="item.images.female" :alt="item.name" class="w-full h-full object-cover">
              <p>{{ item.title }}</p>
            </div> -->
            <Card class="shadow-box">
              <div class="space-y-4 flex flex-col ">
                <div class="flex flex-col space-y-0">
                  <div class="flex items-center justify-start space-x-1.5">
                    <h3 class="text-lg font-bold">
                      Wardrobe
                    </h3>
                    <span class="text-sm font-medium text-slate-400">({{ clothing.length }}+ options)</span>
                  </div>
                  <!-- <p class="text-sm font-normal text-gray-700">
                    Pick your attire from a wide range of options.
                  </p> -->
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  <LandingpageBackdropsItem
                    v-for="item in clothing"
                    :key="item._id"
                    :image="item.image"
                    :title="item.type"
                  />
                </div>
              </div>
            </Card>
            <Card class="shadow-box">
              <div class="space-y-2 flex flex-col">
                <div class="flex flex-col space-y-0">
                  <div class="flex items-center justify-start space-x-1.5">
                    <h3 class="text-lg font-bold">
                      Backdrops
                    </h3>
                    <span class="text-sm font-medium text-slate-400">({{ styles.length }}+ options)</span>
                  </div>
                  <!-- <p class="text-sm font-normal text-gray-700">
                    Select your backdrop from a wide range of options.
                  </p> -->
                </div>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  <LandingpageBackdropsItem
                    v-for="item in styles"
                    :key="item._id"
                    :image="item.image"
                    :title="item.title"
                  />
                </div>
              </div>
            </Card>
          </div>
        <!-- <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4">
            <div v-for="item in styles" :key="item._id" class="flex flex-col">
              <img v-if="item?.image?.maleImage" :src="item.image.maleImage" :alt="item.name" class="w-full h-full object-cover">
              <img v-if="item?.image?.femaleImage" :src="item.image.femaleImage" :alt="item.name" class="w-full h-full object-cover">
            </div>
          </div> -->
        </div>
      </section>
      <MarketingCTAV2 />
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  data () {
    return {
      isMobile: false
    }
  },
  head () {
    return {
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/backdrop-and-outfit'
        }
      ]
    }
  },
  computed: {
    clothing () {
      const items = []
      this.$store.state.clothing.forEach((item) => {
        if (item.active) {
          if (item.images.male) {
            items.push({
              ...item,
              image: item.images.male
            })
          }
          if (item.images.female) {
            items.push({
              ...item,
              image: item.images.female
            })
          }
        }
      })
      return items
    },
    styles () {
      const items = []
      this.$store.state.styles.forEach((item) => {
        if (item.organizationIds?.length > 0) {
          return true
        }

        if (item.status === 'active') {
          if (item.image.maleImage) {
            items.push({
              ...item,
              image: item.image.maleImage
            })
          }
          if (item.image.femaleImage) {
            items.push({
              ...item,
              image: item.image.femaleImage
            })
          }
        }
      })
      return items
    }
  },
  mounted () {
    // Check if device is mobile
    this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

}
</script>

<style>

</style>
