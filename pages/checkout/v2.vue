<template>
  <!-- ---------------------------------------------------------------------------------------------------- -->
  <!-- @NOTE: Page used for test #20 to add package to the checkout. Failed with -10% revenue! https://us.posthog.com/project/62828/experiments/82885 -->
  <!-- ---------------------------------------------------------------------------------------------------- -->
  <PrecheckoutWrapper>
    <section class="relative flex-1 pb-40 sm:pb-48 sm:pt-12 md:pb-12 md:pt-2">
      <div class="mx-auto max-w-screen-xl sm:px-6 lg:px-8">
        <!-- <div class="flex items-center justify-start gap-x-3 md:justify-center pb-6">
          <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="">
          <img class="h-4 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="">
        </div> -->
        <div class="block md:hidden mx-auto text-left md:max-w-xl md:text-center mb-4">
          <h1 class="inline-flex text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('Checkout') }}
          </h1>
          <p class="mt-2 text-sm font-medium text-paragraph  lg:max-w-md lg:mx-auto">
            <!-- {{ $t('goodCompany', { users: $store.state.stats.users }) }} -->
            Select the package that best suits your needs and pay with your preferred payment method.
          </p>
        </div>
        <div class="mb-6 flex w-full justify-start md:justify-center md:mb-0">
          <TrustpilotRating />
        </div>
        <div class="mx-auto grid max-w-5xl grid-cols-1 gap-6 sm:mt-8 md:grid-cols-2 md:gap-12 lg:mt-10 xl:gap-24">
          <div class="hidden md:block space-y-8 md:order-2">
            <div class="space-y-5 rounded-lg border-gray-100 bg-white p-6 shadow-sm md:border lg:p-8">
              <div class="flex items-center justify-between gap-4">
                <p class="text-lg font-bold tracking-tight text-primary-500">
                  {{ $t('Order Summary') }}
                </p>

                <!-- <nuxt-link :to="localePath(`/app/add?step=2`)" class="text-sm font-medium tracking-tight text-gray-500 underline hover:text-gray-900">
                  {{ $t('Change package') }}
                </nuxt-link> -->
              </div>

              <LoadingWrapper :is-loading="isLoading">
                <div class="space-y-5">
                  <div v-if="priceDetails && product" class="flex items-start justify-between gap-4">
                    <div class="flex-1">
                      <p class="text-base font-medium tracking-tight text-primary-500">
                        {{ $t('LineItem', { quantity, package: priceDetails.title }) }}
                      </p>
                      <ul class="mt-4 space-y-1.5 text-sm font-medium text-paragraph">
                        <li class="flex items-center gap-1">
                          <svg aria-hidden="true" class="size-5 shrink-0 text-success-600" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M16.704 4.16719C16.7825 4.22682 16.8486 4.30135 16.8983 4.38652C16.948 4.47169 16.9804 4.56582 16.9937 4.66354C17.007 4.76125 17.0008 4.86062 16.9757 4.95597C16.9505 5.05132 16.9068 5.14076 16.847 5.21919L8.847 15.7192C8.78211 15.8042 8.69977 15.8744 8.60552 15.925C8.51126 15.9756 8.40727 16.0055 8.30053 16.0126C8.19379 16.0197 8.08676 16.0039 7.98663 15.9662C7.8865 15.9286 7.79559 15.8699 7.72 15.7942L3.22 11.2942C3.08752 11.152 3.0154 10.964 3.01882 10.7697C3.02225 10.5754 3.10096 10.39 3.23838 10.2526C3.37579 10.1152 3.56118 10.0364 3.75548 10.033C3.94978 10.0296 4.13782 10.1017 4.28 10.2342L8.174 14.1272L15.654 4.31019C15.7744 4.15215 15.9526 4.04832 16.1494 4.02151C16.3463 3.9947 16.5457 4.0471 16.704 4.16719Z"
                            />
                          </svg>
                          {{ (priceDetails.styles + priceDetails.additional) * photoPerStyle }} {{ $t('headshots') }}
                        </li>

                        <li class="flex items-center gap-1">
                          <svg aria-hidden="true" class="size-5 shrink-0 text-success-600" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M16.704 4.16719C16.7825 4.22682 16.8486 4.30135 16.8983 4.38652C16.948 4.47169 16.9804 4.56582 16.9937 4.66354C17.007 4.76125 17.0008 4.86062 16.9757 4.95597C16.9505 5.05132 16.9068 5.14076 16.847 5.21919L8.847 15.7192C8.78211 15.8042 8.69977 15.8744 8.60552 15.925C8.51126 15.9756 8.40727 16.0055 8.30053 16.0126C8.19379 16.0197 8.08676 16.0039 7.98663 15.9662C7.8865 15.9286 7.79559 15.8699 7.72 15.7942L3.22 11.2942C3.08752 11.152 3.0154 10.964 3.01882 10.7697C3.02225 10.5754 3.10096 10.39 3.23838 10.2526C3.37579 10.1152 3.56118 10.0364 3.75548 10.033C3.94978 10.0296 4.13782 10.1017 4.28 10.2342L8.174 14.1272L15.654 4.31019C15.7744 4.15215 15.9526 4.04832 16.1494 4.02151C16.3463 3.9947 16.5457 4.0471 16.704 4.16719Z"
                            />
                          </svg>
                          <!-- {{ priceDetails.styles + priceDetails.additional }} {{ $t('unique backgrounds') }} -->
                          {{ $t('Choice of') }} {{ priceDetails.styles + priceDetails.additional }} {{ $t('backdrops and outfits') }} <Tooltip info="You get to pick how your headshots will look like. Select from a wide range of over 100+ backdrops and outfits. <a href='/backdrop-and-outfit' class='underline text-sky-500'>Click here</a> to view all options." />
                        </li>

                        <li class="flex items-center gap-1">
                          <svg aria-hidden="true" class="size-5 shrink-0 text-success-600" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M16.704 4.16719C16.7825 4.22682 16.8486 4.30135 16.8983 4.38652C16.948 4.47169 16.9804 4.56582 16.9937 4.66354C17.007 4.76125 17.0008 4.86062 16.9757 4.95597C16.9505 5.05132 16.9068 5.14076 16.847 5.21919L8.847 15.7192C8.78211 15.8042 8.69977 15.8744 8.60552 15.925C8.51126 15.9756 8.40727 16.0055 8.30053 16.0126C8.19379 16.0197 8.08676 16.0039 7.98663 15.9662C7.8865 15.9286 7.79559 15.8699 7.72 15.7942L3.22 11.2942C3.08752 11.152 3.0154 10.964 3.01882 10.7697C3.02225 10.5754 3.10096 10.39 3.23838 10.2526C3.37579 10.1152 3.56118 10.0364 3.75548 10.033C3.94978 10.0296 4.13782 10.1017 4.28 10.2342L8.174 14.1272L15.654 4.31019C15.7744 4.15215 15.9526 4.04832 16.1494 4.02151C16.3463 3.9947 16.5457 4.0471 16.704 4.16719Z"
                            />
                          </svg>
                          <!-- {{ (priceDetails?.turnAroundTime ?? '2 hours').replace('hour', $t('hour')) }} {{ $t('turnaround time') }} -->
                          {{ $t('Ready in') }} {{ priceDetails?.turnAroundTime ?? '2 hours' }}
                        </li>
                      </ul>
                    </div>

                    <p class="text-base font-medium tracking-tight text-primary-500">
                      {{ formatPrice((price * quantity) / 100, currency, 2, false) }}
                    </p>
                  </div>

                  <hr class="hidden border-gray-200 md:block">

                  <template v-if="discountPercentage || discountAmount">
                    <div class="flex items-start justify-between gap-4">
                      <div class="flex-1">
                        <p class="texst-base font-medium tracking-tight text-primary-500">
                          <template v-if="discountPercentage">
                            {{ discountPercentage }}% {{ $t('discount') }}
                          </template>
                          <template v-else>
                            {{ formatPrice(discountAmount / 100, currency, 2, false) }} {{ $t('discount') }}
                          </template>
                        </p>
                      </div>

                      <p class="text-base font-medium tracking-tight text-primary-500">
                        <template v-if="discountPercentage">
                          -{{ formatPrice((price * quantity * (discountPercentage / 100)) / 100, currency, 2, false) }}
                        </template>
                        <template v-else>
                          -{{ formatPrice(discountAmount / 100, currency, 2, false) }}
                        </template>
                      </p>
                    </div>
                  </template>
                  <div class="hidden items-center justify-between gap-4 md:flex">
                    <p class="text-xl font-medium tracking-tight text-primary-500">
                      Total
                    </p>
                    <p class="text-xl font-medium tracking-tight text-teal-500">
                      <template v-if="discountPercentage">
                        {{ formatPrice((price * quantity * (1 - discountPercentage / 100)) / 100, selectedCurrency, 2, false) }}
                      </template>
                      <template v-else>
                        {{ formatPrice(price * quantity - discountAmount, selectedCurrency, 2, false) }}
                      </template>
                    </p>
                  </div>

                  <LoadingWrapper :is-loading="isPaying" class="hidden md:flex">
                    <button type="button" class="hidden space-x-1.5 w-full items-center justify-center rounded-lg bg-[#ff6600] border-2 border-black/5 px-4 py-2.5 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 md:flex" :disabled="!canPay" @click="pay()">
                      <!-- {{ $t('Pay now') }} -->
                      <span>{{ $t('Purchase your headshots') }}</span>
                      <IconChevron class="size-4 text-white" />
                    </button>
                  </LoadingWrapper>
                  <CheckoutUpsell :package="priceDetails?.id" :upselled="upselled" @change="changePackage($event)" @downgrade="downgrade($event)" />
                </div>
              </LoadingWrapper>

              <div class="hidden md:block">
                <div class="flex items-center justify-center gap-1.5">
                  <svg aria-hidden="true" class="size-5 shrink-0 text-teal-500" viewBox="0 0 18 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M15.8836 7.24277C15.6164 6.96855 15.3422 6.68027 15.2367 6.43418C15.1312 6.18809 15.1383 5.82246 15.1312 5.4498C15.1242 4.76777 15.1102 3.9873 14.5687 3.4459C14.0273 2.90449 13.2469 2.89043 12.5648 2.8834C12.1922 2.87637 11.8125 2.86934 11.5805 2.77793C11.3484 2.68652 11.0461 2.39824 10.7719 2.13105C10.2867 1.66699 9.73125 1.13965 9 1.13965C8.26875 1.13965 7.71328 1.66699 7.22813 2.13105C6.95391 2.39824 6.66563 2.67246 6.41953 2.77793C6.17344 2.8834 5.80781 2.87637 5.43516 2.8834C4.75313 2.89043 3.97266 2.90449 3.43125 3.4459C2.88984 3.9873 2.87578 4.76777 2.86875 5.4498C2.86172 5.82246 2.85469 6.20215 2.76328 6.43418C2.67188 6.66621 2.38359 6.96855 2.11641 7.24277C1.65234 7.72793 1.125 8.2834 1.125 9.01465C1.125 9.7459 1.65234 10.3014 2.11641 10.7865C2.38359 11.0607 2.65781 11.349 2.76328 11.5951C2.86875 11.8412 2.86172 12.2068 2.86875 12.5795C2.87578 13.2615 2.88984 14.042 3.43125 14.5834C3.97266 15.1248 4.75313 15.1389 5.43516 15.1459C5.80781 15.1529 6.1875 15.16 6.41953 15.2514C6.65156 15.3428 6.95391 15.6311 7.22813 15.8982C7.71328 16.3623 8.26875 16.8896 9 16.8896C9.73125 16.8896 10.2867 16.3623 10.7719 15.8982C11.0461 15.6311 11.3344 15.3568 11.5805 15.2514C11.8266 15.1459 12.1922 15.1529 12.5648 15.1459C13.2469 15.1389 14.0273 15.1248 14.5687 14.5834C15.1102 14.042 15.1242 13.2615 15.1312 12.5795C15.1383 12.2068 15.1453 11.8271 15.2367 11.5951C15.3281 11.3631 15.6164 11.0607 15.8836 10.7865C16.3477 10.3014 16.875 9.7459 16.875 9.01465C16.875 8.2834 16.3477 7.72793 15.8836 7.24277ZM12.4805 7.73496L8.36016 11.6725C8.25363 11.7727 8.11265 11.8281 7.96641 11.8271C7.82232 11.8277 7.68365 11.7722 7.57969 11.6725L5.51953 9.70371C5.46239 9.65386 5.41592 9.59297 5.38291 9.52469C5.3499 9.45642 5.33104 9.38218 5.32746 9.30644C5.32387 9.23069 5.33564 9.155 5.36206 9.08392C5.38847 9.01283 5.42899 8.94783 5.48116 8.8928C5.53334 8.83778 5.59611 8.79387 5.66569 8.76372C5.73527 8.73357 5.81023 8.7178 5.88606 8.71736C5.96189 8.71692 6.03703 8.73182 6.10696 8.76116C6.17688 8.7905 6.24015 8.83367 6.29297 8.88809L7.96641 10.4842L11.707 6.91934C11.8164 6.82391 11.9584 6.77446 12.1034 6.78132C12.2484 6.78818 12.3851 6.85082 12.485 6.95614C12.5849 7.06147 12.6402 7.20132 12.6393 7.34646C12.6385 7.49161 12.5816 7.63081 12.4805 7.73496Z"
                    />
                  </svg>
                  <span class="text-sm font-extrabold uppercase tracking-wide text-teal-500">
                    {{ $t('14-day money back guarantee') }} <Tooltip info="Our Profile-Worthy guarantee promises that you'll walk away with at least (1) usable, profile-worthy headshot you can use professionally. If you don't get a profile-worthy photo back from HeadshotPro, you can claim a complete 100% refund." />
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="md:order-1 xl:pr-12 flex flex-col gap-8">
            <CheckoutPackage
              :selected-package-id="selectedPackageId"
              @select="changePackage($event, false)"
            />

            <div class="flex flex-col gap-4">
              <p class="text-lg font-bold tracking-tight text-primary-500">
                {{ $t('Select your preferred payment method') }}
              </p>

              <div>
                <CheckoutPaymentMethodSelection
                  :payment-providers="paymentProviders"
                  :selected-payment-provider="selectedPaymentProvider"
                  @select="selectedPaymentProvider = $event"
                />
              </div>
            </div>

            <div class="block">
              <CheckoutCoupon :discount-amount="discountAmount" :discount-percentage="discountPercentage" @couponApplied="applyDiscount($event)" />
            </div>

            <CheckoutUpsell class="block md:hidden" :package="priceDetails?.id" :upselled="upselled" @change="changePackage($event)" @downgrade="downgrade($event)" />

            <div class="block">
              <!-- <MarketingTrustpilot size="h-5 w-5" /> -->

              <ul class="mt-5 space-y-5 text-sm font-medium tracking-tight text-gray-500">
                <CheckoutBenefitItem icon="IconLock" :text="$t('Secure checkout - SSL encrypted')" />
                <CheckoutBenefitItem icon="IconHeartAlt" :text="$t('trustedByMoreThan', { n: happyCustomers })" />
                <CheckoutBenefitItem icon="IconClock" :text="$t('Done in') + ' ' + (priceDetails?.turnAroundTime ?? '2 hours').replace('hour', $t('hour')) + ' ' + $t('or less')" />
                <CheckoutBenefitItem icon="IconPayment" :text="$t('8x cheaper than a photographer')" />
              </ul>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="max-w-5xl mx-auto mt-10">
        <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <template v-for="item of trustPilotReviews.slice(0, 8)">
            <LandingpageV2ReviewTrustpilot :key="item.id" :item="item" :cap-length="true" />
          </template>
        </div>
      </div> -->
      <div class="fixed inset-x-0 bottom-0 border-t border-gray-100 bg-white py-3 shadow-sm md:hidden">
        <div class="mx-auto max-w-screen-xl px-5 sm:px-6 lg:px-8">
          <LoadingWrapper :is-loading="isLoading" title="Calculation your package">
            <div class="flex items-center justify-between gap-2 sm:gap-4">
              <div>
                <p class="text-base font-medium tracking-tight text-primary-500">
                  Total
                </p>
                <div class="flex items-center gap-1.5">
                  <p class="text-xl font-medium tracking-tight text-teal-500">
                    {{ formatPrice((price * quantity * (1 - discountPercentage / 100)) / 100, selectedCurrency, 2, false) }}
                  </p>
                </div>
              </div>
              <LoadingWrapper :is-loading="isPaying" title="Redirecting to payment">
                <button type="button" class="w-40 items-center justify-center rounded-lg bg-[#ff6600] px-4 py-2.5 text-base font-bold text-white shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20" :disabled="!canPay" @click="pay()">
                  {{ $t('Pay now') }}
                </button>
              </LoadingWrapper>
            </div>
          </LoadingWrapper>

          <div class="mt-3 flex items-center justify-center gap-1 sm:mt-5">
            <svg aria-hidden="true" class="size-4 shrink-0 text-gray-500" viewBox="0 0 18 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M15.8836 7.24277C15.6164 6.96855 15.3422 6.68027 15.2367 6.43418C15.1312 6.18809 15.1383 5.82246 15.1312 5.4498C15.1242 4.76777 15.1102 3.9873 14.5687 3.4459C14.0273 2.90449 13.2469 2.89043 12.5648 2.8834C12.1922 2.87637 11.8125 2.86934 11.5805 2.77793C11.3484 2.68652 11.0461 2.39824 10.7719 2.13105C10.2867 1.66699 9.73125 1.13965 9 1.13965C8.26875 1.13965 7.71328 1.66699 7.22813 2.13105C6.95391 2.39824 6.66563 2.67246 6.41953 2.77793C6.17344 2.8834 5.80781 2.87637 5.43516 2.8834C4.75313 2.89043 3.97266 2.90449 3.43125 3.4459C2.88984 3.9873 2.87578 4.76777 2.86875 5.4498C2.86172 5.82246 2.85469 6.20215 2.76328 6.43418C2.67188 6.66621 2.38359 6.96855 2.11641 7.24277C1.65234 7.72793 1.125 8.2834 1.125 9.01465C1.125 9.7459 1.65234 10.3014 2.11641 10.7865C2.38359 11.0607 2.65781 11.349 2.76328 11.5951C2.86875 11.8412 2.86172 12.2068 2.86875 12.5795C2.87578 13.2615 2.88984 14.042 3.43125 14.5834C3.97266 15.1248 4.75313 15.1389 5.43516 15.1459C5.80781 15.1529 6.1875 15.16 6.41953 15.2514C6.65156 15.3428 6.95391 15.6311 7.22813 15.8982C7.71328 16.3623 8.26875 16.8896 9 16.8896C9.73125 16.8896 10.2867 16.3623 10.7719 15.8982C11.0461 15.6311 11.3344 15.3568 11.5805 15.2514C11.8266 15.1459 12.1922 15.1529 12.5648 15.1459C13.2469 15.1389 14.0273 15.1248 14.5687 14.5834C15.1102 14.042 15.1242 13.2615 15.1312 12.5795C15.1383 12.2068 15.1453 11.8271 15.2367 11.5951C15.3281 11.3631 15.6164 11.0607 15.8836 10.7865C16.3477 10.3014 16.875 9.7459 16.875 9.01465C16.875 8.2834 16.3477 7.72793 15.8836 7.24277ZM12.4805 7.73496L8.36016 11.6725C8.25363 11.7727 8.11265 11.8281 7.96641 11.8271C7.82232 11.8277 7.68365 11.7722 7.57969 11.6725L5.51953 9.70371C5.46239 9.65386 5.41592 9.59297 5.38291 9.52469C5.3499 9.45642 5.33104 9.38218 5.32746 9.30644C5.32387 9.23069 5.33564 9.155 5.36206 9.08392C5.38847 9.01283 5.42899 8.94783 5.48116 8.8928C5.53334 8.83778 5.59611 8.79387 5.66569 8.76372C5.73527 8.73357 5.81023 8.7178 5.88606 8.71736C5.96189 8.71692 6.03703 8.73182 6.10696 8.76116C6.17688 8.7905 6.24015 8.83367 6.29297 8.88809L7.96641 10.4842L11.707 6.91934C11.8164 6.82391 11.9584 6.77446 12.1034 6.78132C12.2484 6.78818 12.3851 6.85082 12.485 6.95614C12.5849 7.06147 12.6402 7.20132 12.6393 7.34646C12.6385 7.49161 12.5816 7.63081 12.4805 7.73496Z"
              />
            </svg>
            <span class="tracking-thight text-xs font-extrabold uppercase text-gray-500">
              <!-- 14-day money back guarantee -->
              {{ $t('Trusted by 100K+ happy customers') }}
            </span>
          </div>
        </div>
      </div>
    </section>
  </PrecheckoutWrapper>
</template>

<script>
import CheckoutMixin from '@/mixins/CheckoutMixin'
export default {
  mixins: [CheckoutMixin],
  layout: 'protected',
  data () {
    return {
      price: null,
      product: null,
      quantity: 1,
      priceDetails: {},
      selectedPackageId: null,
      upselled: false
    }
  },
  head () {
    return {
      title: 'Checkout | HeadshotPro'
    }
  },
  computed: {
    priceId () {
      return this.selectedPackageId || this.$route.query.priceId
    },
    packages () {
      return this.$store.state.packages
    },
    packageIdsForKeys () {
      // return 'price_':'small'
      const object = {}
      const keys = Object.keys(this.packages)
      keys.forEach((key) => {
        const value = this.packages[key]
        object[value.id] = key
      })
      return object
    }
  },
  mounted () {
    this.selectedPackageId = this.$route?.query?.priceId || this.packages?.medium?.id || Object.keys(this.packages)[0]
    this.priceDetails = this.packages[this.packageIdsForKeys[this.selectedPackageId]]

    this.$posthog.capture('$funnel:checkout')
    this.$facebook.trackEvent('AddToCart')
    this.getPrice(this.selectedPackageId)
    const packages = this.packages

    Object.keys(packages).forEach((key) => {
      const p = packages[key]
      if (p.id === this.$route.query.priceId) {
        this.priceDetails = p
        // For test #20
        this.selectedPackageId = p.id
      }
    })

    if (this.$route.query.payment === 'failed') {
      this.handleError(this.$t('Payment failed, please try again or contact support.'))
    }
  },
  methods: {
    async changePackage (id, upsell = true) {
      this.selectedPackageId = id
      await this.$router.push({ query: { priceId: id } })
      await this.getPrice(id)
      const packages = this.packages
      Object.keys(packages).forEach((key) => {
        const p = packages[key]
        if (p.id === this.$route.query.priceId) {
          this.priceDetails = p
        }
      })

      if (upsell) {
        if (!this.priceDetails.upsellDiscount[this.env || 'production']) {
          return
        }

        this.upselled = true

        this.applyDiscount({
          amount: this.priceDetails?.upsellDiscount.percentage,
          code: this.priceDetails?.upsellDiscount[this?.env || 'production'],
          type: 'percentage'
        })
        this.$posthog.capture('$checkout:upsell_package')
      }
    },
    async downgrade (id) {
      await this.$router.push({ query: { priceId: id } })
      await this.getPrice(id)
      const packages = this.packages
      Object.keys(packages).forEach((key) => {
        const p = packages[key]
        if (p.id === this.$route.query.priceId) {
          this.priceDetails = p
        }
      })

      this.upselled = false
      this.discountPercentage = null
      this.couponCode = null
    },
    increaseOpacity () {
      document.getElementById('review').style.opacity = 1
    },
    quantityAmount () {
      return Array.from(Array(100).keys()).map(x => x + 1)
    },
    async getPrice (id = null) {
      try {
        this.isLoading = true

        console.log('id', (id ?? this.priceId))
        const { success, data, errorMessage } = await this.$axios.$get('/checkout/stripe/price/retrieve/' + (id ?? this.priceId))
        if (!success) {
          throw new Error(errorMessage)
        }
        // if (data?.currency_options && data?.currency_options[this.userCurrency?.toLowerCase()]) {
        //   if (this.checkIfUserIsInTestId('9-eur-vs-usd') && this?.userCurrency?.toLowerCase() === 'eur') {
        //     this.price = data?.currency_options[this.userCurrency?.toLowerCase()].unit_amount
        //     this.currency = this.userCurrency?.toLowerCase()
        //   } else {
        //     this.price = data.unit_amount
        //     this.currency = data.currency
        //   }
        // } else {
        this.price = data.unit_amount
        this.currency = 'usd'

        const product = await this.$axios.$get('/checkout/stripe/product/retrieve/' + data.product)
        if (product) {
          const { description, images, name } = product
          this.product = { description, name, image: images[0] }
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style>
@media screen and (max-width: 767px) {
  .chat-button {
    bottom: 10rem;
  }
}
</style>

<i18n>
  {
    "en": {
      "Checkout": "Checkout",
      "goodCompany": "You're in good company. We've helped over {users}+ happy customers create their professional headshots.",
      "Order Summary": "Order Summary",
      "Change package": "Change package",
      "LineItem": "{quantity}x {package} Package",
      "headshots": "headshots",
      "unique backgrounds": "unique backgrounds",
      "hour": "hour",
      "turnaround time": "turnaround time",
      "discount": "discount",
      "Pay now": "Pay now",
      "Purchase your headshots": "Purchase your headshots",
      "14-day money back guarantee": "14-day money back guarantee",
      "I was looking for professional work photos for my LinkedIn so this is perfect": "I was looking for professional work photos for my LinkedIn so this is perfect",
      "Select your preferred payment method": "Select your preferred payment method",
      "Pay with": "Pay with",
      "Secure checkout - SSL encrypted": "Secure checkout - SSL encrypted",
      "trustedByMoreThan": "Trusted by more than {n} customers",
      "Done in": "Done in",
      "or less": "or less",
      "8x cheaper than a photographer": "8x cheaper than a photographer",
      "Trusted by 100K+ happy customers": "Trusted by 100K+ happy customers",
      "Payment failed, please try again or contact support.": "Payment failed, please try again or contact support.",
      "Something went wrong while setting up your payment. Please contact support.": "Something went wrong while setting up your payment. Please contact support.",
      "credit card": "credit card",
      "Choice of": "Choice of",
      "backdrops and outfits": "backdrops and outfits",
      "Ready in": "Ready in"
    },
    "es": {
      "Checkout": "Carrito",
      "goodCompany": "Estás en buena compañía. Hemos ayudado a más de {users}+ clientes satisfechos a crear sus fotos profesionales.",
      "Order Summary": "Resumen del pedido",
      "Change package": "Cambiar paquete",
      "LineItem": "{quantity}x Paquete {package}",
      "headshots": "fotos profesionales",
      "unique backgrounds": "fondos únicos",
      "hour": "hora",
      "turnaround time": "tiempo de entrega",
      "discount": "descuento",
      "Pay now": "Pagar ahora",
      "Purchase your headshots": "Compra tus fotos profesionales",
      "14-day money back guarantee": "Garantía de devolución de 14 días",
      "I was looking for professional work photos for my LinkedIn so this is perfect": "Estaba buscando fotos profesionales para mi LinkedIn, así que esto es perfecto",
      "Select your preferred payment method": "Selecciona tu método de pago preferido",
      "Pay with": "Pagar con",
      "Secure checkout - SSL encrypted": "Pago seguro - Encriptación SSL",
      "trustedByMoreThan": "Más de {n} clientes confían en nosotros",
      "Done in": "Hecho en",
      "or less": "o menos",
      "8x cheaper than a photographer": "8x más barato que un fotógrafo",
      "Trusted by 100K+ happy customers": "Más de 100K clientes satisfechos",
      "Payment failed, please try again or contact support.": "El pago falló, por favor inténtalo de nuevo o contacta con soporte.",
      "Something went wrong while setting up your payment. Please contact support.": "Algo salió mal al configurar tu pago. Por favor, contacta con soporte.",
      "credit card": "tarjeta",
      "Choice of": "Elección de",
      "backdrops and outfits": "fondos y vestuarios",
      "Ready in": "Listo en"
    }
  }
</i18n>
