<template>
  <section class=" text-black flex items-start md:items-center justify-center min-h-screen bg-gray-50 px-4 py-8 md:pt-0">
    <div class=" flex items-center justify-center flex-col">
      <nuxt-link to="/">
        <Logo class="w-[180px] text-white" />
      </nuxt-link>

      <div v-if="isLoading" class=" rounded-lg py-4 bg-white backdrop-blur-lg px-8 mt-4">
        <LoadingSpinner class="mx-auto" title="Setting everything up..." />
      </div>
      <template v-else>
        <Card class="mt-4 rounded-lg shadow w-full max-w-lg min-w-lg ">
          <div class="p-2">
            <h1 class="font-medium text-xl">
              Redeem your coupon
            </h1>
            <p class="text-sm text-gray-700 mb-4">
              Enter your coupon code below to redeem your photo shoot
            </p>
            <LoadingWrapper :is-loading="isRedeeming" title="Redeeming code...">
              <Input v-model="code" placeholder="Enter your gift card code" />
              <ButtonPrimary :disabled="isRedeeming || !code" class="mt-2 w-full" @click="redeemCode">
                Redeem code
              </ButtonPrimary>
            </LoadingWrapper>
          </div>
        </Card>
      </template>
    </div>
  </section>
</template>

<script>
export default {
  data () {
    return {
      isLoading: true,
      isRedeeming: false,
      unsubscribeAuthChange: () => {},
      code: null
    }
  },
  watch: {
    code (newValue) {
      this.code = newValue.replace(/\s/g, '')
    }
  },
  created () {
    // if (!this.isLoggedIn) {
    this.isLoading = true
    if (!this.isLoggedIn) {
      this.$router.push('/auth/login?redirect=' + this.$route.fullPath)
    }
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        await this.setupUserDetails(user)
        this.isLoading = false
      } else {
        this.isLoading = false
      }
    })
    // } else if (this.userTokens > 0) { this.activeStep = 3 } else { this.activeStep = 1 }
  },
  mounted () {
    if (this.isLoggedIn) {
      if (this.$store.state.user.packages.length > 0) {
        this.$router.push('/app/add')
      }
    };

    if (this.$route.query.code) {
      this.code = this.$route.query.code
    }
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()
  },
  methods: {
    redeemCode () {
      let { code } = this
      if (code) {
        code = code.replace(/\s/g, '')
      }

      if (!code || code.length < 5) {
        this.$toast.error('Please enter a coupon code')
        return
      }

      this.isRedeeming = true

      this.$axios.$post('/coupon/redeem', { code })
        .then(async (data) => {
          if (data?.success) {
            this.$toast.success('Coupon redeemed successfully')
            await this.getUserData()
            this.$router.push('/app/add')
          } else {
            this.$toast.error(data?.message)
          }
          this.isRedeeming = false
        })
        .catch((err) => {
          this.$toast.error(err?.response?.data?.message || 'Something went wrong')
          this.isRedeeming = false
        })
    }
  }
}
</script>
