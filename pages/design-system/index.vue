<template>
  <div class="max-w-7xl mx-auto p-6 space-y-12">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-6">
      <h1 class="text-3xl font-bold text-gray-900">
        Design System
      </h1>
      <p class="mt-2 text-lg text-gray-600">
        A comprehensive guide to HeadshotPro's UI components, patterns, and design tokens.
      </p>
    </div>

    <!-- Colors Section -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Colors
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="space-y-3">
          <h3 class="text-lg font-medium text-gray-900">
            Primary
          </h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-primary-500 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Primary 500
                </div>
                <div class="text-sm text-gray-500">
                  Main brand color
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-brand-500 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Brand 500
                </div>
                <div class="text-sm text-gray-500">
                  Brand accent
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <h3 class="text-lg font-medium text-gray-900">
            Grays
          </h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gray-100 rounded-lg shadow-sm border" />
              <div>
                <div class="font-medium">
                  Gray 100
                </div>
                <div class="text-sm text-gray-500">
                  Light background
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gray-500 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Gray 500
                </div>
                <div class="text-sm text-gray-500">
                  Medium gray
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gray-900 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Gray 900
                </div>
                <div class="text-sm text-gray-500">
                  Dark text
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <h3 class="text-lg font-medium text-gray-900">
            Status
          </h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-green-500 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Green 500
                </div>
                <div class="text-sm text-gray-500">
                  Success
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-red-500 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Red 500
                </div>
                <div class="text-sm text-gray-500">
                  Error
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-yellow-500 rounded-lg shadow-sm" />
              <div>
                <div class="font-medium">
                  Yellow 500
                </div>
                <div class="text-sm text-gray-500">
                  Warning
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Typography Section -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Typography
      </h2>
      <div class="space-y-6">
        <div>
          <h1 class="text-4xl font-bold text-gray-900">
            Heading 1 - 4xl Bold
          </h1>
          <p class="text-sm text-gray-500 mt-1">
            text-4xl font-bold
          </p>
        </div>
        <div>
          <h2 class="text-3xl font-semibold text-gray-900">
            Heading 2 - 3xl Semibold
          </h2>
          <p class="text-sm text-gray-500 mt-1">
            text-3xl font-semibold
          </p>
        </div>
        <div>
          <h3 class="text-2xl font-semibold text-gray-900">
            Heading 3 - 2xl Semibold
          </h3>
          <p class="text-sm text-gray-500 mt-1">
            text-2xl font-semibold
          </p>
        </div>
        <div>
          <h4 class="text-xl font-medium text-gray-900">
            Heading 4 - xl Medium
          </h4>
          <p class="text-sm text-gray-500 mt-1">
            text-xl font-medium
          </p>
        </div>
        <div>
          <p class="text-base text-gray-900">
            Body text - Base Regular
          </p>
          <p class="text-sm text-gray-500 mt-1">
            text-base
          </p>
        </div>
        <div>
          <p class="text-sm text-gray-600">
            Small text - SM Regular
          </p>
          <p class="text-xs text-gray-500 mt-1">
            text-sm
          </p>
        </div>
        <div>
          <p class="text-xs text-gray-500">
            Caption text - XS Regular
          </p>
          <p class="text-xs text-gray-400 mt-1">
            text-xs
          </p>
        </div>
      </div>
    </section>

    <!-- Input Components Section -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Input Components
      </h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Text Input -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Text Input
          </h3>
          <div class="space-y-4">
            <Input
              v-model="sampleData.textInput"
              label="Default Input"
              placeholder="Enter some text..."
              description="This is a standard text input field"
            />
            <Input
              v-model="sampleData.textInputError"
              label="Error State"
              placeholder="This has an error..."
              :error="true"
            />
            <Input
              v-model="sampleData.textInputDisabled"
              label="Disabled Input"
              placeholder="This is disabled"
              :disabled="true"
            />
          </div>
        </div>

        <!-- TextArea -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            TextArea
          </h3>
          <div class="space-y-4">
            <TextArea
              v-model="sampleData.textArea"
              label="Message"
              placeholder="Enter your message..."
              :rows="4"
              description="Multi-line text input"
            />
            <TextArea
              v-model="sampleData.textAreaError"
              label="Error State"
              placeholder="This has an error..."
              :rows="3"
              :error="true"
            />
          </div>
        </div>

        <!-- Select -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Select
          </h3>
          <div class="space-y-4">
            <Select
              v-model="sampleData.select"
              label="Choose Option"
              :options="selectOptions"
              description="Dropdown selection"
            />
            <Select
              v-model="sampleData.selectDisabled"
              label="Disabled Select"
              :options="selectOptions"
              :disabled="true"
            />
          </div>
        </div>

        <!-- Checkbox -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Checkbox
          </h3>
          <div class="space-y-4">
            <Checkbox
              v-model="sampleData.checkbox1"
              label="I agree to the terms and conditions"
            />
            <Checkbox
              v-model="sampleData.checkbox2"
              label="Send me marketing emails"
            />
            <Checkbox
              v-model="sampleData.checkboxDark"
              label="Dark mode checkbox"
              :dark-mode="true"
            />
          </div>
        </div>

        <!-- Search -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Search
          </h3>
          <div class="space-y-4">
            <Search
              v-model="sampleData.search"
              label="Search"
              placeholder="Search for something..."
            />
          </div>
        </div>

        <!-- Range -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Range
          </h3>
          <div class="space-y-4">
            <Range
              v-model="sampleData.range"
              label="Volume"
              :min="0"
              :max="100"
              :show-value="true"
            />
          </div>
        </div>

        <!-- Toggle -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Toggle
          </h3>
          <div class="space-y-4">
            <Toggle
              :active="sampleData.toggle"
              @change="sampleData.toggle = !sampleData.toggle"
            >
              <span class="text-sm font-medium text-gray-900">Enable notifications</span>
            </Toggle>
          </div>
        </div>

        <!-- InputTag -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Input Tags
          </h3>
          <div class="space-y-4">
            <InputTag
              :tags="sampleData.tags"
              label="Tags"
              description="Press enter to add tags"
              @update="sampleData.tags = $event"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- File Upload -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        File Upload
      </h2>
      <div class="max-w-lg">
        <File
          :multiple="false"
          file-format=".jpg,.png,.jpeg"
          @change="handleFileChange"
        />
      </div>
    </section>

    <!-- Color Picker -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Color Picker
      </h2>
      <div class="max-w-lg">
        <ColorPicker
          start-color="#3B82F6"
          @select="handleColorSelect"
        />
      </div>
    </section>

    <!-- Array Input -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Array Input
      </h2>
      <div class="max-w-lg">
        <Array
          v-model="sampleData.arrayItems"
          label="List Items"
          :items="sampleData.arrayItems"
          @update="sampleData.arrayItems = $event"
        />
      </div>
    </section>

    <!-- Styled Radio Button -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Styled Radio Buttons
      </h2>
      <div class="space-y-3">
        <StyledRadioButton
          :model="sampleData.radioSelection"
          label="Option A"
          value="optionA"
          @click="sampleData.radioSelection = $event"
        />
        <StyledRadioButton
          :model="sampleData.radioSelection"
          label="Option B"
          value="optionB"
          @click="sampleData.radioSelection = $event"
        />
        <StyledRadioButton
          :model="sampleData.radioSelection"
          label="Option C"
          value="optionC"
          @click="sampleData.radioSelection = $event"
        />
      </div>
    </section>

    <!-- Buttons Section -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Buttons
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Primary Buttons
          </h3>
          <div class="space-y-3">
            <ButtonPrimary size="lg">
              Large Primary
            </ButtonPrimary>
            <ButtonPrimary>Default Primary</ButtonPrimary>
            <ButtonPrimary size="sm">
              Small Primary
            </ButtonPrimary>
            <ButtonPrimary size="xs">
              Extra Small
            </ButtonPrimary>
          </div>
        </div>

        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Secondary Buttons
          </h3>
          <div class="space-y-3">
            <ButtonWhite size="lg">
              Large White
            </ButtonWhite>
            <ButtonWhite>Default White</ButtonWhite>
            <ButtonWhite size="sm">
              Small White
            </ButtonWhite>
            <ButtonWhite size="xs">
              Extra Small
            </ButtonWhite>
          </div>
        </div>

        <div class="space-y-4">
          <h3 class="text-lg font-medium text-gray-900">
            Button States
          </h3>
          <div class="space-y-3">
            <ButtonPrimary :is-disabled="true">
              Disabled Primary
            </ButtonPrimary>
            <ButtonWhite :disabled="true">
              Disabled White
            </ButtonWhite>
            <ButtonWhite :loading="true">
              Loading...
            </ButtonWhite>
          </div>
        </div>
      </div>

      <!-- Additional Button Variants -->
      <div class="mt-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          Additional Button Variants
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Secondary
            </h4>
            <ButtonSecondary>Secondary</ButtonSecondary>
            <ButtonSecondary size="sm">
              Small
            </ButtonSecondary>
          </div>

          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Gray
            </h4>
            <ButtonGray>Gray Button</ButtonGray>
            <ButtonGray size="sm">
              Small Gray
            </ButtonGray>
          </div>

          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Orange
            </h4>
            <ButtonOrange>Orange</ButtonOrange>
            <ButtonOrange size="sm">
              Small Orange
            </ButtonOrange>
          </div>

          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Destructive
            </h4>
            <ButtonWhiteDelete>Delete Item</ButtonWhiteDelete>
            <ButtonDelete>Confirm Delete</ButtonDelete>
          </div>
        </div>

        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Dark Theme
            </h4>
            <div class="bg-gray-900 p-4 rounded-lg">
              <ButtonDark>Dark Button</ButtonDark>
            </div>
          </div>

          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Blur Effect
            </h4>
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-lg">
              <ButtonBlur>Blur Button</ButtonBlur>
            </div>
          </div>

          <div class="space-y-3">
            <h4 class="text-sm font-medium text-gray-700">
              Round
            </h4>
            <ButtonRound>Round Button</ButtonRound>
          </div>
        </div>
      </div>
    </section>

    <!-- Cards Section -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Cards
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              Basic Card
            </h3>
            <p class="text-gray-600">
              This is a basic card component with some content inside.
            </p>
          </div>
        </Card>

        <Card class="border-2 border-blue-200">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              Highlighted Card
            </h3>
            <p class="text-gray-600">
              This card has a custom border style applied.
            </p>
          </div>
        </Card>

        <Card>
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Card with Button
            </h3>
            <p class="text-gray-600 mb-4">
              This card includes an action button.
            </p>
            <ButtonPrimary size="sm">
              Take Action
            </ButtonPrimary>
          </div>
        </Card>
      </div>
    </section>

    <!-- Modals & Popups Section -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Modals & Popups
      </h2>
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Modal Examples
          </h3>
          <div class="flex flex-wrap gap-4">
            <ButtonPrimary @click="showModal = true">
              Show Modal
            </ButtonPrimary>
            <ButtonWhite @click="showPopup = true">
              Show Popup
            </ButtonWhite>
            <ButtonSecondary @click="showSlideIn = true">
              Show Slide-in
            </ButtonSecondary>
          </div>
        </div>

        <!-- Modal Examples (Hidden by default) -->
        <Modal v-if="showModal" @close="showModal = false">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Sample Modal
            </h3>
            <p class="text-gray-600 mb-4">
              This is a sample modal component. It can contain any content and has a backdrop that closes the modal when clicked.
            </p>
            <div class="flex justify-end space-x-3">
              <ButtonWhite @click="showModal = false">
                Cancel
              </ButtonWhite>
              <ButtonPrimary @click="showModal = false">
                Confirm
              </ButtonPrimary>
            </div>
          </div>
        </Modal>

        <Popup v-if="showPopup" title="Sample Popup" @closeModal="showPopup = false">
          <div class="space-y-4">
            <p class="text-gray-600">
              This is a popup component with a title and close button. It's typically used for forms or detailed content.
            </p>
            <Input v-model="sampleData.popupInput" label="Sample Input" placeholder="Enter something..." />
            <div class="flex justify-end space-x-3 pt-4">
              <ButtonWhite @click="showPopup = false">
                Cancel
              </ButtonWhite>
              <ButtonPrimary @click="showPopup = false">
                Save
              </ButtonPrimary>
            </div>
          </div>
        </Popup>

        <PopupSlideIn
          v-if="showSlideIn"
          title="Slide-in Panel"
          description="This panel slides in from the right side"
          @closeModal="showSlideIn = false"
        >
          <div class="space-y-4">
            <p class="text-gray-600">
              Slide-in panels are great for forms, settings, or detailed views that don't need to completely take over the screen.
            </p>
            <Select
              v-model="sampleData.slideInSelect"
              label="Sample Select"
              :options="selectOptions"
            />
            <TextArea
              v-model="sampleData.slideInTextarea"
              label="Comments"
              placeholder="Add your comments..."
              :rows="4"
            />
          </div>
        </PopupSlideIn>
      </div>
    </section>

    <!-- Spacing & Layout -->
    <section>
      <h2 class="text-2xl font-semibold text-gray-900 mb-6">
        Spacing & Layout
      </h2>
      <div class="space-y-6">
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Spacing Scale
          </h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-4">
              <div class="w-16 text-sm text-gray-600">
                xs (0.5)
              </div>
              <div class="h-2 bg-blue-500" style="width: 0.125rem;" />
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-16 text-sm text-gray-600">
                sm (1)
              </div>
              <div class="h-2 bg-blue-500" style="width: 0.25rem;" />
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-16 text-sm text-gray-600">
                base (2)
              </div>
              <div class="h-2 bg-blue-500" style="width: 0.5rem;" />
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-16 text-sm text-gray-600">
                md (3)
              </div>
              <div class="h-2 bg-blue-500" style="width: 0.75rem;" />
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-16 text-sm text-gray-600">
                lg (4)
              </div>
              <div class="h-2 bg-blue-500" style="width: 1rem;" />
            </div>
            <div class="flex items-center space-x-4">
              <div class="w-16 text-sm text-gray-600">
                xl (6)
              </div>
              <div class="h-2 bg-blue-500" style="width: 1.5rem;" />
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      sampleData: {
        textInput: 'Sample text',
        textInputError: 'This has an error',
        textInputDisabled: 'Disabled text',
        textArea: 'This is a sample message in a textarea component.',
        textAreaError: 'Error message',
        select: 'option2',
        selectDisabled: 'option1',
        checkbox1: true,
        checkbox2: false,
        checkboxDark: true,
        search: '',
        range: 75,
        toggle: true,
        tags: ['design', 'system', 'components'],
        arrayItems: ['Item 1', 'Item 2', 'Item 3'],
        radioSelection: 'optionA'
      },
      selectOptions: [
        { value: 'option1', text: 'Option 1' },
        { value: 'option2', text: 'Option 2' },
        { value: 'option3', text: 'Option 3' }
      ]
    }
  },
  head () {
    return {
      title: 'Design System | Admin'
    }
  },
  methods: {
    handleFileChange (event) {
      console.log('File selected:', event.target.files[0])
    },
    handleColorSelect (color) {
      console.log('Color selected:', color)
    }
  }
}
</script>

<style scoped>
/* Custom styles for design system page */
</style>
