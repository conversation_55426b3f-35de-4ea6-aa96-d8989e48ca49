<template>
  <div>
    <Header />
    <div class="w-full bg-teal-50/80">
      <div class="max-w-3xl mx-auto flex items-center justify-center flex-col py-24 space-y-6 text-center">
        <div class="flex items-center justify-center space-y-2 flex-col">
          <h2 class=" text-sm uppercase font-medium text-gray-800">
            Affiliate program
          </h2>
          <h1 class="text-2xl leading-[1.6rem] font-bold tracking-tight text-primary-500 sm:text-4xl sm:leading-[3rem] lg:text-[38px]">
            We take their headshots. You take 30%
          </h1>
          <p class="text-lg text-gray-800">
            We pay our affiliates more than any other AI headshot generator and provide them with valuable marketing resources to make promotion easy.
          </p>
        </div>
        <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 space-x-4 items-center justify-center">
          <ListCheckSolid>30% commission on all sales</ListCheckSolid>
          <ListCheckSolid>Bi-weekly payout</ListCheckSolid>
          <ListCheckSolid>Highest conversion rate (~5%)</ListCheckSolid>
        </div>
        <a href="https://headshotpro-1.getrewardful.com/signup" target="_blank">
          <ButtonPrimary>
            Join the HeadshotPro affiliate program
          </ButtonPrimary>
        </a>
      </div>
    </div>
    <div class="w-full px-4 md:px-0">
      <div class="max-w-6xl mx-auto py-16 space-y-16 md:space-y-32">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
          <div class="flex flex-col space-y-4 items-start justify-center h-full order-1">
            <h2 class="font-bold text-2xl md:text-3xl text-brand-500">
              A product loved by {{ $store.state.stats.users }} happy customers
            </h2>
            <p class="text-gray-800">
              HeadshotPro has generated over 14 million professional headshots for over {{ $store.state.stats.users }} customers.
            </p>
            <ul class="list-disc flex flex-col ml-4 space-y-2">
              <li>Get a sale for every 20 visitors with our industry leading conversion rate (~5%) and lowest refund rate.</li>
              <li>
                Every purchase is covered by our <nuxt-link to="/refund" class="underline" target="_blank">
                  14-day profile-worthy money back guarantee
                </nuxt-link>.
              </li>
              <li>Relevant to anybody with a LinkedIn profile, an online presence, or a business.</li>
            </ul>
          </div>
          <div class="grid grid-cols-2 gap-4 h-full px-8 order-2">
            <template v-for="item of filteredReviews.slice(0, 2)">
              <div :key="item._id" class="transition duration-300 relative overflow-hidden">
                <ImageDns :src="item.image" class="h-full w-full object-cover" />
                <div v-if="item?.review?.quote" class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
                  <p class="font-bold">
                    {{ item?.review?.title }}
                  </p>
                  <p class="italic text-white/80 text-[10px] leading-[12px]">
                    "{{ item?.review?.quote }}"
                  </p>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
          <div class="flex flex-col space-y-4 items-start justify-center h-full order-1 md:order-2">
            <h2 class="font-bold text-2xl md:text-3xl text-brand-500">
              Industry-leading affiliate terms
            </h2>
            <p class="text-gray-800">
              We take care of our affiliates. We pay the highest commission rate in the industry and provide you with valuable marketing resources to make promotion easy.
            </p>
            <ul class="list-disc flex flex-col ml-4 space-y-4 text-sm ">
              <li>
                High commission rate at 30%
                <ul class="list-disc ml-4 text-sm text-gray-700 space-y-2 mt-2">
                  <li>$8.70 commission on $29 package</li>
                  <li>$11.70 commission on $39 package (most popular)</li>
                  <li>$14.70 commission on $49 package</li>
                </ul>
              </li>
              <li>Low minimum payout starting from $25.</li>
              <li>Fast payout schedule every 15 days via PayPal.</li>
              <li>Long cookie length of 60 days</li>
            </ul>
          </div>
          <div class="px-8 order-2 md:order-1">
            <ImageDns :src="require('@/assets/img/affiliate-2.png')" class="w-full object-cover" />
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
          <div class="flex flex-col space-y-4 items-start justify-center h-full order-1">
            <h2 class="font-bold text-2xl md:text-3xl text-brand-500">
              We’ll help you earn more money
            </h2>
            <ul class="list-disc flex flex-col ml-4 space-y-2">
              <li>
                Get a discount of up to 100% off on a special reviewer AI headshot package. <nuxt-link to="/content/review-us" class="underline" target="_blank">
                  Apply here.
                </nuxt-link>
              </li>
              <li>Get promotional material to use in your coverage of HeadshotPro, including detailed feature breakdowns, verified customer reviews, and quotes from the founder</li>
              <li>Get a direct line of email communication with the HeadshotPro team, where you’re free to ask questions that help you create more in depth content</li>
            </ul>
          </div>
          <div class="grid grid-cols-2 gap-4 h-full px-8 order-2">
            <template v-for="item of filteredReviews.slice(2, 4)">
              <div :key="item._id" class="transition duration-300 relative overflow-hidden">
                <ImageDns :src="item.image" class="h-full w-full object-cover" />
                <div v-if="item?.review?.quote" class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
                  <p class="font-bold">
                    {{ item?.review?.title }}
                  </p>
                  <p class="italic text-white/80 text-[10px] leading-[12px]">
                    "{{ item?.review?.quote }}"
                  </p>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="relative w-full">
        <!-- START CTA -->
        <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
          <div class="relative overflow-hidden rounded-xl bg-teal-500 shadow-lg">
            <div class="absolute bottom-0 right-0 -mr-10">
              <img class="w-full" src="@/assets/img/ring-pattern.svg" alt="">
            </div>

            <div class="relative flex flex-col items-center lg:flex-row lg:items-center">
              <div class="px-6 py-12 text-center sm:p-12 lg:py-16 lg:text-left xl:p-20">
                <h5 class="text-3xl font-bold text-white sm:text-3xl lg:text-3xl">
                  Ready to start earning?
                </h5>
                <p class="mt-5 text-base font-normal text-white">
                  In 2023, HeadshotPro affiliates earned an average of $2178. Sign up as a HeadshotPro affiliate in 2 minutes or less and start earning real money on the paying customers you refer.
                </p>

                <div class="mt-8 flex flex-col items-center gap-4 sm:flex-row sm:justify-center sm:gap-6 lg:justify-start">
                  <a href="https://headshotpro-1.getrewardful.com/signup" title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-primary-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-teal-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                    Join now
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </a>
                </div>
              </div>

              <div class="mt-4 shrink-0 lg:mt-0 lg:px-8 xl:px-16">
                <ImageDns width="422" height="444" class="w-full max-w-sm origin-bottom scale-110 lg:translate-y-16" :src="require('@/assets/img/cta-image.png')" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <MarketingFooter class="mt-16" />
    </div>
  </div>
</template>

<script>
export default {
  head () {
    return {
      title: 'HeadshotPro Affiliate Program | Earn 30% commission on all sales',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'We pay our affiliates more than any other AI headshot generator and provide them with valuable marketing resources to make promotion easy.'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/affiliate'
        }
      ]
    }
  },
  computed: {
    reviews () {
      return this.$store.state.reviews
    },
    filteredReviews () {
      // Create a copy of reviews array and sort it
      return [...this.reviews].sort((a, b) => {
        if (a.review.quote && !b.review.quote) {
          return -1
        }
        if (!a.review.quote && b.review.quote) {
          return 1
        }
        return 0
      })
    }
  }
  // async asyncData ({ $content, params, error }) {
  //   try {
  //     const article = await $content('affiliate').fetch()

  //     return {
  //       article
  //     }
  //   } catch (err) {
  //     error({ statusCode: 404, message: 'Post not found' })
  //   }
  // }
}
</script>
