<template>
  <section class=" text-black flex items-start md:items-center justify-center min-h-screen bg-gray-50 px-4 py-8 md:pt-0">
    <div class=" flex items-center justify-center flex-col">
      <nuxt-link to="/">
        <Logo class="w-[180px] text-black" />
      </nuxt-link>
      <Card class="mt-4 rounded-lg shadow w-full max-w-md p-8">
        <LoadingWrapper :is-loading="isLoading">
          <div class=" text-left">
            <template v-if="message && message.length > 0">
              <p class="text-sm text-gray-900 text-center">
                {{ message }}
              </p>
            </template>
            <template v-else>
              <h2 class="font-medium text-xl">
                Unsubscribe from all emails
              </h2>
              <p class="text-sm text-gray-700 mb-4">
                Fill in your email and we'll unsubscribe you immidiatly from all marketing emails.
              </p>
              <Input
                v-model="email"
                label="Your email"
                placeholder="<EMAIL>"
              />
              <ButtonDark size="sm" class="w-full mt-4" @click="unsubscribe">
                Unsubscribe me
              </ButtonDark>
              <p class="text-xs text-gray-500 mt-2">
                We will still occasionally send you important emails.
              </p>
            </template>
          </div>
        </LoadingWrapper>
      </Card>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      email: null,
      message: null,
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Unsubscribe | HeadshotPro'
    }
  },
  mounted () {
    // Check if route.query.email is set
    if (this.$route.query && this.$route.query.email) {
      this.email = this.$route.query.email
      // Check if valid email with vanialla JS
      if (this.email && this.email.length > 0 && this.email.includes('@')) {
        this.unsubscribe()
      } else {
        this.$toast.warning('Invalid email address')
      }
    }
    this.isLoading = false
  },
  methods: {
    async unsubscribe () {
      this.isLoading = true
      const response = await this.$axios.$get(
        `/user/email/unsubscribe/?email=${this.email}`
      )
      this.message = response
      this.isLoading = false
    }
  }
}
</script>

<style></style>
