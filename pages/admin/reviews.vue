<template>
  <div class="p-8">
    <div class="mx-auto w-full p-8">
      <LoadingWrapper :is-loading="isLoading">
        <div class="grid grid-cols-6 gap-4">
          <div v-for="item in items" :key="item._id" class="group relative space-y-2 shadow-box bg-white rounded-md">
            <ImageDns :src="item.image" class="w-full" />
            <div class="flex flex-col space-y-2 p-2">
              <span class="text-xs">ID: {{ item._id }}</span>
              <strong class="text-sm font-bold">{{ item.title }}</strong>
              <p v-if="item?.review?.quote" class="text-sm italic">
                "{{ item.review.quote }}"
              </p>
              <span class="text-xs" :style="{ color: item.review.frontpage ? 'green' : 'inherit' }">
                Frontpage: {{ item.review.frontpage ? 'Yes' : 'No' }}
              </span>
            </div>
            <ButtonWhite class="hidden group-hover:flex top-2 left-2 absolute" size="sm" @click="setupQuoteModal(item._id)">
              <span v-if="!item?.review?.quote">Add quote</span>
              <span v-if="item?.review?.quote">Edit quote</span>
            </ButtonWhite>
            <ButtonDelete class="hidden group-hover:flex bottom-2 left-2 absolute" size="sm" @click="removeReview(item._id)">
              Remove review
            </ButtonDelete>
            <ButtonWhite class="hidden group-hover:flex top-2 right-2 absolute" size="sm" @click="setupTrustpilotScoreModal(item._id)">
              <span>Trustpilot ⭐️</span>
            </ButtonWhite>
            <ButtonWhite class="hidden group-hover:flex bottom-2 right-2 absolute" size="sm" @click="toggleFrontPage(item._id)">
              <span v-if="!item.review.frontpage">Add to front page</span>
              <span v-else>Remove front page</span>
            </ButtonWhite>
          </div>
        </div>
      </LoadingWrapper>
    </div>
    <Modal v-if="showQuoteModal" @close="showQuoteModal = false">
      <div class="w-[500px] p-4 flex flex-col space-y-2">
        <LoadingWrapper :is-loading="isLoading">
          <InputTextArea v-model="quote" placeholder="Leave empty to delete it" label="Quote" />
          <ButtonPrimary @click="saveQuote">
            Save quote
          </ButtonPrimary>
        </LoadingWrapper>
      </div>
    </Modal>
    <Modal v-if="showTrustpilotModal" @close="showTrustpilotModal = false">
      <div class="w-[500px] p-4 flex flex-col space-y-2">
        <LoadingWrapper :is-loading="isLoading">
          <Input v-model="trustpilotScore" type="number" :max="5" placeholder="Leave empty to delete it" label="1-5" />
          <ButtonPrimary @click="saveTrustpilotScore">
            Save
          </ButtonPrimary>
        </LoadingWrapper>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      items: [],
      isLoading: true,
      showQuoteModal: false,
      showTrustpilotModal: false,
      quote: null,
      trustpilotScore: null,
      selectedItem: null
    }
  },

  async created () {
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData () {
      this.isLoading = true
      const { data } = await this.$axios.$get('/admin/reviews/all')
      this.items = data
      this.isLoading = false
    },
    saveQuote () {
      this.isLoading = true
      this.$axios.$post('/admin/reviews/quote/' + this.selectedItem._id, { quote: this.quote })
        .then((response) => {
          if (response.success) {
          // Update this.items
            this.items = this.items.map((item) => {
              if (item._id === this.selectedItem._id) {
                item.review.quote = this.quote
              }
              return item
            })

            this.selectedItem = null
            this.quote = null
            this.showQuoteModal = false
            this.isLoading = false

            this.$toast.success('Quote saved')
          }
        }).catch((err) => {
          console.log(err)
          this.isLoading = false
        })
    },
    saveTrustpilotScore () {
      this.isLoading = true
      this.$axios.$post('/admin/reviews/trustpilot/' + this.selectedItem._id, { score: this.trustpilotScore })
        .then((response) => {
          if (response.success) {
          // Update this.items
            this.items = this.items.map((item) => {
              if (item._id === this.selectedItem._id) {
                item.review.trustpilotScore = this.trustpilotScore
              }
              return item
            })

            this.selectedItem = null
            this.trustpilotScore = null
            this.showTrustpilotModal = false
            this.isLoading = false

            this.$toast.success('Trustpilot score saved')
          }
        }).catch((err) => {
          console.log(err)
          this.isLoading = false
        })
    },
    removeReview (id) {
      this.isLoading = true
      this.$axios.$post('/admin/reviews/remove/' + id)
        .then((response) => {
          if (response.success) {
            this.items = this.items.filter(item => item._id !== id)
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.isLoading = false
        })
    },
    setupQuoteModal (id) {
      this.showQuoteModal = true
      this.selectedItem = this.items.find(item => item._id === id)
      this.quote = this.selectedItem?.review?.quote
    },
    setupTrustpilotScoreModal (id) {
      this.showTrustpilotModal = true
      this.selectedItem = this.items.find(item => item._id === id)
      this.trustpilotScore = this.selectedItem?.review?.trustpilotScore
    },
    toggleFrontPage (id) {
      this.isLoading = true
      this.$axios.$post('/admin/reviews/frontpage/' + id)
        .then((response) => {
          if (response.success) {
            this.items = this.items.map((item) => {
              if (item._id === id) {
                item.review.frontpage = !item.review.frontpage
              }
              return item
            })

            this.isLoading = false
            this.$toast.success('Front page updated')
          }
        }).catch((err) => {
          console.log(err)
          this.isLoading = false
        })
    }
  }
}
</script>

<style></style>
