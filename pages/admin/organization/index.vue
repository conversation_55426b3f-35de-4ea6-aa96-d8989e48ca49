<template>
  <div class="p-8">
    <div class="grid grid-cols-5 gap-4 pb-5">
      <Input v-model="search" label="Search" placeholder="Search" class="w-full" @handleEnter="fetchData" />
      <InputSelect v-model="searchBy" label="Search by" theme="v2" :options="['_id', 'uid', 'name', 'email', 'url', 'teamSize']" class="w-full" />
      <InputSelect
        v-model="filter.type"
        label="Filter"
        theme="v2"
        :options="['no-filter', 'totalCreditsPurchased', 'teamSize']"
        class="w-full"
        placeholder="Select filter"
      />
      <Input
        v-if="filter.type === 'totalCreditsPurchased'"
        v-model="filter.value"
        label="Total credits purchased"
        type="number"
        :placeholder="`Filter by ${filter.type}`"
        class="w-full "
        @handleEnter="fetchData"
      />
      <InputSelect
        v-if="filter.type === 'teamSize'"
        v-model="filter.value"
        label="Team size"
        :options="['1-9', '10-24', '25-49', '50-99', '100-249', '250-499', '500-999', '1000+']"
        class="w-full "
        placeholder="Select team size"
      />
      <div class="flex flex-col justify-end">
        <ButtonPrimary size="sm" @click="fetchData">
          Search
        </ButtonPrimary>
      </div>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Status', 'Created', 'Name', 'Size', 'Owner', 'Credits', 'Ever purchased', 'Discount','Stripe', 'Actions']">
          <TableRow v-for="organization in organizations" :key="organization._id">
            <TableItem>
              <Badge v-if="organization.totalCreditsPurchased > 0" color="green">
                Paid
              </Badge>
              <Badge v-else color="gray">
                Unpaid
              </Badge>
            </TableItem>
            <TableItem>{{ formatDate(organization.createdAt) }}</TableItem>
            <TableItem>
              <p class="text-gray-900">
                {{ organization.name }}
              </p>
              <a class="text-xs hover:underline" :href="`/admin/user?search=${organization._id}&searchBy=organization`" target="_blank">
                {{ organization._id }}
              </a>
              <p v-if="organization.website" class="text-xs">
                <a :href="organization.website" target="_blank">{{ organization.website }}</a>
              </p>
            </TableItem>
            <TableItem>
              <p class="text-gray-900">
                {{ organization?.teamSize || "" }}
              </p>
            </TableItem>
            <TableItem>
              <p class="text-gray-900">
                {{ organization?.owner?.email || "" }}
              </p>
              <a class="text-xs hover:underline" :href="`/admin/user?search=${organization?.owner?._id}&searchBy=_id`" target="_blank">
                {{ organization?.owner?._id }}
              </a>
            </TableItem>
            <TableItem>{{ organization.credits }}</TableItem>
            <TableItem>{{ organization.totalCreditsPurchased ?? 0 }}</TableItem>
            <TableItem>{{ getDiscountRate(organization, organization.totalCreditsPurchased) }}</TableItem>
            <TableItem>
              <a v-if="organization?.stripe?.accountId" :href="`https://dashboard.stripe.com/customers/${organization.stripe.accountId}`" target="_blank">
                {{ stripLength(organization?.stripe?.accountId, 10) }}
              </a>
            </TableItem>
            <TableItem>
              <div class="flex items-center space-x-2">
                <ButtonWhite size="xs" @click="selectedOrganization = organization; showMoreInfoModal = true">
                  Details
                </ButtonWhite>
                <ButtonWhite
                  size="xs"
                  @click="
                    showModal = true;
                    selectedOrganization = organization;
                  "
                >
                  Edit
                </ButtonWhite>
                <nuxt-link :to="`/admin/transactions?search=${organization._id}&searchBy=organization`">
                  <ButtonWhite size="xs">
                    Transactions
                  </ButtonWhite>
                </nuxt-link>
              </div>
            </TableItem>
          </TableRow>
        </Table>
      </LoadingWrapper>
    </Card>
    <Popup v-if="showModal" size="xl" @closeModal="resetModal()">
      <LoadingWrapper :is-loading="isSaving">
        <div class="divide-y divide-gray-200">
          <div class="flex items-center justify-between py-3">
            <div>
              <p class="text-lg font-medium text-gray-900">
                Add credits (purchase)
              </p>
              <p class="text-sm font-medium text-gray-500">
                When adding credits as a purchase, they will count towards calculating the discount for the team.
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <Input v-model.number="addPurchaseCreditsAmount" class="w-[100px]" type="number" />
              <ButtonPrimary size="xs" @click="addCredits('purchase')">
                Add purchase
              </ButtonPrimary>
            </div>
          </div>
          <div class="flex items-center justify-between py-3">
            <div>
              <p class="text-lg font-medium text-gray-900">
                Add credits (support)
              </p>
              <p class="text-sm font-medium text-gray-500">
                When adding credits as a support, they will NOT count towards calculating the discount for the team.
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <Input v-model.number="addSupportCreditsAmount" class="w-[100px]" type="number" />
              <ButtonPrimary size="xs" @click="addCredits('support')">
                Add credits
              </ButtonPrimary>
            </div>
          </div>
          <div class="flex items-center justify-between py-3">
            <p class="text-lg font-medium text-gray-900">
              Remove credits
            </p>
            <div class="flex items-center space-x-2">
              <Input v-model.number="removeCreditsAmount" class="w-[100px]" type="number" />
              <ButtonPrimary size="xs" @click="removeCredits">
                Remove
              </ButtonPrimary>
            </div>
          </div>
          <div class="flex items-center justify-between py-3">
            <p class="text-lg font-medium text-gray-900">
              Remove ALL credits
            </p>
            <div class="flex items-center space-x-2">
              <ButtonPrimary size="xs" @click="removeCredits('all')">
                Remove all
              </ButtonPrimary>
            </div>
          </div>
          <div class="flex items-center justify-between py-3">
            <p class="text-lg font-medium text-gray-900">
              Transfer ownership
            </p>
            <div class="flex items-center space-x-2">
              <Input v-model="transferOwnerEmail" placeholder="Email" />
              <ButtonPrimary size="xs" @click="transferOwnership">
                Transfer
              </ButtonPrimary>
            </div>
          </div>
          <div class="flex items-center justify-between py-3">
            <p class="text-lg font-medium text-gray-900">
              Delete all user data after 30 days
              <span class="block text-sm text-gray-500">
                ⚠️ Warning: if enabled, all team member results will be deleted 30 days after their photo shoot is completed.
              </span>
            </p>
            <div class="flex items-center space-x-2">
              <InputToggle
                :active="selectedOrganization?.shouldDeleteModels || false"
                @change="toggleDeletion"
              />
            </div>
          </div>
          <div class="flex items-center justify-between py-3">
            <div>
              <p class="text-lg font-medium text-gray-900">
                Features
              </p>
              <p class="text-sm font-medium text-gray-500">
                Enable or disable features for this organization.
              </p>
            </div>
          </div>
          <div class="flex items-center justify-between py-3 pl-4">
            <div>
              <p class="text-base font-medium text-gray-900">
                Bulk Upload
              </p>
              <p class="text-sm text-gray-500">
                Allow organization to use bulk upload feature for creating multiple models at once.
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <InputToggle
                :active="selectedOrganization?.features?.bulkUpload || false"
                @change="toggleBulkUpload"
              />
            </div>
          </div>
        </div>
      </LoadingWrapper>
    </Popup>
    <Popup v-if="showMoreInfoModal" size="xl" @closeModal="showMoreInfoModal = false">
      <AdminOrganizationDetails :organization="selectedOrganization" />
    </Popup>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      organizations: [],
      isLoading: true,
      isSaving: false,
      search: null,
      showModal: false,
      selectedOrganization: null,
      addPurchaseCreditsAmount: 0,
      addSupportCreditsAmount: 0,
      removeCreditsAmount: 0,
      showMore: null,
      searchBy: 'email',
      transferOwnerEmail: null,
      showMoreInfoModal: false,
      filter: {
        type: 'no-filter',
        value: null
      }
    }
  },
  watch: {
    '$route.query': {
      handler (newQuery) {
        const { search, searchBy, id } = newQuery

        if (search !== undefined) {
          this.search = search
        }

        if (searchBy && ['_id', 'uid', 'name', 'email', 'url', 'teamSize'].includes(searchBy)) {
          this.searchBy = searchBy
        }

        // Legacy support for 'id' parameter
        if (id && !search) {
          this.search = id
          this.searchBy = '_id'
        }

        this.fetchData()
      },
      deep: true
    }
  },
  async created () {
    // Handle URL search parameters
    const { search, searchBy, id } = this.$route.query

    if (search) {
      this.search = search
    }

    if (searchBy && ['_id', 'uid', 'name', 'email', 'url', 'teamSize'].includes(searchBy)) {
      this.searchBy = searchBy
    }

    // Legacy support for 'id' parameter (maps to _id search)
    if (id && !search) {
      this.search = id
      this.searchBy = '_id'
    }

    await this.fetchData()
  },
  methods: {
    getDiscountRate (organization, totalCreditsPurchased) {
      const discountOptions = this.getTeamDiscountOptions(organization)
      console.log({ organization, discountOptions })
      const discount = discountOptions.find(option => totalCreditsPurchased >= option.from && totalCreditsPurchased <= option.to)
      return discount ? `${discount.discount}%` : '-'
    },
    async toggleBulkUpload () {
      try {
        this.isSaving = true

        // Calculate the new state (opposite of current state)
        const currentState = this.selectedOrganization?.features?.bulkUpload || false
        const newState = !currentState

        const { success, errorMessage } = await this.$axios.$post('/admin/organization/toggle-feature', {
          organizationId: this.selectedOrganization._id,
          feature: 'bulkUpload',
          enabled: newState
        })
        if (!success) {
          throw new Error(errorMessage)
        }

        // Update the local organization object
        if (!this.selectedOrganization.features) {
          this.selectedOrganization.features = {}
        }
        this.selectedOrganization.features.bulkUpload = newState

        // Also update the organization in the list
        const orgInList = this.organizations.find(org => org._id === this.selectedOrganization._id)
        if (orgInList) {
          if (!orgInList.features) {
            orgInList.features = {}
          }
          orgInList.features.bulkUpload = newState
        }

        this.$toast.success(`Bulk upload ${newState ? 'enabled' : 'disabled'}`)
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    async toggleDeletion () {
      try {
        this.isSaving = true
        const currentState = this.selectedOrganization?.shouldDeleteModels || false
        const enabled = !currentState

        const { success, errorMessage } = await this.$axios.$post('/admin/organization/toggle-deletion', {
          organizationId: this.selectedOrganization._id,
          enabled
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success(`Deletion ${enabled ? 'enabled' : 'disabled'}`)

        // Update the local organization object
        this.selectedOrganization.shouldDeleteModels = enabled

        // Also update the organization in the list
        const orgInList = this.organizations.find(org => org._id === this.selectedOrganization._id)
        if (orgInList) {
          orgInList.shouldDeleteModels = enabled
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    transferOwnership () {
      try {
        if (!this.transferOwnerEmail) {
          return this.$toast.error('Please enter an email')
        }
        this.$axios.$post('/admin/organization/transfer-ownership', {
          email: this.transferOwnerEmail,
          organizationId: this.selectedOrganization._id
        }).then((res) => {
          if (!res.success) {
            return this.$toast.error(res.errorMessage)
          }
          this.$toast.success('Ownership transferred')
          // this.resetModal()
          // await this.fetchData()
        }).catch((err) => {
          this.$toast.error(err.response.data.errorMessage)
        })
      } catch (err) {
        this.$toast.error(err.message)
      }
    },
    async fetchData () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/organization', { params: { search: this.search, searchBy: this.searchBy, ...(this.filter.type !== 'no-filter' && { filter: this.filter }) } })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.organizations = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async addCredits (as = 'purchase') {
      try {
        this.isSaving = true
        const amount = as === 'purchase' ? this.addPurchaseCreditsAmount : this.addSupportCreditsAmount
        if (amount <= 0) {
          throw new Error('Please enter a valid amount')
        }

        const { success, errorMessage } = await this.$axios.$post('/admin/organization/add-credits', {
          amount,
          organizationId: this.selectedOrganization._id,
          type: as
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Credits added')
        this.changeCredits(this.selectedOrganization._id, amount, 'add')
        this.resetModal()
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    async removeCredits (type = 'remove') {
      try {
        this.isSaving = true
        if (type === 'remove' && this.removeCreditsAmount <= 0) {
          throw new Error('Please enter a valid amount')
        }
        const amount = type === 'remove' ? this.removeCreditsAmount : this.selectedOrganization.credits

        const { success, errorMessage } = await this.$axios.$post('/admin/organization/remove-credits', {
          amount,
          organizationId: this.selectedOrganization._id
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Credits removed')
        this.changeCredits(this.selectedOrganization._id, amount, 'remove')
        this.resetModal()
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },

    changeCredits (organizationId, amount, type) {
      const organization = this.organizations.find(org => org._id === organizationId)
      if (type === 'add') {
        organization.credits += amount
      } else if (type === 'remove') {
        organization.credits -= amount
      }
    },

    resetModal () {
      this.showModal = false
      this.selectedOrganization = null
      this.addPurchaseCreditsAmount = 0
      this.addSupportCreditsAmount = 0
      this.removeCreditsAmount = 0
    }
  }
}
</script>

<style></style>
