<template>
  <div class="p-8 pb-32">
    <LoadingWrapper :is-loading="isLoading">
      <Card class="mx-auto w-full min-h-3/4">
        <h2 class="font-bold text-lg">
          Pending refunds
        </h2>
        <Table :head="['Date', 'Country', 'Reasons', 'Additional', 'Type', 'UID', 'Stripe', 'Models to refund', 'Actions']">
          <template v-for="item in pendingRefunds">
            <!-- <template v-if="inSearch(item)"> -->
            <TableRow :key="item._id">
              <TableItem>{{ formatDate(item.createdAt) }}</TableItem>
              <TableItem>{{ item?.stripe?.country || 'N/A' }}</TableItem>
              <TableItem>
                {{ item.reasons.join(', ') }}
              </TableItem>
              <TableItem>
                <div class="w-[200px] break-all">
                  <p class="text-xs w-[200px] block break-all">
                    {{ item.additional }}
                  </p>
                </div>
              </TableItem>
              <TableItem>{{ item.type }}</TableItem>
              <TableItem>
                <nuxt-link :to="`/admin/user/?search=${item.user.uid}`">
                  {{ stripLength(item.user.uid, 10) }}
                </nuxt-link>
              </TableItem>
              <TableItem>
                <div class="flex flex-col">
                  <a v-if="item?.user?.lemonsqueezy?.accountId" :href="`https://app.lemonsqueezy.com/person/customers/${item?.user?.lemonsqueezy?.accountId}`" target="_blank">
                    {{ stripLength(item?.user?.lemonsqueezy?.accountId, 5) }}
                  </a>
                  <a v-if="item?.user?.stripe?.accountId" :href="`https://dashboard.stripe.com/customers/${item?.user?.stripe?.accountId}`" target="_blank">
                    {{ stripLength(item?.user?.stripe?.accountId, 5) }}
                  </a>
                  <a v-if="item.user.organization?.stripe.accountId" :href="`https://dashboard.stripe.com/customers/${item.user.organization?.stripe.accountId}`" target="_blank">
                    {{ stripLength(item.user.organization?.stripe.accountId, 5) }}
                  </a>
                  <nuxt-link v-if="item?.user?.paypal?.accountId" :to="`/admin/transactions?uid=${item.user.uid}`">
                    {{ stripLength(item?.user?.paypal?.accountId, 5) }}
                  </nuxt-link>
                </div>
              </TableItem>
              <TableItem>
                <nuxt-link v-for="model in item.models" :key="model" :to="`/admin/model/${model}`" class="text-xs">
                  {{ model }}
                </nuxt-link>
              </TableItem>
              <TableItem>
                <ButtonDropdown v-if="dropdownItems.length > 0" :items="dropdownItems" title="Actions" @select="handleDropdown($event, item._id)" />
              </TableItem>
            </TableRow>
            <!-- </template> -->
          </template>
        </Table>
      </Card>
      <Card class="mx-auto w-full mt-16">
        <h2 class="font-bold text-lg">
          Processed refunds
        </h2>
        <Table :head="['Date', 'Country', 'Reasons', 'Additional', 'Type', 'UID', 'Stripe', 'Models to refund', 'Actions']">
          <template v-for="item in processedRefunds">
            <!-- <template v-if="inSearch(item)"> -->
            <TableRow :key="item._id">
              <TableItem>{{ formatDate(item.createdAt) }}</TableItem>
              <TableItem>{{ item?.stripe?.country || 'N/A' }}</TableItem>
              <TableItem>
                {{ item.reasons.join(', ') }}
              </TableItem>
              <TableItem>
                <div class="w-[200px] break-all">
                  <p class="text-xs w-[200px] block break-all">
                    {{ item.additional }}
                  </p>
                </div>
              </TableItem>
              <TableItem>{{ item.type }}</TableItem>
              <TableItem>
                <nuxt-link :to="`/admin/user/?search=${item.user.uid}&searchBy=uid`">
                  {{ stripLength(item.user.uid, 10) }}
                </nuxt-link>
              </TableItem>
              <TableItem>
                <a v-if="item?.user?.stripe?.accountId" :href="`https://dashboard.stripe.com/customers/${item?.user?.stripe?.accountId}`" target="_blank">
                  {{ stripLength(item?.user?.stripe?.accountId, 5) }}
                </a>
                <a v-if="item.user.organization?.stripe.accountId" :href="`https://dashboard.stripe.com/customers/${item.user.organization?.stripe.accountId}`" target="_blank">
                  {{ stripLength(item.user.organization?.stripe.accountId, 5) }}
                </a>
                <nuxt-link v-if="item?.user?.paypal?.accountId" :to="`/admin/transactions?uid=${item.user.uid}`">
                  {{ stripLength(item?.user?.paypal?.accountId, 5) }}
                </nuxt-link>
              </TableItem>
              <TableItem>
                <nuxt-link v-for="model in item.models" :key="model" :to="`/admin/model/${model}`" class="text-xs">
                  {{ model }}
                </nuxt-link>
              </TableItem>
              <TableItem>
                <ButtonDropdown v-if="dropdownItems.length > 0" :items="dropdownItems" title="Actions" @select="handleDropdown($event, item._id)" />
              </TableItem>
            </TableRow>
            <!-- </template> -->
          </template>
        </Table>
      </Card>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      dropdownItems: [
        { title: 'Refund full', value: 'refund:full' },
        { title: 'Refund partial', value: 'refund:partial' },
        { title: 'Decline', value: 'decline' },
        { title: 'Decline (many downloads)', value: 'decline:usage' },
        { title: 'Decline (contact support)', value: 'decline:support' },
        { title: 'DELETE', value: 'delete' }
      ],
      refunds: [],
      isLoading: true,
      search: null
    }
  },
  computed: {
    pendingRefunds () {
      return this.refunds.filter(item => item.status === 'pending')
    },
    processedRefunds () {
      return this.refunds.filter(item => item.status !== 'pending')
    }
  },
  async created () {
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async handleDropdown (event, value) {
      this.isLoading = true
      if (event.includes('decline')) {
        const array = event.split(':')
        const type = (array && array.length > 0) ? event.split(':')[1] : null
        await this.$axios.$post('/admin/refund/decline/' + value, { type })
      } else if (event.includes('refund:')) {
        const type = event.split(':')[1]
        await this.$axios.$post('/admin/refund/accept/' + value, { type })
      } else if (event.includes('delete')) {
        await this.$axios.$delete('/admin/refund/' + value)
      }
      await this.fetchData()
      this.isLoading = false
    },
    async fetchData () {
      this.isLoading = true
      this.refunds = await this.$axios.$get('/admin/refund/all')
      this.isLoading = false
    }
  }
}
</script>

<style></style>
