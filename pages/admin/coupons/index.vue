<template>
  <div class="w-full p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <ButtonWhite size="sm" class="flex-shrink-0" @click="unclaimed = !unclaimed">
        {{ unclaimed ? 'Show All' : 'Show Unclaimed' }}
      </ButtonWhite>
      <ButtonPrimary size="sm" class="flex-shrink-0" @click="showCreateModal = true">
        New Coupon
      </ButtonPrimary>
    </div>
    <Card class="relative">
      <LoadingWrapper :is-loading="items.length === 0">
        <Table :head="tableColumns">
          <TableRow v-for="item in filteredItems" :key="item._id">
            <TableItem>{{ item.code }}</TableItem>
            <TableItem>{{ item.package || 'large' }}</TableItem>
            <TableItem>{{ item.maxUses || 1 }}</TableItem>
            <TableItem>
              <span v-show="item.claimedBy" class="block cursor-pointer hover:text-primary-500" @click="copyToClipboard(item.claimedBy)">
                {{ stripLength(item.claimedBy, 6) }}
              </span>
              <span
                v-for="(uid, index) in (item.claimHistory || [])"
                :key="item._id + '_' + uid + '_' + index"
                class="block cursor-pointer hover:text-primary-500"
                @click="copyToClipboard(uid)"
              >
                {{ stripLength(uid, 6) }}
              </span>
            </TableItem>
            <TableItem>{{ claimStatus(item) }}</TableItem>
            <TableItem>{{ formatDate(item.createdAt) }}</TableItem>
            <TableItem>
              <div v-show="item.maxUses === 1 || !item.maxUses" class="flex items-center justify-start space-x-2">
                <ButtonWhite size="sm" @click="toggleClaimStatus(item._id)">
                  <span v-if="item.claimed">Mark as not claimed</span>
                  <span v-else>Mark as claimed</span>
                </ButtonWhite>
              </div>
            </TableItem>
          </TableRow>
        </Table>
      </LoadingWrapper>
    </Card>
    <portal to="modal">
      <Popup v-if="showCreateModal" size="4xl" @closeModal="showCreateModal = false">
        <div class="p-4 md:p-8 md:px-16 max-w-4xl">
          <div class="mx-auto text-left">
            <h2 class="text-sm md:text-lg font-bold text-primary-500">
              Create new coupon
            </h2>
          </div>
          <form @submit.prevent="createCoupon">
            <div class="mt-4 flex">
              <Input v-model="newCoupon.code" placeholder="Coupon code" class="w-full" />
              <div class="ml-4">
                <ButtonWhite type="button" size="sm" @click="generateRandomCoupon">
                  Random
                </ButtonWhite>
              </div>
            </div>
            <div class="mt-4 flex gap-4">
              <Input v-model="newCoupon.maxUses" placeholder="Max uses" type="number" class="w-full" label="Max uses" />
              <InputSelect v-model="newCoupon.package" :options="possiblePackages" class="w-full" label="Package" />
            </div>
            <div class="mt-4">
              <ButtonPrimary type="submit">
                Create
              </ButtonPrimary>
            </div>
          </form>
        </div>
      </Popup>
    </portal>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      items: [],
      search: '',
      unclaimed: false,
      showCreateModal: false,
      newCoupon: {
        code: '',
        maxUses: 1,
        package: 'large'
      }
    }
  },
  async fetch () {
    const items = await this.$axios.$get('/admin/coupons')
    this.items = items

    if (this.$store.state.packages.length === 0) {
      await this.$store.dispatch('getPackages')
    }
  },
  computed: {
    serverUrl () {
      return process.env.SERVER_URL
    },
    tableColumns () {
      return ['Code', 'Package', 'Max uses', 'Used by', 'Status', 'Creation Date', 'Action']
    },
    filteredItems () {
      let items = this.items
      if (this.unclaimed) {
        const hasKey = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)
        items = items.filter(item => (hasKey(item, 'claimed') && !item.claimed) || (hasKey(item, 'maxUses') && ((item.uses || 0) < item.maxUses)))
      }

      if (this.search.trim().length > 0) {
        items = items.filter(item => item.code.toLowerCase().includes(this.search.toLowerCase()))
      }

      return items
    },
    possiblePackages () {
      const packages = []
      for (const key in this.$store.state.packages) {
        if (!this.$store.state.packages[key].meta?.visible) {
          continue
        }

        packages.push({
          title: this.$store.state.packages[key].title,
          value: key
        })
      }

      return packages
    }
  },
  methods: {
    toggleClaimStatus (id) {
      this.$axios.$post('/admin/coupons/claim/' + id)
        .then((response) => {
          if (response.success) {
            this.items = this.items.map((item) => {
              if (item._id === id) {
                item.claimed = response.claimed
                if (!response.claimed) {
                  item.claimedBy = null
                }
              }

              return item
            })
          }
        })
        .catch((err) => {
          this.$toast.error(err?.response?.data?.message || 'Unknown error')
        })
    },
    createCoupon () {
      if (this.newCoupon.code.trim().length === 0) {
        this.$toast.error('Coupon code is required')
        return
      }

      this.$axios.$post('/admin/coupons', this.newCoupon)
        .then((response) => {
          if (response.success) {
            this.items = response.coupons
            this.showCreateModal = false
            this.newCoupon.code = ''
          } else {
            this.$toast.error(response.err || 'Unknown error')
          }
        })
    },
    generateRandomCoupon () {
      const randomString = () => Math.random().toString(36).substring(9, 15).toUpperCase().padStart(4, '0')
      this.newCoupon.code = [randomString(), randomString(), randomString(), randomString()].join('-')
    },
    claimStatus (item) {
      const uses = item.uses || 0
      const maxUses = item.maxUses || 1
      if (uses >= 1 && uses < maxUses) {
        return item.uses + ' uses'
      }

      return (uses >= maxUses || item.claimed) ? '✅ Claimed' : '⏱️ Unclaimed'
    },
    copyToClipboard (text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          this.$toast.success('Copied to clipboard')
        })
        .catch(() => {
          this.$toast.error('Failed to copy to clipboard')
        })
    }
  }
}
</script>
