<template>
  <section class="flex min-h-screen items-start justify-center p-2 text-black md:p-4">
    <div class="w-full p-2 md:p-8">
      <AppTitle title="Clothing" sub-title="Clothing items people can choose from">
        <Input
          v-model="minFavorites"
          type="number"
          name="minFavorites"
          placeholder="Minimum favorites"
          label="Minimum favorites"
        />
        <InputSearch placeholder="Search" @input="filterClothing($event)" />
        <ButtonPrimary size="sm" @click="openAddNewModal">
          Add new
        </ButtonPrimary>
      </AppTitle>

      <Card class="mx-auto w-full" inner-class="overflow-x-auto">
        <LoadingWrapper :is-loading="isLoading">
          <TableSortable :head="tableHead" @sort="handleSort">
            <TableRow v-for="item in sortedClothingItems.filter(item => item.usage?.favorite >= minFavorites)" :key="item._id" :class="{ 'opacity-50 hover:opacity-100': !item.active}">
              <TableItem>
                <div class="flex flex-col space-y-2">
                  <!-- Active/Inactive Status -->
                  <div class="flex items-center">
                    <span
                      :class="{
                        'bg-green-100 text-green-800 border-green-200': item.active,
                        'bg-gray-100 text-gray-800 border-gray-200': !item.active
                      }"
                      class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                    >
                      <!-- Active Icon -->
                      <svg v-if="item.active" class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <!-- Inactive Icon -->
                      <svg v-else class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      </svg>
                      {{ item.active ? 'Active' : 'Inactive' }}
                    </span>
                  </div>

                  <!-- Processing Status Badge -->
                  <div v-if="item.processingStatus && item.processingStatus !== 'completed'">
                    <span
                      :class="{
                        'bg-yellow-100 text-yellow-800 border-yellow-200': item.processingStatus === 'pending',
                        'bg-blue-100 text-blue-800 border-blue-200': item.processingStatus === 'processing',
                        'bg-red-100 text-red-800 border-red-200': item.processingStatus === 'failed'
                      }"
                      class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                    >
                      <!-- Processing Icon -->
                      <svg v-if="item.processingStatus === 'processing'" class="animate-spin w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24">
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        />
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                      <!-- Pending Icon -->
                      <svg v-else-if="item.processingStatus === 'pending'" class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                      </svg>
                      <!-- Failed Icon -->
                      <svg v-else-if="item.processingStatus === 'failed'" class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                      </svg>
                      {{ item.processingStatus === 'pending' ? 'Pending' : item.processingStatus === 'processing' ? 'Processing' : 'Failed' }}
                    </span>
                  </div>
                </div>
              </TableItem>
              <TableItem class="group">
                <div class="flex items-center group-hover:hidden">
                  <span

                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                  >
                    {{ item.recommendedInPopular ? 'Yes' : '-' }}
                  </span>
                </div>
                <div class="items-center hidden group-hover:flex">
                  <ButtonWhite size="sm" @click="toggleRecommendedInPopular(item._id)">
                    {{ item.recommendedInPopular ? 'Remove from Featured' : 'Add to Featured' }}
                  </ButtonWhite>
                </div>
              </TableItem>
              <TableItem>
                <div class="flex flex-col space-y-1">
                  <span class="text-sm font-medium text-gray-900">
                    {{ formatDateRelative(item.createdAt) }}
                  </span>
                  <span class="text-xs text-gray-500" :title="formatDateFull(item.createdAt)">
                    {{ formatDateShort(item.createdAt) }}
                  </span>
                </div>
              </TableItem>
              <TableItem>
                <div v-if="item.images?.male || item.images?.female" class="flex items-center space-x-2">
                  <!-- Male Image -->
                  <div v-if="item.images?.male" class="relative">
                    <img
                      :src="item.images.male"
                      class="w-16 h-16 rounded-lg object-cover cursor-pointer border-2 border-gray-200 hover:border-blue-300 transition-colors"
                      :alt="`Male ${item.type}`"
                      @click="showEnlargedImage(item.images.male)"
                      @error="$event.target.style.display='none'"
                    >
                    <div class="absolute -bottom-1 -right-1 bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded-full border border-blue-200 font-medium">
                      M
                    </div>
                  </div>

                  <!-- Female Image -->
                  <div v-if="item.images?.female" class="relative">
                    <img
                      :src="item.images.female"
                      class="w-16 h-16 rounded-lg object-cover cursor-pointer border-2 border-gray-200 hover:border-pink-300 transition-colors"
                      :alt="`Female ${item.type}`"
                      @click="showEnlargedImage(item.images.female)"
                      @error="$event.target.style.display='none'"
                    >
                    <div class="absolute -bottom-1 -right-1 bg-pink-100 text-pink-800 text-xs px-1.5 py-0.5 rounded-full border border-pink-200 font-medium">
                      F
                    </div>
                  </div>
                </div>

                <!-- No Images Placeholder -->
                <div v-else class="w-16 h-16 rounded-lg bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </TableItem>
              <TableItem>
                <div class="flex flex-col space-y-1 max-w-xs">
                  <span class="text-sm font-semibold text-gray-900 capitalize">
                    {{ item.type }}
                  </span>
                  <p class="text-xs text-gray-600 line-clamp-2 leading-relaxed" :title="item.prompt">
                    {{ item.prompt?.length > 100 ? item.prompt.substring(0, 100) + '...' : item.prompt }}
                  </p>
                </div>
              </TableItem>
              <TableItem>
                <div v-if="item?.gender && item.gender.length > 0" class="flex flex-wrap gap-1">
                  <span
                    v-for="gender in item.gender"
                    :key="gender"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                    :class="{
                      'bg-blue-100 text-blue-800': gender === 'male',
                      'bg-pink-100 text-pink-800': gender === 'female'
                    }"
                  >
                    <svg v-if="gender === 'male'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6a2 2 0 114 0v1H8V6z" />
                    </svg>
                    <svg v-else-if="gender === 'female'" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6a2 2 0 114 0v1H8V6z" />
                    </svg>
                    {{ gender.charAt(0).toUpperCase() + gender.slice(1) }}
                  </span>
                </div>
                <div v-else class="text-xs text-gray-400 italic">
                  Not specified
                </div>
              </TableItem>
              <TableItem>
                <div v-if="item.usage" class="space-y-2">
                  <!-- Usage Stats -->
                  <div class="grid grid-cols-2 gap-2 text-xs">
                    <div class="bg-gray-50 px-2 py-1 rounded">
                      <span class="text-gray-500">Total:</span>
                      <span class="font-medium ml-1">{{ item.usage?.total || 0 }}</span>
                    </div>
                    <div class="bg-blue-50 px-2 py-1 rounded">
                      <span class="text-blue-600">Favorites:</span>
                      <span class="font-medium ml-1 text-blue-800">{{ item.usage?.favorite || 0 }}</span>
                    </div>
                  </div>

                  <!-- Conversion Rate with Visual Indicator -->
                  <div class="space-y-1">
                    <div class="flex items-center justify-between text-xs">
                      <span class="text-gray-600">Conversion Rate</span>
                      <span
                        class="font-medium"
                        :class="{
                          'text-green-600': (item.usage?.conversionRate || 0) >= 0.15,
                          'text-yellow-600': (item.usage?.conversionRate || 0) >= 0.10 && (item.usage?.conversionRate || 0) < 0.15,
                          'text-red-600': (item.usage?.conversionRate || 0) < 0.10
                        }"
                      >
                        {{ ((item.usage?.conversionRate || 0) * 100).toFixed(1) }}%
                      </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        class="h-1.5 rounded-full transition-all duration-300"
                        :class="{
                          'bg-green-500': (item.usage?.conversionRate || 0) >= 0.15,
                          'bg-yellow-500': (item.usage?.conversionRate || 0) >= 0.10 && (item.usage?.conversionRate || 0) < 0.15,
                          'bg-red-500': (item.usage?.conversionRate || 0) < 0.10
                        }"
                        :style="`width: ${Math.min((item.usage?.conversionRate || 0) * 100, 100)}%`"
                      />
                    </div>
                  </div>
                </div>
                <div v-else class="text-xs text-gray-400 italic">
                  No usage data
                </div>
              </TableItem>

              <TableItem>
                <ButtonDropdown
                  title="Actions"
                  theme="v2"
                  size="sm"
                  :items="getActionItems(item)"
                  @select="handleAction($event, item._id)"
                />
              </TableItem>
            </TableRow>
          </TableSortable>
        </LoadingWrapper>
      </Card>
    </div>
    <Modal v-if="showEditModal" @close="closeModal">
      <LoadingWrapper :is-loading="isLoading">
        <div class="flex flex-col p-6 space-y-6 w-[600px] max-h-[90vh] overflow-y-auto">
          <!-- Header -->
          <div class="flex items-center justify-between border-b border-gray-200 pb-4">
            <div>
              <h2 class="text-2xl font-bold text-gray-900">
                <template v-if="editId">
                  Edit Clothing Item
                </template>
                <template v-else>
                  Add New Clothing Item
                </template>
              </h2>
              <p class="text-sm text-gray-600 mt-1">
                <template v-if="editId">
                  Update the details of this clothing item
                </template>
                <template v-else>
                  Create a new clothing item for your collection
                </template>
              </p>
            </div>
          </div>
          <!-- Gender Selection -->
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-semibold text-gray-900 mb-2">Target Gender *</label>
              <p class="text-xs text-gray-600 mb-3">
                Select which gender(s) this clothing item is designed for
              </p>
            </div>
            <div class="grid grid-cols-3 gap-3">
              <label class="relative flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors" :class="{ 'border-blue-500 bg-blue-50': JSON.stringify(form.gender) === JSON.stringify(['male']) }">
                <input
                  v-model="form.gender"
                  type="radio"
                  :value="['male']"
                  class="sr-only"
                >
                <div class="text-center">
                  <svg class="w-6 h-6 mx-auto mb-1 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6a2 2 0 114 0v1H8V6z" />
                  </svg>
                  <span class="text-sm font-medium">Male</span>
                </div>
              </label>
              <label class="relative flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors" :class="{ 'border-blue-500 bg-blue-50': JSON.stringify(form.gender) === JSON.stringify(['female']) }">
                <input
                  v-model="form.gender"
                  type="radio"
                  :value="['female']"
                  class="sr-only"
                >
                <div class="text-center">
                  <svg class="w-6 h-6 mx-auto mb-1 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6a2 2 0 114 0v1H8V6z" />
                  </svg>
                  <span class="text-sm font-medium">Female</span>
                </div>
              </label>
              <label class="relative flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors" :class="{ 'border-blue-500 bg-blue-50': JSON.stringify(form.gender) === JSON.stringify(['male', 'female']) }">
                <input
                  v-model="form.gender"
                  type="radio"
                  :value="['male', 'female']"
                  class="sr-only"
                >
                <div class="text-center">
                  <svg class="w-6 h-6 mx-auto mb-1 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-sm font-medium">Both</span>
                </div>
              </label>
            </div>
          </div>

          <!-- Mode Selection -->
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-semibold text-gray-900 mb-2">Input Method</label>
              <p class="text-xs text-gray-600 mb-3">
                Choose how you want to create this clothing item
              </p>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors" :class="{ 'border-blue-500 bg-blue-50': inputMode === 'manual' }">
                <input
                  v-model="inputMode"
                  type="radio"
                  value="manual"
                  class="sr-only"
                  @change="onInputModeChange"
                >
                <div class="flex items-center">
                  <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <div>
                    <span class="text-sm font-medium text-gray-900">Manual Entry</span>
                    <p class="text-xs text-gray-600">Type details manually</p>
                  </div>
                </div>
              </label>
              <label class="relative flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 transition-colors" :class="{ 'border-blue-500 bg-blue-50': inputMode === 'image' }">
                <input
                  v-model="inputMode"
                  type="radio"
                  value="image"
                  class="sr-only"
                  @change="onInputModeChange"
                >
                <div class="flex items-center">
                  <svg class="w-6 h-6 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <div>
                    <span class="text-sm font-medium text-gray-900">Upload Image</span>
                    <p class="text-xs text-gray-600">AI will analyze the image</p>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- Manual Entry Fields -->
          <div v-if="inputMode === 'manual'" class="space-y-4 bg-gray-50 p-4 rounded-lg border">
            <div class="flex items-center mb-3">
              <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <h3 class="text-sm font-semibold text-gray-900">
                Manual Entry Details
              </h3>
            </div>
            <div class="space-y-3">
              <Input v-model="form.type" label="Clothing Type" placeholder="e.g., t-shirt, jacket, dress, jeans" required />
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Description</label>
                <textarea
                  v-model="form.prompt"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                  rows="3"
                  placeholder="Describe the clothing item in detail - style, color, material, fit, etc."
                  required
                />
                <p class="text-xs text-gray-500">
                  Be as descriptive as possible to help generate accurate images
                </p>
              </div>
            </div>
          </div>

          <!-- Image Upload Field -->
          <div v-if="inputMode === 'image'" class="space-y-4 bg-gray-50 p-4 rounded-lg border">
            <div class="flex items-center mb-3">
              <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <h3 class="text-sm font-semibold text-gray-900">
                Image Upload
              </h3>
            </div>

            <div class="space-y-3">
              <div class="text-sm text-gray-600">
                <p class="mb-2">
                  Upload an image to automatically generate type and description using AI analysis.
                </p>
                <div class="flex items-center space-x-4 text-xs text-gray-500">
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    Max: 5MB
                  </span>
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                    </svg>
                    JPEG, PNG, JPG
                  </span>
                </div>
              </div>

              <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                <input
                  id="fileextra"
                  ref="fileextra"
                  name="fileextra"
                  type="file"
                  accept="image/png, image/jpeg, image/jpg"
                  enctype="multipart/form-data"
                  class="hidden"
                  required
                  @change="onFileSelected"
                >
                <label for="fileextra" class="cursor-pointer">
                  <div v-if="!form.image || form.image.length === 0">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="mt-4">
                      <span class="mt-2 block text-sm font-medium text-gray-900">Click to upload an image</span>
                      <span class="mt-1 block text-xs text-gray-500">or drag and drop</span>
                    </div>
                  </div>
                  <div v-else class="text-sm">
                    <div class="flex items-center justify-center text-green-600 mb-2">
                      <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span class="font-medium">Image Selected</span>
                    </div>
                    <p class="text-gray-900 font-medium">{{ form.image[0].name }}</p>
                    <p class="text-gray-500 text-xs mt-1">{{ (form.image[0].size / 1024 / 1024).toFixed(2) }} MB</p>
                    <button type="button" class="mt-2 text-xs text-blue-600 hover:text-blue-800" @click.prevent="form.image = null; $refs.fileextra.value = ''">Change image</button>
                  </div>
                </label>
              </div>
            </div>
          </div>
          <!-- Action Buttons -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <ButtonWhite :disabled="isLoading" @click="closeModal">
              Cancel
            </ButtonWhite>
            <ButtonPrimary :disabled="isLoading" class="min-w-[140px]" @click="editItem()">
              <template v-if="isLoading">
                <span class="inline-flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    />
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Processing...
                </span>
              </template>
              <template v-else-if="editId">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                </svg>
                Update Item
              </template>
              <template v-else>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Item
              </template>
            </ButtonPrimary>
          </div>
        </div>
      </LoadingWrapper>
    </Modal>
    <!-- Simple Image Modal -->
    <div
      v-if="enlargedImage"
      class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
      @click="enlargedImage = null"
    >
      <div class="relative max-w-4xl max-h-full p-4">
        <button
          class="absolute top-2 right-2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 z-10"
          @click="enlargedImage = null"
        >
          <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <img
          :src="enlargedImage"
          class="max-h-[90vh] max-w-full object-contain rounded-lg"
          @click.stop
        >
      </div>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      clothingItems: [],
      item: null,
      isLoading: true,
      showEditModal: false,
      editId: null,
      form: {},
      inputMode: 'image', // 'manual' or 'image'
      pollingItems: new Set(), // Track items being polled
      pollIntervals: new Map(), // Track polling intervals
      genderOptions: [
        { title: 'Male', value: ['male'] },
        { title: 'Female', value: ['female'] },
        { title: 'All', value: ['male', 'female'] }
      ],
      enlargedImage: null,
      tableHead: [
        { label: 'Status', key: 'status', sortable: true },
        { label: 'Recommended', key: 'recommendedInPopular', sortable: true },
        { label: 'Date', key: 'createdAt', sortable: true },
        { label: 'Image', key: 'image', sortable: false },
        { label: 'Title/Prompts', key: 'type', sortable: false },
        { label: 'Gender', key: 'gender', sortable: false },
        { label: 'Total/Favorite', key: 'usage', sortable: true },
        { label: 'Actions', key: 'actions', sortable: false }
      ],
      sortBy: null,
      sortDirection: 'asc',
      minFavorites: 0,
      filter: {
        type: ''
      }
    }
  },
  computed: {
    sortedClothingItems () {
      const { sortBy, sortDirection } = this
      return [...this.clothingItems].filter((item) => {
        if (this?.filter?.type) {
          return (item?.type?.toLowerCase()?.includes(this.filter.type?.toLowerCase()) || item?.prompt?.toLowerCase()?.includes(this.filter.type?.toLowerCase()))
        }
        return true
      }).sort((a, b) => {
        if (sortBy === 'status') {
          return sortDirection === 'desc' ? b.active - a.active : a.active - b.active
        }
        if (sortBy === 'recommendedInPopular') {
          return sortDirection === 'desc' ? b.recommendedInPopular - a.recommendedInPopular : a.recommendedInPopular - b.recommendedInPopular
        }
        if (sortBy === 'usage') {
          const rateA = a.usage?.conversionRate || 0
          const rateB = b.usage?.conversionRate || 0
          return sortDirection === 'desc' ? rateB - rateA : rateA - rateB
        }
        if (sortBy === 'createdAt') {
          return sortDirection === 'desc' ? new Date(b.createdAt) - new Date(a.createdAt) : new Date(a.createdAt) - new Date(b.createdAt)
        }
        if (sortBy === 'type') {
          return sortDirection === 'desc' ? b.type.localeCompare(a.type) : a.type.localeCompare(b.type)
        }

        return 0
      })
    }
  },

  mounted () {
    this.fetch()
    // Add keyboard listener for image modal
    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy () {
    // Clean up all polling intervals
    this.pollIntervals.forEach((interval) => {
      clearInterval(interval)
    })
    this.pollIntervals.clear()
    this.pollingItems.clear()
    // Remove keyboard listener
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    filterClothing (event) {
      this.filter.type = event
    },
    toggleRecommendedInPopular (id) {
      this.$axios.$post(`/admin/clothing/${id}/recommend`)
        .then(() => {
          this.clothingItems = this.clothingItems.map((item) => {
            if (item._id === id) {
              item.recommendedInPopular = !item.recommendedInPopular
            }
            return item
          })
          this.$toast.success('Recommendation status updated successfully')
        })
        .catch((err) => {
          console.error('Toggle recommendation error:', err)
          let errorMessage = 'Failed to update recommendation status'
          if (err.response?.data?.errorMessage) {
            errorMessage = err.response.data.errorMessage
          }
          this.$toast.error(errorMessage)
        })
    },
    handleSort (event) {
      this.sortBy = event.column
      this.sortDirection = event.direction
    },
    openAddNewModal () {
      this.resetForm()
      this.editId = null
      this.showEditModal = true
    },
    resetForm () {
      this.form = {
        gender: [],
        image: null,
        _id: null,
        type: '',
        prompt: '',
        images: {
          male: null,
          female: null
        }
      }
      this.inputMode = 'image' // Reset to image mode
      // Clear file input
      if (this.$refs.fileextra) {
        this.$refs.fileextra.value = ''
      }
    },
    closeModal () {
      this.showEditModal = false
      this.editId = null
      this.resetForm()
    },
    onInputModeChange () {
      // Clear form data when switching modes
      this.form.type = ''
      this.form.prompt = ''
      this.form.image = null

      // Clear file input
      if (this.$refs.fileextra) {
        this.$refs.fileextra.value = ''
      }
    },
    onFileSelected (event) {
      const files = event.target.files
      if (files && files.length > 0) {
        const file = files[0]

        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
          this.$toast.error('Image file must be smaller than 5MB')
          event.target.value = ''
          return
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
        if (!allowedTypes.includes(file.type)) {
          this.$toast.error('Only JPEG, PNG, and JPG image formats are allowed')
          event.target.value = ''
          return
        }

        this.form.image = files
      }
    },
    onFileSelectedMale (event) {
      this.form.images.male = event.target.files
    },
    onFileSelectedFemale (event) {
      this.form.images.female = event.target.files
    },
    editItem () {
      this.isLoading = true
      const { _id, gender, image, prompt, type } = this.form
      const formData = new FormData()

      // Enhanced validation
      if (!gender || gender.length === 0) {
        this.$toast.error('You must select a gender')
        this.isLoading = false
        return
      }

      // Validate based on input mode
      if (this.inputMode === 'manual') {
        if (!type) {
          this.$toast.error('You must provide a clothing type')
          this.isLoading = false
          return
        }
        if (!prompt) {
          this.$toast.error('You must provide a clothing description')
          this.isLoading = false
          return
        }
      } else if (this.inputMode === 'image') {
        if (!image || image.length === 0) {
          this.$toast.error('You must upload an image')
          this.isLoading = false
          return
        }
      }

      if (gender) {
        formData.append('gender', gender)
      }

      // Append fields based on input mode
      if (this.inputMode === 'image') {
        if (image && image.length > 0) {
          formData.append('images', image[0])
        }
      } else if (this.inputMode === 'manual') {
        if (prompt) {
          formData.append('prompt', prompt)
        }
        if (type) {
          formData.append('type', type)
        }
      }

      const handleSuccess = (response) => {
        if (response && response.success) {
          if (!_id && response.data?.clothingId) {
            // New item created - start polling for progress
            this.$toast.success('Clothing item created! Processing in progress...')
            this.showEditModal = false
            this.resetForm()
            this.startPollingProgress(response.data.clothingId)
            // Add item to list immediately with pending status
            this.fetch()
          } else {
            // Updated existing item - check if processing is needed
            const needsProcessing = (image && image.length > 0) || (prompt && prompt !== this.clothingItems.find(item => item._id === _id)?.prompt)

            if (needsProcessing) {
              const message = 'Clothing item updated successfully. Processing will complete in about 1-2 minutes.'
              this.$toast.success(message)
            } else {
              this.$toast.success('Clothing item updated successfully!')
            }

            this.isLoading = false
            this.showEditModal = false
            this.resetForm()
            setTimeout(() => {
              this.fetch()
            }, 1000)
          }
        } else {
          this.$toast.error('Something went wrong')
          this.isLoading = false
        }
      }

      const handleError = (error) => {
        this.isLoading = false
        console.error('Clothing operation error:', error)

        // Extract meaningful error message
        let errorMessage = 'Something went wrong'
        if (error.response?.data?.errorMessage) {
          errorMessage = error.response.data.errorMessage
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }

        this.$toast.error(errorMessage)
      }

      if (_id) {
        formData.append('_id', _id)
        this.$axios.$put('/admin/clothing', formData)
          .then(handleSuccess)
          .catch(handleError)
      } else {
        this.$axios.$post('/admin/clothing', formData)
          .then(handleSuccess)
          .catch(handleError)
      }
    },
    setupEditForm (id) {
      this.editId = id
      this.showEditModal = true
      const item = this.clothingItems.find(item => item._id === id)
      this.form = {
        type: item?.type || '',
        prompt: item?.prompt || '',
        _id: item._id,
        gender: item?.gender || [],
        image: null, // Always null for editing, no pre-existing file
        images: item?.images || {
          female: null,
          male: null
        }
      }
      // For existing items, always use manual mode since we have type/prompt data
      this.inputMode = 'manual'
    },
    setupDuplicateForm (id) {
      this.editId = null // Clear editId to ensure new item creation
      this.showEditModal = true
      const item = this.clothingItems.find(item => item._id === id)
      this.form = {
        type: item?.type || '',
        prompt: item?.prompt || '',
        _id: null, // Clear ID to create new item
        gender: item?.gender || [],
        image: null, // Always null for new items
        images: {
          female: null,
          male: null
        }
      }
      // Use manual mode since we have the data pre-populated
      this.inputMode = 'manual'
    },
    async fetch () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/clothing')
        if (!success) {
          throw new Error(errorMessage)
        }
        this.clothingItems = data
      } catch (err) {
        console.error('Fetch clothing error:', err)
        let errorMessage = 'Failed to load clothing items'
        if (err.response?.data?.errorMessage) {
          errorMessage = err.response.data.errorMessage
        } else if (err.message) {
          errorMessage = err.message
        }
        this.$toast.error(errorMessage)
      } finally {
        this.isLoading = false
      }
    },
    async addItem () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$post('/admin/clothing', { type: this.item })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.clothingItems.push(data)
        this.item = null
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async deleteItem (id) {
      if (!confirm('Are you sure you want to delete this clothing item? This action cannot be undone.')) {
        return
      }

      try {
        const { success, errorMessage } = await this.$axios.$delete(`/admin/clothing/${id}`)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.clothingItems = this.clothingItems.filter(item => item._id !== id)
        this.$toast.success('Clothing item deleted successfully')
      } catch (err) {
        console.error('Delete clothing error:', err)
        let errorMessage = 'Failed to delete clothing item'
        if (err.response?.data?.errorMessage) {
          errorMessage = err.response.data.errorMessage
        }
        this.$toast.error(errorMessage)
      }
    },
    async toggleStatus (id) {
      try {
        const { success, errorMessage } = await this.$axios.$post(`/admin/clothing/${id}/status`)
        if (!success) {
          throw new Error(errorMessage)
        }
        const item = this.clothingItems.find(item => item._id === id)
        const newStatus = !item.active
        item.active = newStatus
        this.$toast.success(`Clothing item ${newStatus ? 'activated' : 'deactivated'} successfully`)
      } catch (err) {
        console.error('Toggle status error:', err)
        let errorMessage = 'Failed to update clothing item status'
        if (err.response?.data?.errorMessage) {
          errorMessage = err.response.data.errorMessage
        }
        this.$toast.error(errorMessage)
      }
    },
    showEnlargedImage (imageUrl) {
      this.enlargedImage = imageUrl
    },
    startPollingProgress (clothingId) {
      if (this.pollingItems.has(clothingId)) {
        return // Already polling this item
      }

      this.pollingItems.add(clothingId)
      this.isLoading = false

      const pollInterval = setInterval(async () => {
        try {
          const { success, data } = await this.$axios.$get(`/admin/clothing/${clothingId}/status`)

          if (success && data) {
            // Update the item in the list
            const itemIndex = this.clothingItems.findIndex(item => item._id === clothingId)
            if (itemIndex !== -1) {
              this.clothingItems.splice(itemIndex, 1, {
                ...this.clothingItems[itemIndex],
                processingStatus: data.processingStatus,
                active: data.active,
                type: data.type || this.clothingItems[itemIndex].type,
                prompt: data.prompt || this.clothingItems[itemIndex].prompt,
                images: data.images || this.clothingItems[itemIndex].images
              })
            }

            // Check if processing is complete
            if (data.processingStatus === 'completed') {
              this.stopPolling(clothingId)
              this.$toast.success('Clothing item processing completed!')
              this.fetch() // Refresh the full list
            } else if (data.processingStatus === 'failed') {
              this.stopPolling(clothingId)
              this.$toast.error('Clothing item processing failed and has been removed')
              this.fetch() // Refresh to remove the failed item
            }
          }
        } catch (error) {
          console.error('Polling error:', error)
          // Continue polling on error, but stop after too many failures
        }
      }, 2000) // Poll every 2 seconds

      this.pollIntervals.set(clothingId, pollInterval)

      // Auto-stop polling after 5 minutes to prevent infinite polling
      setTimeout(() => {
        if (this.pollingItems.has(clothingId)) {
          this.stopPolling(clothingId)
          this.$toast.warning('Stopped polling for clothing item progress after 5 minutes')
        }
      }, 300000) // 5 minutes
    },
    stopPolling (clothingId) {
      if (this.pollIntervals.has(clothingId)) {
        clearInterval(this.pollIntervals.get(clothingId))
        this.pollIntervals.delete(clothingId)
      }
      this.pollingItems.delete(clothingId)
    },

    // Enhanced date formatting methods
    formatDateRelative (dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diffInSeconds = Math.floor((now - date) / 1000)

      if (diffInSeconds < 60) {
        return 'Just now'
      } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes} min${minutes === 1 ? '' : 's'} ago`
      } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours} hour${hours === 1 ? '' : 's'} ago`
      } else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400)
        return `${days} day${days === 1 ? '' : 's'} ago`
      } else {
        return this.formatDateShort(dateString)
      }
    },

    formatDateShort (dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
      })
    },

    formatDateFull (dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // Keyboard handler for image modal
    handleKeydown (event) {
      if (event.key === 'Escape' && this.enlargedImage) {
        this.enlargedImage = null
      }
    },

    // Generate action items for dropdown
    getActionItems (item) {
      return [
        {
          title: 'Edit',
          value: 'edit',
          icon: 'EditIcon',
          color: '#6B7280'
        },
        {
          title: 'Duplicate',
          value: 'duplicate',
          icon: 'DuplicateIcon',
          color: '#8B5CF6'
        },
        {
          title: item.active ? 'Deactivate' : 'Activate',
          value: 'toggle-status',
          icon: item.active ? 'EyeSlashIcon' : 'EyeIcon',
          color: item.active ? '#F59E0B' : '#10B981'
        },
        {
          title: 'Delete',
          value: 'delete',
          icon: 'TrashIcon',
          color: '#EF4444'
        }
      ]
    },

    // Handle dropdown action selection
    handleAction (action, itemId) {
      switch (action) {
        case 'edit':
          this.setupEditForm(itemId)
          break
        case 'duplicate':
          this.setupDuplicateForm(itemId)
          break
        case 'toggle-status':
          this.toggleStatus(itemId)
          break
        case 'delete':
          this.deleteItem(itemId)
          break
      }
    }
  }
}
</script>

<style></style>
