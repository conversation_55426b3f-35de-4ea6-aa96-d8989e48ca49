<template>
  <div class="flex flex-col space-y-4">
    <div class="h-[40px] w-full bg-white border-b border-black/10 px-4">
      <div class="h-full flex items-center space-x-4">
        <h1 class="text-lg font-medium">
          Playground
        </h1>
        <!-- <NavigationTabs :items="tabs" :initial-active="activeTab" @click="handleTabClick($event)" /> -->
      </div>
    </div>
    <div class="h-[calc(100vh-40px)] w-full px-4">
      <div class="flex w-full gap-8">
        <div class="w-[400px] bg-white border rounded-md shadow-box p-4 border-black/10">
          <div class="flex flex-col space-y-2">
            <!-- <h2 class="text-sm font-medium">
              {{ tabs.find(tab => tab.value === activeTab)?.label }}
            </h2> -->
            <InputSelect v-model="selectedModelLora" :options="modelLoras" label="Model" />
            <InputTextArea v-model="prompt" label="Prompt" />
            <ul class="flex space-x-3 text-xs">
              <template v-for="shortcode in shortcodes">
                <li :key="shortcode.key">
                  <kbd
                    class="px-1.5 py-0.5 bg-gray-100 rounded cursor-pointer hover:bg-gray-200"
                    @click="copyToClipboard(shortcode.key)"
                  >{{ shortcode.key }}</kbd>
                </li>
              </template>
            </ul>
            <Input v-model="form.clothing" label="Clothing">
              <template #action>
                <button @click="showVisionModal = true; visionMode = 'clothing'">
                  <IconSolidEye class="w-4 h-4 cursor-pointer text-gray-500 hover:text-sky-500" />
                </button>
              </template>
            </Input>
            <Input v-model="form.location" label="Location">
              <template #action>
                <button @click="showVisionModal = true; visionMode = 'style'">
                  <IconSolidEye class="w-4 h-4 cursor-pointer text-gray-500 hover:text-sky-500" />
                </button>
              </template>
            </Input>
            <Input v-model="form.emotion" label="Emotion" />
            <Input v-model="form.seed" label="Seed" />
            <InputSelect v-model="form.imageSize" :options="imageSizes" label="Image size" />
            <ButtonPrimary size="sm" :disabled="isSubmitting" @click="generateImage">
              <LoadingSpinner v-if="isSubmitting" />
              <span v-else>Generate</span>
            </ButtonPrimary>
          </div>
        </div>
        <div class="w-full bg-black/5 shadow-inner p-4 rounded-md min-h-[calc(100vh-70px)] max-h-[calc(100vh-70px)] overflow-y-auto">
          <div class="grid grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4">
            <div v-for="prediction in predictionIds" :key="prediction" class="bg-black/10 rounded-md p-4 flex items-center justify-center">
              <LoadingSpinner />
            </div>
            <template v-for="image in images">
              <div :key="image.url" class="group relative">
                <ImageDns :src="image.url" class="w-full h-full object-cover" />
                <div class="absolute top-0 left-0 w-full p-4 h-full bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                  <ul class="flex flex-col space-y-1">
                    <li v-for="key in Object.keys(image.metadata)" :key="key" class="text-white text-xs">
                      {{ key }}: {{ image.metadata[key] }}
                    </li>
                  </ul>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <Popup v-if="showVisionModal" size="lg" @closeModal="showVisionModal = false; visionPrompt = null">
      <LoadingWrapper :is-loading="isGeneratingVision">
        <div class="flex flex-col space-y-4">
          <template v-if="visionPrompt">
            <div class="text-sm bg-gray-100 p-4 rounded-md">
              {{ visionPrompt }}
            </div>
            <ButtonWhite size="xs" @click="visionPrompt = null">
              Retry
            </ButtonWhite>
          </template>
          <template v-else>
            <h2 class="text-lg font-medium">
              Prompt from {{ visionMode }}
            </h2>
            <input
              id="fileextra"
              ref="fileextra"
              name="fileextra"
              type="file"
              accept="image/png, image/jpeg, image/jpg"
              enctype="multipart/form-data"
              @change="onFileSelected"
            >
            <ButtonPrimary size="sm" @click="generatePromptUsingVision">
              Generate prompt
            </ButtonPrimary>
          </template>
        </div>
      </LoadingWrapper>
    </Popup>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      tabs: [
        { label: 'Tab 1', value: 'tab1' },
        { label: 'Tab 2', value: 'tab2' },
        { label: 'Tab 3', value: 'tab3' }
      ],
      activeTab: 'tab1',
      modelLoras: [
        { title: 'female', value: 'https://storage.googleapis.com/fal-flux-lora/cf537935d9184335b22a377b75859273_lora.safetensors' },
        { title: 'male', value: 'https://storage.googleapis.com/fal-flux-lora/ff102667a24046419602679f50ff270e_lora.safetensors' }
      ],
      selectedModelLora: 'https://storage.googleapis.com/fal-flux-lora/cf537935d9184335b22a377b75859273_lora.safetensors',
      prompt: 'A business portait photo of sks {GENDER} wearing a {CLOTHING}, {LOCATION}, {EMOTION}, medium shot. Bright light, bokeh background. Photo could be used for professional purposes like LinkedIn.',
      form: {
        clothing: 'a white buttoned shirt',
        location: 'in a conference room',
        emotion: 'smiling',
        imageSize: '1024x832'
      },
      shortcodes: [
        { key: '{GENDER}', label: 'Gender' },
        { key: '{CLOTHING}', label: 'Clothing' },
        { key: '{LOCATION}', label: 'Location' },
        { key: '{EMOTION}', label: 'Emotion' }
      ],
      predictionIds: ['4a9ee773-9adb-4cc9-bc2d-e8d4bcaaa6ba'],
      imageSizes: [
        { title: '1024x832', value: '1024x832' },
        { title: '1024x1024', value: '1024x1024' }
      ],
      pollingInterval: null,
      images: [],
      isSubmitting: false,
      showVisionModal: false,
      visionMode: 'style',
      file: null,
      isGeneratingVision: false,
      visionPrompt: null
    }
  },
  mounted () {
    this.startPolling()
    this.fetchPhotos()
  },
  beforeDestroy () {
    this.stopPolling() // Clean up polling when component is destroyed
  },
  methods: {
    onFileSelected (event) {
      this.file = event.target.files
    },
    async generatePromptUsingVision () {
      try {
        const { visionMode, file } = this
        if (!visionMode || !file) { return }
        const formData = new FormData()
        formData.append('file', file[0])
        formData.append('mode', visionMode)
        this.isGeneratingVision = true
        const response = await this.$axios.$post('/admin/playground/vision', formData)
        this.visionPrompt = response.prompt
        this.isGeneratingVision = false
      } catch (error) {
        this.isGeneratingVision = false
        this.$toast.error('Error generating prompt')
      }
    },
    fetchPhotos () {
      this.$axios.$get('/admin/playground').then((response) => {
        this.images = response.photos
        this.predictionIds = this.images.filter(image => image.status === 'pending').map(image => image._id)
        this.images = this.images.filter(image => image.status !== 'pending')
      })
    },
    handleTabClick (value) {
      this.activeTab = value
    },
    generateImage () {
      if (!this.selectedModelLora) { return this.$toast.error('Please select a model') }
      if (!this.form.clothing) { return this.$toast.error('Please enter a clothing') }
      if (!this.form.location) { return this.$toast.error('Please enter a location') }
      this.isSubmitting = true
      switch (this.activeTab) {
        case 'tab1':
          this.$axios.$post('/admin/playground/submit/' + this.activeTab, {
            metadata: this.form,
            prompt: this.getFinalPrompt(),
            lora: this.selectedModelLora,
            imageSize: this.form.imageSize
          }).then((response) => {
            if (response && response.success) {
              if (response.prediction) {
                this.predictionIds.push(response.prediction.request_id)
                this.startPolling()
              }
            }
          }).finally(() => {
            this.isSubmitting = false
          })
      }
    },
    getFinalPrompt () {
      let { prompt } = this
      const selectedGender = this.modelLoras?.find(lora => lora.value === this.selectedModelLora)?.title
      if (selectedGender) {
        prompt = prompt.replace('{GENDER}', selectedGender)
      }
      if (this.form.clothing) {
        prompt = prompt.replace('{CLOTHING}', this.form.clothing)
      }
      if (this.form.location) {
        prompt = prompt.replace('{LOCATION}', this.form.location)
      }
      prompt = prompt.replace(', {EMOTION}', this.form.emotion ? `, ${this.form.emotion}` : '')
      return prompt
    },
    copyToClipboard (text) {
      navigator.clipboard.writeText(text)
      this.$toast.success('Copied to clipboard')
    },
    startPolling () {
      if (this.pollingInterval) { return }

      this.pollingInterval = setInterval(async () => {
        if (this.predictionIds.length === 0) {
          this.stopPolling()
          return
        }

        try {
          const response = await this.$axios.$get('/admin/playground/status', {
            params: { ids: this.predictionIds }
          })

          if (response && response.results) {
            response.results.forEach((result) => {
              const { requestId, status } = result
              if (status === 'active') {
                this.predictionIds = this.predictionIds.filter(id => id !== requestId)
                this.images = [result, ...this.images]
              }
            })
          }
        } catch (error) {
          console.error('Polling error:', error)
          this.stopPolling()
        }
      }, 2000) // Poll every 2 seconds
    },
    stopPolling () {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }
    }
  }
}
</script>

<style>

</style>
