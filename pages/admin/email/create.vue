<template>
  <div class="p-8 space-y-4">
    <LoadingWrapper :is-loading="isLoading">
      <div class="flex w-full justify-between space-x-2">
        <h3 class="font-bold text-lg">
          New email
        </h3>
        <ButtonPrimary size="sm" @click="save">
          Save
        </ButtonPrimary>
      </div>
      <hr>
      <div class="grid grid-cols-2 gap-8 h-full pt-4">
        <div class="h-full space-y-2">
          <Input v-model="email.title" label="Title*" @input="debouncedUpdateContent" />
          <Input v-model="email.subject" label="Subject*" @input="debouncedUpdateContent" />
          <Input v-model="email.slug" label="Slug" description="Leave empty to auto generate based on subject" @input="debouncedUpdateContent" />
          <InputCheckbox id="transactional" v-model="email.transactional" label="Transactional email" />
          <InputTextArea label="Email body" :value="email.text" textarea-class="w-full h-[calc(100vh-400px-100px)]" @input="email.text = $event; debouncedUpdateContent($event)" />
        </div>
        <div class="h-full">
          <div class="w-full border border-gray-300 bg-white rounded-xl overflow-hidden divide-y divide-gray-200 h-full">
            <div class="px-4 py-2 text-sm">
              <p>
                To: <strong><EMAIL></strong>
              </p>
            </div>
            <div class="px-4 py-2 text-sm">
              <p>
                Subject: <strong>{{ email.subject }}</strong>
              </p>
            </div>
            <iframe :srcdoc="previewHtml" class="w-full h-full border rounded" />
          </div>
        </div>
      </div>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      email: {
        text: 'Text',
        subject: 'Subject',
        excerpt: 'Excerpt',
        title: 'Title',
        transactional: true,
        slug: 'slug'
      },
      previewHtml: '',
      debounceTimeout: null,
      isLoading: true
    }
  },
  mounted () {
    this.isLoading = true
    this.updateContent()
    if (this.$route.query.edit && this.$route.query.id) {
      this.$axios.$get(`/admin/email/single/${this.$route.query.id}`)
        .then(({ email }) => {
          this.email = email
          this.updateContent()
          this.isLoading = false
        }).catch(() => {
          this.isLoading = false
        })
    } else {
      this.isLoading = false
    }
  },
  methods: {
    async save () {
      try {
        const { text, subject, title, transactional, slug } = this.email
        if (!text || !subject || !title) {
          this.$toast.open({
            title: 'Error',
            message: 'Please fill in all fields',
            type: 'error'
          })
          return false
        }

        this.isLoading = true

        if (this.$route.query.id && this.$route.query.edit) {
          // Edit
          const { success, message } = await this.$axios.$put(`/admin/email/update/${this.$route.query.id}`, {
            text, subject, title, transactional, slug
          })

          if (success) {
            this.$toast.open({ title: 'Success', message, type: 'success' })
            this.$router.push('/admin/email')
          } else {
            this.$toast.open({ title: 'Error', message, type: 'error' })
          }
        } else {
          // Save new
          const { success, message } = await this.$axios.$post('/admin/email/new', {
            text, subject, title, transactional, slug
          })

          if (success) {
            this.$toast.open({ title: 'Success', message, type: 'success' })
            this.$router.push('/admin/email')
          } else {
            this.$toast.open({ title: 'Error', message, type: 'error' })
          }
        }
        this.isLoading = false
      } catch (err) {
        this.$toast.open({ title: 'Error', message: 'An error occurred', type: 'error' })
        this.isLoading = false
      }
    },
    async updateContent () {
      const { html } = await this.$axios.$post('/admin/email/test/editor', {
        ...this.email
      })
      this.previewHtml = html
    },
    debouncedUpdateContent (event) {
      clearTimeout(this.debounceTimeout)
      this.debounceTimeout = setTimeout(() => {
        this.updateContent()
      }, 1000)
    }
  }
}
</script>

<style>

</style>
