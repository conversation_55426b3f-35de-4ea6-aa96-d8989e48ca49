<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search by email, UID, or referrer UID" class="w-full" @handleEnter="fetchData" />
      <InputSelect v-model="searchBy" :options="['email', 'uid', 'referredBy', 'displayName']" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
    </div>

    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Referred Date', 'Referred User Email', 'Referred User UID', 'Referred By Email', 'Referred By UID', 'Transactions']">
          <template v-for="item in referrals">
            <TableRow :key="item._id">
              <TableItem>{{ formatDate(item.createdAt) }}</TableItem>
              <TableItem>
                <button
                  class="text-blue-600 hover:underline cursor-pointer"
                  @click="navigateToUserSearch(item.email, 'email')"
                >
                  {{ item.email }}
                </button>
              </TableItem>
              <TableItem>
                <button
                  class="text-blue-600 hover:underline cursor-pointer font-mono text-sm"
                  @click="navigateToUserSearch(item.uid, 'uid')"
                >
                  {{ stripLength(item.uid, 8) }}
                </button>
              </TableItem>
              <TableItem>
                <button
                  v-if="item.referrer"
                  class="text-green-600 hover:underline cursor-pointer"
                  @click="navigateToUserSearch(item.referrer.email, 'email')"
                >
                  {{ item.referrer.email }}
                </button>
                <span v-else class="text-gray-500 italic">Unknown</span>
              </TableItem>
              <TableItem>
                <button
                  v-if="item.referrer"
                  class="text-green-600 hover:underline cursor-pointer font-mono text-sm"
                  @click="navigateToUserSearch(item.referrer.uid, 'uid')"
                >
                  {{ stripLength(item.referrer.uid, 8) }}
                </button>
                <span v-else class="text-gray-500 italic">Unknown</span>
              </TableItem>
              <TableItem class="flex justify-end">
                <ButtonWhite
                  size="xs"
                  @click="navigateToTransactions(item.uid)"
                >
                  View Transactions
                </ButtonWhite>
              </TableItem>
            </TableRow>
          </template>
        </Table>
        <div v-if="referrals.length === 0 && !isLoading" class="text-center py-8 text-gray-500">
          No referral data found
        </div>
      </LoadingWrapper>
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      referrals: [],
      isLoading: true,
      search: null,
      searchBy: 'email'
    }
  },

  async created () {
    // Handle URL parameters for search
    if (this.$route.query.search) {
      this.search = this.$route.query.search
    }
    if (this.$route.query.searchBy) {
      this.searchBy = this.$route.query.searchBy
    }

    await this.fetchData()
    this.isLoading = false
  },

  methods: {
    async fetchData () {
      this.isLoading = true

      // Update URL with search parameters
      const query = { ...this.$route.query }
      if (this.search && this.search.length > 0) {
        query.search = this.search.trim()
        query.searchBy = this.searchBy.trim()
      } else {
        delete query.search
        delete query.searchBy
      }

      // Update the URL without page reload
      this.$router.replace({ query })

      try {
        this.referrals = await this.$axios.$get('/admin/referral', {
          params: {
            ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
            ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
          }
        })
      } catch (error) {
        console.error('Error fetching referral data:', error)
        this.$toast.error('Failed to fetch referral data')
        this.referrals = []
      }

      this.isLoading = false
    },

    navigateToUserSearch (searchTerm, searchBy) {
      // Navigate to user search page with the filter applied
      this.$router.push({
        path: '/admin/user',
        query: {
          search: searchTerm,
          searchBy
        }
      })
    },

    navigateToTransactions (uid) {
      // Navigate to transactions page filtered by user UID
      this.$router.push({
        path: '/admin/transactions',
        query: {
          search: uid,
          searchBy: 'uid'
        }
      })
    }
  }
}
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
</style>
