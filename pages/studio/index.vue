<template>
  <div>
    &nbsp;
  </div>
</template>

<script>
export default {
  layout: 'protected',
  async mounted () {
    this.$loading.show({
      text: 'Loading studio...'
    })
    await this.$axios.$get('/studio/model/lastest')
      .then((res) => {
        console.log(res)
        this.$loading.hide()
        this.$router.push(`/studio/${res.data._id}`)
      })
      .catch((err) => {
        console.log(err)
        this.$loading.hide()
        this.$router.push('/app')
      })
  }

}
</script>

<style>

</style>
