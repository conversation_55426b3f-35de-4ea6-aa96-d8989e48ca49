<?php

namespace App\Podcast;

use App\Concerns;
use App\Model;
use App\School;
use App\Support\Screenshot;
use App\Tag;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class Episode extends Model
{
    use Concerns\HasSearch;

    protected $fillable = [
        'id',
        'title',
        'slug',
        'description',
        'length',
        'audio_url',
        'image_url',
        'captivate_id',
        'spotify_id',
        'created_at',
    ];

    protected $appends = [
        'date',
        'excerpt',
        'media_url',
        'player_url',
        'url',
    ];

    protected $searchFields = [
        'id',
        'title',
        'guest',
        'description',
        'hosts.name',
        'hosts.affiliation',
        'pathways.label',
        'pathways.slug',
        'pathways.description',
        'sponsors.name',
        'tags.label',
        'tags.slug',
        'tags.description',
    ];

    protected $searchJoins = [
        ['podcast_pathway', 'podcast_pathway.podcast_id', '=', 'podcasts.id'],
        ['pathways', 'podcast_pathway.pathway_id', '=', 'pathways.id'],
        ['podcast_host', 'podcast_host.podcast_id', '=', 'podcasts.id'],
        ['hosts', 'podcast_host.host_id', '=', 'hosts.id'],
        ['podcast_sponsor', 'podcast_sponsor.podcast_id', '=', 'podcasts.id'],
        ['sponsors', 'podcast_sponsor.sponsor_id', '=', 'sponsors.id'],
        ['podcast_tag', 'podcast_tag.podcast_id', '=', 'podcasts.id'],
        ['tags', 'podcast_tag.tag_id', '=', 'tags.id'],
    ];

    protected $table = 'podcasts';

    protected $with = [
        'hosts',
    ];

    protected $visible = [
        'id',
        'title',
        'guest',
        'length',
        'audio_url',
        'image_url',
        'media_url',
        'player_url',
        'description',
        'excerpt',
        'date',
        'pathways',
        'tags',
        'transcript',
        'url',
        'created_at',
    ];

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope(function ($builder) {
            $builder->orderBy('created_at', 'desc');
        });

        static::deleting(function ($podcast) {
            $podcast->hosts()->detach();
            $podcast->pathways()->detach();
            $podcast->sponsors()->detach();
            $podcast->tags()->detach();
        });
    }

    public function headline(): Attribute
    {
        return Attribute::make(function () {
            $name = "I Am The Law Episode #{$this->id}: {$this->title}";

            if ($this->subtitle) {
                $name .= " {$this->subtitle}";
            }

            return $name;
        });
    }

    public function subtitle(): Attribute
    {
        return Attribute::make(function () {
            return implode(' ', array_filter([
                $this->host || $this->guest ? 'w/' : null,
                Arr::join(array_filter([$this->host, ...explode(', ', $this->guest)]), ', ', ' and '),
            ]));
        });
    }

    public function date(): Attribute
    {
        return Attribute::make(fn () => $this->created_at->format('M j, Y'));
    }

    public function excerpt(): Attribute
    {
        return Attribute::make(fn () => explode("\n\n", Str::html2text($this->description))[0]);
    }

    public function host(): Attribute
    {
        return Attribute::make(fn () => $this->hosts->pluck('name')->join(', '));
    }

    public function hosts()
    {
        return $this->belongsToMany(Host::class, 'podcast_host', 'podcast_id');
    }

    public function filename(): Attribute
    {
        return Attribute::make(fn () => basename($this->audio_url));
    }

    public function pathways()
    {
        return $this->belongsToMany(Pathway::class, 'podcast_pathway', 'podcast_id');
    }

    public function mediaType(): Attribute
    {
        return Attribute::make(fn () => 'audio/mpeg');
    }

    public function mediaUrl(): Attribute
    {
        return Attribute::make(fn () => (
            $this->captivate_id
            ? "https://chrt.fm/track/3FA2DC/podcasts.captivate.fm/media/{$this->captivate_id}/{$this->filename}"
            : null
        ));
    }

    public function playerWidth(): Attribute
    {
        return Attribute::make(fn () => 540);
    }

    public function playerHeight(): Attribute
    {
        return Attribute::make(fn () => 300);
    }

    public function playerUrl(): Attribute
    {
        return Attribute::make(fn () => (
            $this->captivate_id
            ? route('podcasts.iatl.player', [$this, $this->slug])
            : null
        ));
    }

    public function previous(): Attribute
    {
        return Attribute::make(fn () => Episode::firstWhere('id', $this->id - 1));
    }

    public function next(): Attribute
    {
        return Attribute::make(fn () => Episode::firstWhere('id', $this->id + 1));
    }

    public function school()
    {
        return $this->belongsTo(School::class)->withTrashed();
    }

    public function sponsors()
    {
        return $this->belongsToMany(Sponsor::class, 'podcast_sponsor', 'podcast_id')->withTrashed();
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'podcast_tag', 'podcast_id');
    }

    public function thumbnail(): Attribute
    {
        return Attribute::make(fn () => (
            Screenshot::url($this->player_url, width: $this->player_width, height: $this->player_height, scale: 2, version: $this->updated_at)
        ));
    }

    public function url(): Attribute
    {
        return Attribute::make(fn () => route('podcasts.iatl.show', [$this->id, $this->slug]));
    }
}
