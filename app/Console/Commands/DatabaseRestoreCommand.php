<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Console\ConfirmableTrait;
use Illuminate\Support\Facades\Process;

class DatabaseRestoreCommand extends Command
{
    use ConfirmableTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:restore {--force : Force the operation to run when in production}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Restore your last capture of the production database';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (!$this->confirmToProceed()) {
            return Command::FAILURE;
        }

        $db = (object) config('database.connections.' . config('database.default'));

        $load_command = "mysql -h {$db->host} -u {$db->username}" . ($db->password ? " -p{$db->password}" : '') . " {$db->database}";

        Process::run("cat latest.dump | {$load_command}");

        $this->info('Database restored');

        $this->newLine();

        $this->call('migrate', ['--force' => true]);

        return Command::SUCCESS;
    }
}
