<?php

namespace App;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Request;

class State extends Model
{
    use Concerns\HasCache;
    use Concerns\HasSearch;

    public $incrementing = false;

    public $timestamps = false;

    protected $primaryKey = 'code';

    protected $appends = [
        'image',
        'is_preferred',
    ];

    protected $casts = [
        'employment_score' => 'float',
        'underemployment_score' => 'float',
    ];

    protected $hidden = [
        'pivot',
    ];

    protected $searchFields = [
        'name',
        'code',
    ];

    public function __toString()
    {
        return $this->name;
    }

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope(function ($builder) {
            $builder->select('states.*', 'state_reports.*')
                ->leftJoin('state_reports', 'state_reports.state_code', '=', 'states.code')
                ->orderBy('name', 'asc');
        });
    }

    public function borders()
    {
        return $this->belongsToMany(self::class, 'state_borders', 'state_code', 'border_code');
    }

    public function description(): Attribute
    {
        return Attribute::make(fn () => "Discover U.S. law schools with successful job placements in {$this->name}. Compare job outcomes, cost, diversity, admissions, salary, and debt statistics.");
    }

    public function image(): Attribute
    {
        return Attribute::make(fn () => asset("images/states/{$this->code}.jpg"));
    }

    public function thumbnail(): Attribute
    {
        return Attribute::make(fn () => $this->image);
    }

    public function isPreferred(): Attribute
    {
        return Attribute::make(fn () => (bool) Request::user()?->statesPreferred->contains($this));
    }

    public function schools()
    {
        return $this->belongsToMany(School::class, 'school_states')
            ->where('percent', '>=', nova_get_setting('state_min_percent'))
            ->orWhere(fn ($query) => $query->where('schools.state_code', $this->code)->whereNull('percent'))
            ->orderByDesc('percent')
            ->withPivot('emp', 'graduation_year', 'percent');
    }

    public function url(): Attribute
    {
        return Attribute::make(fn () => route('states.show', $this));
    }
}
