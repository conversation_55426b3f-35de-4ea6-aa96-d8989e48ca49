<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\EloquentSortable\Sortable;

class Guide extends Model implements Sortable
{
    use Concerns\HasSearch;
    use Concerns\HasSort;
    use SoftDeletes;

    public $timestamps = false;

    protected $casts = [
        'published_at' => 'datetime',
    ];

    protected $searchFields = [
        'title',
        'slug',
        'group',
        'excerpt',
        'body',
    ];

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope(function ($builder) {
            $builder->orderBy('order', 'asc');
        });
    }

    public function scopePublished(Builder $query): void
    {
        $query->where('published_at', '<=', now());
    }

    public function url(): Attribute
    {
        return Attribute::make(fn () => route('guides.show', $this));
    }
}
