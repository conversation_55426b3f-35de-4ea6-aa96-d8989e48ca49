<?php

namespace App\Support;

use App\Enums\Dataset;
use App\Enums\JobType;
use App\School;
use App\User;
use Illuminate\Support\Arr;

class JobScore
{
    const MIN = 5;

    const MAX = 95;

    private static $cache = [];

    public static function calculate(School $school, ?User $user): ?string
    {
        if (!$user || !$user->report->locations || !$user->report->priorities) {
            return null;
        }

        $scores = static::$cache[$user->id] ??= Arr::mapWithKeys(Datasets::employmentAba(Dataset::EMPLOYMENT_ABA->latestYear())->all(), fn ($row) => [
            $row->school_id => self::score($row, $user),
        ]);

        if (!isset($scores[$school->id])) {
            return null; // @codeCoverageIgnore
        }

        return self::normalize($scores[$school->id], min($scores), max($scores));
    }

    private static function score(object $aba, User $user): float
    {
        $types = JobType::cases();

        $priorities = Arr::map($types, function ($type) use ($user) {
            return $type->priority($user)->value;
        });

        $scores = Arr::map($types, function ($type) use ($user, $aba) {
            $priority = $type->priority($user);

            $score = ($type->stat($aba) - $type->historicalAverage()) / $type->historicalStdDev();

            if ($score > 0) {
                return ($score > $priority->cap() ? $priority->cap() : $score) * $priority->value; // @codeCoverageIgnore
            }

            return ($score < ($priority->cap() * -1) ? ($priority->cap() * -1) : $score) * $priority->value;
        });

        return array_sum($scores) / array_sum($priorities);
    }

    private static function normalize($score, $low, $high): string
    {
        if ($high - $low == 0.0) {
            return '0.0';
        }

        return number_format((((self::MAX - self::MIN) * ($score - $low) / ($high - $low)) + self::MIN) / 10, 1); // @codeCoverageIgnore
    }
}
