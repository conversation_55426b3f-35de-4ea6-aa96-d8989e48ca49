<?php

namespace App\Support;

use Illuminate\Support\Str;

class Gravatar
{
    public const DEFAULT_SIZE = 300;

    public static function make(?string $email, ?string $default = 'mm', ?int $size = null): string
    {
        return Str::format('https://www.gravatar.com/avatar/{hash}.jpg?d={default}&r={rating}&s={size}', [
            'hash' => md5(Str::lower($email)),
            'default' => urlencode($default),
            'rating' => 'g',
            'size' => $size ?? static::DEFAULT_SIZE,
        ]);
    }
}
