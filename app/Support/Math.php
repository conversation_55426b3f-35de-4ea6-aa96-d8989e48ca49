<?php

namespace App\Support;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Request;

class Math
{
    public static function currency($number, $replace_zero = null)
    {
        if ($number) {
            return '$' . number_format(round($number, 0));
        }

        if ($replace_zero) {
            return $replace_zero;
        }
    }

    public static function currencyShort($number, $replace_zero = null)
    {
        if ($number >= 1000000000) {
            return '$' . round($number / 1000000000, 1) . ' billion';
        }

        if ($number >= 1000000) {
            return '$' . round($number / 1000000, 1) . ' million';
        }

        if ($number >= 1000) {
            return '$' . round($number / 1000, 1) . 'k';
        }

        if ($number) {
            return '$' . round($number);
        }

        return self::currency($number, $replace_zero);
    }

    public static function matrixTotal($item, $data)
    {
        return Arr::sumBy($data, "{$item}_LT_FT", "{$item}_LT_PT", "{$item}_ST_FT", "{$item}_ST_PT");
    }

    public static function monthlyPayment($balance, $int, $years = '10')
    {
        $result = 0;

        if ($balance > 0) {
            $result = $balance * ($int / 12) / (1 - pow((1 + ($int / 12)), -(12 * $years)));
        }

        return $result;
    }

    public static function percent($n, $d, $end = null, $max = null)
    {
        $result = 0;

        if ($d > 0) {
            $result = $n / $d * 100;

            if ($max && $result > $max) {
                $result = $max;
            }

            $result = round($result, 1);

            if ($end === '%') {
                $result .= $end;
            }
        }

        return $result;
    }

    public static function percentToggle($n, $d)
    {
        if (Request::input('percent')) {
            return self::percent($n, $d, '%');
        }

        return $n;
    }
}
