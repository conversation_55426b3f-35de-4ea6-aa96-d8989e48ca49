<?php

namespace App\Reports;

use Illuminate\Http\Request;

class PersonalReport extends Report
{
    public string $source = 'suggestions';

    public string $type = 'personal';

    public function setup(Request $request): void
    {
        if ($this->source == 'list') {
            $this->schools = $request->user()->schools;

            return;
        }

        if ($request->is('api/reports*') && !$this->user->report->is_complete) {
            abort(404, 'User does not have a personal report');
        }

        $this->wizard->step = isset($request) ? $request->route('page') : null;

        $this->schools = $this->wizard->schools;
    }

    public function progress()
    {
        $items = [
            (bool) $this->user->race,
            (bool) $this->wizard->lsat,
            (bool) $this->wizard->gpa,
            (bool) $this->wizard->locations,
            (bool) $this->wizard->target_debt_period,
            (bool) $this->wizard->priorities,
        ];

        return floor(array_sum($items) / count($items) * 100);
    }
}
