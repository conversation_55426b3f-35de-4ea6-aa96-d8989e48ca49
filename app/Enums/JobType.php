<?php

namespace App\Enums;

use App\User;

enum JobType: string
{
    case LAW = 'law';
    case FIRM = 'firm';
    case BIGLAW = 'biglaw';
    case PUBLIC = 'public';
    case CLERK = 'clerk';

    public function label(): string
    {
        return match ($this) {
            self::LAW => 'Any lawyer job',
            self::FIRM => 'Any law firm job',
            self::BIGLAW => 'Large firm',
            self::PUBLIC => 'Public service',
            self::CLERK => 'Clerkship',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::LAW => 'Lawyer jobs require you to pass the bar exam and practice law.',
            self::FIRM => 'Practice law in a private practice of any size.',
            self::BIGLAW => 'Practice law at the largest firms in the country.',
            self::PUBLIC => 'Work for the government or for a non-profit.',
            self::CLERK => 'Work for a judge in their chambers, usually for just one or two years.',
        };
    }

    // Generated from 2015-2020 graduating classes at all ABA-approved law schools among schools that were open in 2021
    public function historicalAverage(): float
    {
        return match ($this) {
            self::LAW => 0.643029627,
            self::FIRM => 0.448023568,
            self::BIGLAW => 0.138279087,
            self::PUBLIC => 0.160094938,
            self::CLERK => 0.090935519,
        };
    }

    // Generated from 2015-2020 graduating classes at all ABA-approved law schools among schools that were open in 2021
    public function historicalStdDev(): float
    {
        return match ($this) {
            self::LAW => 0.161280282,
            self::FIRM => 0.147333654,
            self::BIGLAW => 0.184804909,
            self::PUBLIC => 0.065201499,
            self::CLERK => 0.085236831,
        };
    }

    public function priority(User $user)
    {
        return match ($this) {
            self::LAW => Priority::from($user->report->priority_law ?? 0),
            self::FIRM => Priority::from($user->report->priority_firm ?? 0),
            self::BIGLAW => Priority::from($user->report->priority_biglaw ?? 0),
            self::PUBLIC => Priority::from($user->report->priority_public ?? 0),
            self::CLERK => Priority::from($user->report->priority_clerk ?? 0),
        };
    }

    public static function priorities(User $user): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn ($type) => [$type->label() => $type->priority($user)->value])
            ->sortDesc()
            ->filter()
            ->keys()
            ->all();
    }

    public function stat(object $aba)
    {
        return match ($this) {
            self::LAW => $aba->BPR_LT_FT / $aba->grads,
            self::FIRM => ($aba->firm_LT_FT + $aba->federal_LT_FT) / $aba->grads,
            self::BIGLAW => ($aba->federal_LT_FT + $aba->firm_251_500_LT_FT + $aba->firm_501_LT_FT) / $aba->grads,
            self::PUBLIC => ($aba->govt_LT_FT + $aba->pubInt_LT_FT) / $aba->grads,
            self::CLERK => ($aba->federal_LT_FT + $aba->state_LT_FT + $aba->otherClerk_LT_FT + $aba->tribal_LT_FT + $aba->intl_LT_FT) / $aba->grads,
        };
    }
}
