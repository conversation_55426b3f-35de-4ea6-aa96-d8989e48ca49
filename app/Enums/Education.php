<?php

namespace App\Enums;

enum Education: int
{
    case NO_HIGH_SCHOOL = 1;
    case HIGH_SCHOOL = 2;
    case COLLEGE = 3;
    case ASSOCIATES_DEGREE = 4;
    case BACHELORS_DEGREE = 5;
    case MASTERS_DEGREE = 6;
    case DOCTORAL_DEGREE = 7;

    public function label(): string
    {
        return match ($this) {
            self::NO_HIGH_SCHOOL => 'No high school degree',
            self::HIGH_SCHOOL => 'High school diploma or equivalency',
            self::COLLEGE => 'Some college but no degree or certificate',
            self::ASSOCIATES_DEGREE => "Associate's degree (two years or more)",
            self::BACHELORS_DEGREE => "Bachelor's (Baccalaureate) degree",
            self::MASTERS_DEGREE => "Master's degree (e.g., M.A., M.S., M. Eng., M.Ed., M.S.W., M.B.A., M.L.S.)",
            self::DOCTORAL_DEGREE => 'Doctoral (Doctorate) degree (e.g., <PERSON><PERSON><PERSON><PERSON>, Ph<PERSON><PERSON><PERSON>, M.D., Ed.D.)',
        };
    }
}
