<?php

namespace App;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class SamlUser
{
    public string $id;

    public ?string $first_name;

    public ?string $last_name;

    public ?string $email;

    public function __construct(string $id, array $attrs = [])
    {
        $this->id = $id;

        $this->first_name = Arr::first($attrs['givenName'] ?? []);

        $this->last_name = Arr::first($attrs['surname'] ?? []);

        $this->email = collect($attrs['email'] ?? [])
            ->map(fn ($email) => Str::lower($email) ?: null)
            ->filter(fn ($email) => !Str::endsWith($email, '.lsac'))
            ->first();
    }
}
