<?php

namespace App\Providers;

use App\Nova\AcademicYear;
use App\Nova\Announcement;
use App\Nova\Citation;
use App\Nova\Dashboards\Main;
use App\Nova\Guide;
use App\Nova\Podcast;
use App\Nova\Salary;
use App\Nova\School;
use App\Nova\State;
use App\Nova\Tag;
use App\Nova\Term;
use App\Nova\User;
use App\Support\Nav;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Menu\Menu;
use Laravel\Nova\Menu\MenuItem;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;
use Laravel\Nova\Panel;
use Oneduo\NovaFileManager\NovaFileManager;
use Outl1ne\NovaSettings\NovaSettings;
use Spatie\BackupTool\BackupTool;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        $this->gate();

        Nova::mainMenu(function () {
            return [
                MenuSection::dashboard(Main::class)
                    ->icon('home'),

                MenuSection::make('Database', [
                    MenuItem::resource(AcademicYear::class),
                    MenuItem::resource(Salary::class),
                    MenuItem::resource(School::class),
                    MenuItem::resource(State::class),
                    MenuItem::resource(User::class),
                ])
                    ->icon('database')
                    ->collapsable(),

                MenuSection::make('Content', [
                    MenuItem::resource(Announcement::class),
                    MenuItem::resource(Citation::class),
                    MenuItem::resource(Guide::class),
                    MenuItem::resource(Term::class),
                ])
                    ->icon('document-text')
                    ->collapsable(),

                MenuSection::make('Podcast', [
                    MenuItem::resource(Podcast\Episode::class),
                    MenuItem::resource(Podcast\Host::class),
                    MenuItem::resource(Podcast\Pathway::class),
                    MenuItem::resource(Podcast\Sponsor::class),
                ])
                    ->icon('microphone')
                    ->collapsable(),

                MenuSection::make('Metadata', [
                    MenuItem::resource(Tag::class),
                ])
                    ->icon('tag')
                    ->collapsable(),

                MenuSection::make('Files')
                    ->path('nova-file-manager')
                    ->icon('folder'),

                MenuSection::make('Settings')
                    ->path('settings')
                    ->icon('cog'),

                MenuSection::make('Backups')
                    ->path('backups')
                    ->icon('support'),
            ];
        });

        NovaSettings::addSettingsFields([
            Number::make('National min biglaw', 'national_min_biglaw')
                ->help('Minimum percent of graduates employed in biglaw to accredit a national school')
                ->rules('required')
                ->min(0)
                ->max(100),

            Number::make('Report years', 'report_years')
                ->help('Number of years to use in reports and when calculating national school accreditation')
                ->rules('required')
                ->min(1),

            Panel::make('Personal report', [
                Number::make('Min count', 'report_min_count')
                    ->help('Minimum number of graduates employed in the state to qualify the school for a location match')
                    ->rules('required')
                    ->min(0)
                    ->step(1),

                Number::make('Min percent', 'report_min_percent')
                    ->help('Minimum percent of graduates employed in the state to qualify the school for a location match')
                    ->rules('required')
                    ->min(0)
                    ->max(100),
            ]),

            Panel::make('State report', [
                Number::make('Min percent', 'state_min_percent')
                    ->help('Minimum percent of graduates employed in the state to qualify the school for the report')
                    ->rules('required')
                    ->min(0)
                    ->max(100),
            ]),
        ], [
            'national_min_biglaw' => 'float',
            'report_min_count' => 'integer',
            'report_min_percent' => 'float',
            'report_years' => 'integer',
            'state_min_percent' => 'float',
        ]);

        Nova::userMenu(function ($request, Menu $menu) {
            foreach (collect(Nav::user())->pluck('children')->flatten() as $item) {
                $menu->append(MenuItem::externalLink($item->title, $item->url));
            }

            return $menu;
        });

        Nova::footer(function () {
            return Blade::render('
                <p class="text-center"><a href="{{ route("home") }}" class="link-default">{{ $name }}</a> {{ $release }} powered by <a href="https://nova.laravel.com" class="link-default">Laravel Nova</a> v{!! $version !!}</p>
                <p class="text-center">© {!! $year !!} Laravel LLC · by Taylor Otwell and David Hemphill.</p>
            ', [
                'name' => config('app.name'),
                'release' => config('app.version'),
                'version' => Nova::version(),
                'year' => date('Y'),
            ]);
        });

        Number::macro('displayPercent', function () {
            return $this->displayUsing(fn ($value) => $value !== null ? "{$value}%" : null);
        });
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [
            BackupTool::make(),
            NovaFileManager::make(),
            NovaSettings::make(),
        ];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        Nova::report(function ($exception) {
            if (app()->bound('sentry')) {
                app('sentry')->captureException($exception);
            }
        });
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
            ->withAuthenticationRoutes()
            ->withPasswordResetRoutes()
            ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function ($user): bool {
            return $user->admin;
        });
    }

    /**
     * Get the extra dashboards that should be displayed on the Nova dashboard.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [
            new Main(),
        ];
    }
}
