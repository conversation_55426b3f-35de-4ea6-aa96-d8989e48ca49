<?php

namespace App;

use Illuminate\Database\Eloquent\Casts\Attribute;

class Tag extends Model
{
    public $timestamps = false;

    protected $casts = [
        'employer_type' => 'boolean',
        'glossary' => 'boolean',
        'practice_area' => 'boolean',
        'state' => 'boolean',
    ];

    protected $hidden = [
        'employer_type',
        'glossary',
        'practice_area',
        'state',
    ];

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope(function ($builder) {
            $builder->orderBy('label', 'asc');
        });

        static::deleting(function ($tag) {
            $tag->podcasts()->detach();
            $tag->terms()->detach();
        });
    }

    public function headline(): Attribute
    {
        return Attribute::make(fn () => "Tag: {$this->label}");
    }

    public function podcasts()
    {
        return $this->belongsToMany(Podcast\Episode::class, 'podcast_tag', 'tag_id', 'podcast_id');
    }

    public function terms()
    {
        return $this->belongsToMany(Term::class, 'term_tag');
    }

    public function toArray()
    {
        return $this->slug;
    }

    public function url(): Attribute
    {
        return Attribute::make(fn () => route('podcasts.iatl.tags.show', $this));
    }
}
