<?php

namespace App\Nova;

use App\Enums\SchoolType;
use App\Nova\Fields\TipTap;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\Line;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Slug;
use <PERSON>vel\Nova\Fields\Stack;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\UiAvatar;
use Laravel\Nova\Http\Requests\NovaRequest;

class School extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\School::class;

    /**
     * Default ordering for index query.
     *
     * @var array
     */
    public static $sort = [
        'name' => 'asc',
    ];

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'slug',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            UiAvatar::make('')
                ->backgroundColor('random')
                ->bold()
                ->disableDownload()
                ->onlyOnIndex(),

            Stack::make('Name', 'name', [
                Line::make('Name')
                    ->asHeading(),

                Line::make('Address')
                    ->asBase(),
            ])
                ->hideFromDetail()
                ->sortable(),

            Text::make('Name')
                ->hideFromIndex()
                ->rules('required', 'max:255')
                ->sortable(),

            Text::make('Name limited')
                ->hideFromIndex()
                ->rules('nullable', 'max:255')
                ->sortable(),

            Slug::make('Slug')
                ->from('Name')
                ->hideFromIndex()
                ->rules('required', 'alpha_dash', 'max:255'),

            Text::make('Address')
                ->hideFromIndex()
                ->rules('nullable', 'max:255')
                ->sortable(),

            Text::make('City')
                ->hideFromIndex()
                ->rules('required_unless:state,DC', 'max:255', 'nullable')
                ->sortable(),

            ...$request->viaRelationship() ? [] : [
                BelongsTo::make('State')
                    ->hideFromIndex()
                    ->filterable(),
            ],

            Select::make('Type')
                ->displayUsingLabels()
                ->filterable()
                ->hideFromIndex()
                ->options(SchoolType::items())
                ->sortable(),

            Text::make('Indicators')
                ->hideFromIndex()
                ->rules('nullable', 'max:255'),

            Number::make('ABA ID')
                ->hideFromIndex()
                ->rules('nullable'),

            Number::make('LSAC ID')
                ->hideFromIndex()
                ->rules('nullable'),

            Boolean::make('National')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Boolean::make('Accepts GRE')
                ->filterable()
                ->sortable()
                ->withMeta(['indexName' => 'GRE']),

            Number::make('Emp score', 'employment_score')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Unemp score', 'underemployment_score')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Grads')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Emp')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Biglaw')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Clerk')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Firm')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Lawyer')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Natl')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Public')
                ->displayPercent()
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            TipTap::make('Note')
                ->alwaysShow(),

            TipTap::make('Delete reason')
                ->alwaysShow()
                ->showOnCreating(false)
                ->showOnDetail(fn () => $this->trashed())
                ->showOnUpdating(fn () => $this->trashed()),

            HasMany::make('States'),
        ];
    }

    /**
     * Build an "index" query for the given resource.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return parent::indexQuery($request, $query)->withTrashed();
    }
}
