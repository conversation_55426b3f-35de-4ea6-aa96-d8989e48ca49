<?php

namespace App\Nova;

use App\Nova\Fields\Multiselect;
use App\Nova\Fields\TipTap;
use <PERSON>vel\Nova\Fields\Slug;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Term extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Term::class;

    /**
     * Default ordering for index query.
     *
     * @var array
     */
    public static $sort = [
        'title' => 'asc',
    ];

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'title',
        'slug',
        'body',
    ];

    /**
     * The relationships that should be eager loaded when performing an index query.
     *
     * @var array
     */
    public static $with = [
        'tags',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Title')
                ->rules('required', 'max:255')
                ->sortable(),

            Slug::make('Slug')
                ->from('Name')
                ->hideFromIndex()
                ->rules('required', 'alpha_dash', 'max:255'),

            TipTap::make('Body')
                ->alwaysShow(),

            Multiselect::make('Tags')
                ->belongsToMany(Tag::class),
        ];
    }
}
