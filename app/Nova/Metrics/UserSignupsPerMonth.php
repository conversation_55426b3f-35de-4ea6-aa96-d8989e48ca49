<?php

namespace App\Nova\Metrics;

use App\User;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Trend;

class UserSignupsPerMonth extends Trend
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $metric = $this->countByMonths($request, User::class);

        $metric->result(array_sum(array_values($metric->trend)) / count($metric->trend));

        return $metric;
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            12 => __('1 Year'),
            24 => __('2 Years'),
            36 => __('3 Years'),
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        return now()->addMinutes(60);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'user-signups-per-month';
    }
}
