<?php

namespace App\Nova\Dashboards;

use App\Nova\Metrics;
use Laravel\Nova\Dashboards\Main as Dashboard;

class Main extends Dashboard
{
    /**
     * Get the displayable name of the dashboard.
     *
     * @return string
     */
    public function name()
    {
        return 'Dashboard';
    }

    /**
     * Get the cards for the dashboard.
     *
     * @return array
     */
    public function cards()
    {
        return [
            Metrics\BudgetCreates::make()->width('1/4'),
            Metrics\CompassPurchases::make()->width('1/4'),
            Metrics\ReportCompletions::make()->width('1/4'),
            Metrics\UserSignups::make()->width('1/4'),
            Metrics\BudgetCreatesPerMonth::make()->width('1/2'),
            Metrics\CompassPurchasesPerMonth::make()->width('1/2'),
            Metrics\ReportCompletionsPerMonth::make()->width('1/2'),
            Metrics\UserSignupsPerMonth::make()->width('1/2'),
        ];
    }
}
