<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Report extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\UserReport>
     */
    public static $model = \App\UserReport::class;

    /**
     * Indicates if the resource should be globally searchable.
     *
     * @var bool
     */
    public static $globallySearchable = false;

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Boolean::make('Step 1', 'step1')
                ->sortable(),

            Boolean::make('Step 2', 'step2')
                ->sortable(),

            Boolean::make('Step 3', 'step3')
                ->sortable(),

            Boolean::make('Step 4', 'step4')
                ->sortable(),

            Boolean::make('Step 5', 'step5')
                ->sortable(),
        ];
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }
}
