<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Slug;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\UiAvatar;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class State extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\State::class;

    /**
     * Default ordering for index query.
     *
     * @var array
     */
    public static $sort = [
        'name' => 'asc',
    ];

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name',
        'code',
        'slug',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            UiAvatar::make('')
                ->backgroundColor('random')
                ->bold()
                ->disableDownload()
                ->onlyOnIndex(),

            Text::make('Name'),

            Text::make('Code'),

            Slug::make('Slug')
                ->from('Name')
                ->hideFromIndex()
                ->rules('required', 'alpha_dash', 'max:255'),

            Currency::make('Median earnings')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Currency::make('Cost of living')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Currency::make('Tuition')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            Number::make('Schools', 'school_count')
                ->filterable()
                ->onlyOnIndex()
                ->sortable(),

            HasMany::make('Schools'),
        ];
    }

    public static function authorizedToCreate($request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }
}
