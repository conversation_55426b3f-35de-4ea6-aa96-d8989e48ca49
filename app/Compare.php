<?php

namespace App;

use Illuminate\Database\Eloquent\Casts\Attribute;

class Compare extends Model
{
    const NAME_SEPARATOR = ' vs ';

    const SLUG_SEPARATOR = '-vs-';

    protected $appends = [
        'url',
    ];

    protected $fillable = [
        'schools',
    ];

    protected $table = 'school_compares';

    public function name(): Attribute
    {
        return Attribute::make(fn () => $this->schools->pluck(count($this->schools) <= 2 ? 'name' : 'name_limited')->join(self::NAME_SEPARATOR));
    }

    public function displayName(): Attribute
    {
        return Attribute::make(fn () => $this->schools->pluck(count($this->schools) <= 2 ? 'display_name' : 'name_limited')->join(self::NAME_SEPARATOR));
    }

    public function schoolA()
    {
        return $this->belongsTo(School::class, 'school_a_id');
    }

    public function schoolB()
    {
        return $this->belongsTo(School::class, 'school_b_id');
    }

    public function schools(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, array $attributes) => $value ?? collect([$this->schoolA, $this->schoolB]),
            set: fn (iterable $schools) => collect($schools)->sortBy('slug')->values(),
        );
    }

    public function slug(): Attribute
    {
        return Attribute::make(fn () => $this->schools->pluck('slug')->join(self::SLUG_SEPARATOR));
    }

    public function title(): Attribute
    {
        return Attribute::make(fn () => $this->schools->count() ? "Compare {$this->name}" : 'Compare law schools');
    }

    public function url(): Attribute
    {
        return Attribute::make(fn () => route('compare', $this->slug));
    }
}
