<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ListController extends Controller
{
    public function show(Request $request)
    {
        $schools = $request->user()->schools->sortByDesc('job_score')->values()->all();

        $latlng = DB::table('schools')
            ->selectRaw('MAX(ST_X(location)) max_x, MIN(ST_X(location)) min_x, MAX(ST_Y(location)) max_y, MIN(ST_Y(location)) min_y')
            ->whereIn('id', Arr::pluck($schools, 'id'))
            ->first();

        $center = null;

        if ($latlng) {
            $center = (object) [
                'lat' => ($latlng->max_y + $latlng->min_y) / 2,
                'lng' => ($latlng->max_x + $latlng->min_x) / 2,
            ];
        }

        return view('list', compact('center', 'schools'));
    }
}
