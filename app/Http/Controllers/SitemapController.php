<?php

namespace App\Http\Controllers;

use App\Support\Sitemap;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use SimpleXMLElement;

class SitemapController extends Controller
{
    private $urlset;

    public function html()
    {
        return view('pages.sitemap', ['items' => Sitemap::items(format: Sitemap::HTML, guest: Auth::guest(), theme: Request::theme())]);
    }

    public function xml()
    {
        $sitemap = Cache::remember(Request::theme() . ':sitemap', 86400, function () {
            $this->urlset = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><urlset/>');

            $this->urlset->addAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

            $this->flatten(Sitemap::items(format: Sitemap::XML, theme: Request::theme()));

            return $this->urlset->asXML();
        });

        return response($sitemap)->header('Content-Type', 'text/xml;charset=utf-8');
    }

    private function flatten(iterable $items)
    {
        foreach ($items as $item) {
            if ($item->url ?? null) {
                $url = $this->urlset->addChild('url');
                $url->addChild('loc', $item->url);
                $url->addChild('priority', $item->priority);
            }

            if ($item->items ?? []) {
                $this->flatten($item->items);
            }
        }
    }
}
