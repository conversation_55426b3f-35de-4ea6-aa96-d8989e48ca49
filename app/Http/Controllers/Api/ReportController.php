<?php

namespace App\Http\Controllers\Api;

use App\Enums\Priority;
use App\Enums\Race;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReportResource;
use App\Reports\PersonalReport;
use App\Reports\Report;
use App\Reports\StateReport;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;

class ReportController extends Controller
{
    private string $report;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if ($request->user()->can('manageUsers') && $request->route('user')) {
                $user = $request->route('user');

                $request->setUserResolver(fn () => $user);
            } elseif ($request->user()->can('manageUsers') && $request->headers->has('X-User-ID')) {
                $user = User::firstWhere(['saml_id' => $request->headers->get('X-User-ID')]);

                $request->setUserResolver(fn () => $user);
            } elseif ($request->route('user') && $request->user()->isNot($request->route('user'))) {
                abort(403, 'Forbidden.');
            }

            $this->report = Report::class;

            if (in_array($request->input('source'), ['list', 'suggestions'])) {
                $this->report = PersonalReport::class;

                if (!$request->user()) {
                    abort(400, "User is required for {$request->input('source')} report"); // @codeCoverageIgnore
                }
            } elseif ($request->input('designation')) {
                $this->report = StateReport::class;
            }

            $response = $next($request);

            if (!$request->route('school')) {
                $response->headers->set('X-Report-Type', class_basename($this->report));
            }

            return $response;
        });
    }

    public function overview(Request $request, User $user)
    {
        return ReportResource::make($this->report::create($request));
    }

    public function jobs(Request $request, User $user)
    {
        return ReportResource::make($this->report::create($request, 'jobs'));
    }

    public function environment(Request $request, User $user)
    {
        return ReportResource::make($this->report::create($request, 'environment'));
    }

    public function financials(Request $request, User $user)
    {
        return ReportResource::make($this->report::create($request, 'financials'));
    }

    public function pt(Request $request, User $user)
    {
        return ReportResource::make($this->report::create($request, 'pt'));
    }

    public function store(Request $request)
    {
        $payload = (object) $request->validate([
            'gpa' => ['nullable', 'numeric', 'between:0,4.33'],
            'lsat' => ['nullable', 'integer', 'between:120,180'],
            'national' => ['required', 'boolean'],
            'priorityBiglaw' => ['required', new Enum(Priority::class)],
            'priorityClerk' => ['required', new Enum(Priority::class)],
            'priorityFirm' => ['required', new Enum(Priority::class)],
            'priorityLaw' => ['required', new Enum(Priority::class)],
            'priorityPublic' => ['required', new Enum(Priority::class)],
            'race' => ['nullable', 'array'],
            'race.*' => [new Enum(Race::class)],
            'statesPreferred' => ['nullable', 'array'],
            'statesPreferred.*' => ['exists:states,code'],
        ]);

        DB::transaction(function () use ($payload, $request) {
            $user = $request->user();
            $user->gpa = $payload->gpa;
            $user->lsat = $payload->lsat;
            $user->race = $payload->race;
            $user->statesPreferred()->sync($payload->statesPreferred);
            $user->save();

            $user->report->national = $payload->national;
            $user->report->priority_law = $payload->priorityLaw;
            $user->report->priority_firm = $payload->priorityFirm;
            $user->report->priority_biglaw = $payload->priorityBiglaw;
            $user->report->priority_public = $payload->priorityPublic;
            $user->report->priority_clerk = $payload->priorityClerk;
            $user->report->step1 = true;
            $user->report->step2 = true;
            $user->report->completed_at ??= now();
            $user->report->save();
        });

        $request->session()->flash('personal-report-alert');

        return ReportResource::make($this->report::create($request));
    }
}
