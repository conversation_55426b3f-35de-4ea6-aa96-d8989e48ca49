<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SchoolResource;
use Illuminate\Http\Request;

class ListController extends Controller
{
    public function index(Request $request)
    {
        return SchoolResource::collection($request->user()->schools()->get());
    }

    public function add(Request $request)
    {
        $request->validate(['id' => ['required', 'integer', 'exists:schools,id']]);

        $request->user()->schools()->syncWithoutDetaching($request->id);
    }

    public function remove(Request $request)
    {
        $request->validate(['id' => ['required', 'integer']]);

        $request->user()->schools()->detach($request->id);
    }
}
