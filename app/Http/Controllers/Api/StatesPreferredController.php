<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\StateResource;
use Illuminate\Http\Request;

class StatesPreferredController extends Controller
{
    public function index(Request $request)
    {
        return StateResource::collection($request->user()->statesPreferred()->get());
    }

    public function add(Request $request)
    {
        $request->validate(['code' => ['required', 'exists:states,code']]);

        $request->user()->statesPreferred()->syncWithoutDetaching($request->code);
    }

    public function remove(Request $request)
    {
        $request->validate(['code' => ['required', 'exists:states,code']]);

        $request->user()->statesPreferred()->detach($request->code);
    }
}
