<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function index(Request $request)
    {
        if ($request->user()->cannot('manageUsers')) {
            abort(403, 'Forbidden.');
        }

        $users = User::search($request->q);

        if ($request->email) {
            $users = $users->where(['email' => $request->email]);
        }

        return UserResource::collection($users->limit(20)->get());
    }

    public function show(Request $request, User $user)
    {
        if ($request->user()->cannot('manageUsers') && $request->user()->isNot($user)) {
            abort(403, 'Forbidden.');
        }

        $user->load([
            'finances',
            'states',
            'statesPreferred',
        ]);

        return UserResource::make($user);
    }
}
