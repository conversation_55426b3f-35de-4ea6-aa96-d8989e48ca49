<?php

namespace App\Http\Controllers;

use App\Guide;

class GlossaryController extends Controller
{
    public function index()
    {
        $guide = (object) [];
        $sidebar = Guide::published()->get();

        $guide->lst_reports_help = $sidebar->filter(function ($value) {
            if ($value->group == 'Reports Help') {
                return $value;
            }
        });

        $guide->in_law_school = $sidebar->filter(function ($value) {
            if ($value->group == 'In Law School') {
                return $value;
            }
        });

        $guide->other_guides = $sidebar->filter(function ($value) {
            if ($value->group == 'Other Guides') {
                return $value;
            }
        });

        $guide->paying_for_law_school = $sidebar->filter(function ($value) {
            if ($value->group == 'Paying For Law School') {
                return $value;
            }
        });

        return view('glossary', compact('guide'));
    }
}
