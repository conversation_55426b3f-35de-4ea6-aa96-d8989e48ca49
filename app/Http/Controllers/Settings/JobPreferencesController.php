<?php

namespace App\Http\Controllers\Settings;

use App\Enums\Priority;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;

class JobPreferencesController extends Controller
{
    public function show()
    {
        return view('settings.job-preferences');
    }

    public function update(Request $request)
    {
        $payload = (object) $request->validate([
            'national' => ['required', 'boolean'],
            'priority_biglaw' => ['required', new Enum(Priority::class)],
            'priority_clerk' => ['required', new Enum(Priority::class)],
            'priority_firm' => ['required', new Enum(Priority::class)],
            'priority_law' => ['required', new Enum(Priority::class)],
            'priority_public' => ['required', new Enum(Priority::class)],
            'states.*' => ['exists:states,code'],
        ]);

        if (!($payload->national ?? false) && !($payload->states ?? [])) {
            return redirect()->back()->with('error-message', 'Please select a location where you want to work.');
        }

        DB::transaction(function () use ($payload, $request) {
            $user = $request->user();

            $user->statesPreferred()->sync($payload->states ?? []);

            $user->report->national = $payload->national;
            $user->report->priority_law = $payload->priority_law;
            $user->report->priority_firm = $payload->priority_firm;
            $user->report->priority_biglaw = $payload->priority_biglaw;
            $user->report->priority_public = $payload->priority_public;
            $user->report->priority_clerk = $payload->priority_clerk;

            $user->report->save();
        });

        $response = redirect()->back();

        if (Str::startsWith($request->next, route('report'))) {
            $response = redirect()->to($request->next);
        }

        return $response->with('success-message', 'You successfully updated your job preferences.');
    }
}
