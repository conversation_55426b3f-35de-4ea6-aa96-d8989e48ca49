<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ContributionController extends Controller
{
    public function update(Request $request)
    {
        $request->validate([
            'y1_contribution' => ['nullable', 'numeric', 'min:0'],
            'y2_contribution' => ['nullable', 'numeric', 'min:0'],
            'y3_contribution' => ['nullable', 'numeric', 'min:0'],
            'y4_contribution' => ['nullable', 'numeric', 'min:0'],
            'y5_contribution' => ['nullable', 'numeric', 'min:0'],
        ]);

        DB::transaction(function () use ($request) {
            $user = $request->user();

            $user->finances->y1_contribution = $request->y1_contribution;
            $user->finances->y2_contribution = $request->y2_contribution;
            $user->finances->y3_contribution = $request->y3_contribution;
            $user->finances->y4_contribution = $request->y4_contribution;
            $user->finances->y5_contribution = $request->y5_contribution;
            $user->finances->save();
        });

        $response = redirect()->back();

        return $response->with('success-message', 'You successfully updated your contribution.');
    }
}
