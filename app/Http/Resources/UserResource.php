<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->saml_id,
            'name' => $this->name,
            'email' => $this->email,
            'lsat' => $this->lsat,
            'gpa' => $this->gpa,
            'has_compass' => $this->has_compass,
            'has_report' => $this->has_report,
            'national' => $this->whenLoaded('states', fn () => $this->report->national),
            'priority_law' => $this->whenLoaded('states', fn () => $this->report->priority_law),
            'priority_firm' => $this->whenLoaded('states', fn () => $this->report->priority_firm),
            'priority_biglaw' => $this->whenLoaded('states', fn () => $this->report->priority_biglaw),
            'priority_public' => $this->whenLoaded('states', fn () => $this->report->priority_public),
            'priority_clerk' => $this->whenLoaded('states', fn () => $this->report->priority_clerk),
            'states_preferred' => $this->whenLoaded('states', fn () => $this->statesPreferred->pluck('code')),
            'states_resided' => $this->whenLoaded('states', fn () => $this->states->pluck('code')),
            'target_tax' => $this->whenLoaded('finances', fn () => $this->report->target_tax),
            'target_debt_period' => $this->whenLoaded('finances', fn () => $this->report->target_debt_period),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
