<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Route;

class RedirectScopes
{
    /**
     * Handle an incoming request.
     *
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->scope) {
            $route = str_replace('_redirect', '', Route::currentRouteName());

            if (!in_array($request->scope, ['national', 'total'])) {
                $route .= ".{$request->scope}";
            }

            if (Route::has($route)) {
                return redirect()->route($route, $request->except('scope'), 301);
            }
        }

        return $next($request);
    }
}
