<?php

namespace App;

use App\Enums\Gender;
use App\Jobs\CompassUpdate;
use App\Support\Gravatar;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Request;
use Laravel\Nova\Auth\Impersonatable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use Concerns\HasCompass;
    use Concerns\HasReport;
    use Concerns\HasSearch;
    use Concerns\Transactions;
    use HasApiTokens;
    use HasFactory;
    use Impersonatable;
    use Notifiable;

    protected $appends = [
        'name',
    ];

    protected $attributes = [
        'compass_plan' => null,
        'is_admin' => false,
        'lcc_id' => null,
        'lcc_latest' => null,
    ];

    protected $casts = [
        'compass_joined_at' => 'datetime',
        'compass_plan' => 'string',
        'compass_terms_accepted_at' => 'datetime',
        'email_verified_at' => 'datetime',
        'gender' => Enums\Gender::class,
        'is_admin' => 'boolean',
        'lcc_id' => 'integer',
        'lcc_latest' => 'datetime',
        'lds' => 'boolean',
        'lsac_waiver' => Enums\FeeWaiver::class,
        'parental_education' => Enums\Education::class,
        'race' => Casts\CSV::class . ':' . Enums\Race::class,
    ];

    protected $guarded = [];

    protected $searchFields = [
        'email',
    ];

    protected $searchWith = [
        'report',
    ];

    protected $visible = [
        'id',
        'name',
        'first_name',
        'last_name',
        'email',
        'gpa',
        'lsat',
        'created_at',
        'updated_at',
    ];

    public static function booted()
    {
        static::created(function ($user) {
            $user->finances()->create();
            $user->report()->create();
        });

        static::deleting(function ($user) {
            $user->finances->delete();
            $user->report->delete();
            $user->schools()->detach();
            $user->states()->detach();
            $user->statesPreferred()->detach();
        });

        static::retrieved(function ($user) {
            if (Request::is('nova-vendor/sanctum-tokens/*')) {
                $user->makeVisible('tokens'); // @codeCoverageIgnore
            }
        });

        static::updated(function ($user) {
            if ($user->lcc_id) {
                CompassUpdate::dispatch($user);
            }
        });
    }

    public function firstName(): Attribute
    {
        return Attribute::make(fn () => $this->first);
    }

    public function lastName(): Attribute
    {
        return Attribute::make(fn () => $this->last);
    }

    public function admin(): Attribute
    {
        return Attribute::make(fn () => $this->is_admin || Request::impersonating());
    }

    public function avatar(): Attribute
    {
        return Attribute::make(fn () => Gravatar::make($this->email));
    }

    public function budgets()
    {
        return $this->hasMany(Budget::class);
    }

    public function discounts(): Attribute
    {
        return Attribute::make(function () {
            $discounts = [];

            if ($this->lds) {
                $discounts[] = 'LDS';
            }

            if ($this->states) {
                $discounts = array_merge($discounts, $this->states->pluck('code')->all());
            }

            return implode(', ', $discounts);
        });
    }

    public function displayName(): Attribute
    {
        return Attribute::make(fn () => strlen($this->first) <= 10 ? $this->first : $this->initials);
    }

    public function finances()
    {
        return $this->hasOne(UserFinances::class);
    }

    public function hasScores(): Attribute
    {
        return Attribute::make(fn () => $this->gpa && $this->lsat);
    }

    public function initials(): Attribute
    {
        return Attribute::make(fn () => substr($this->first, 0, 1) . substr($this->last, 0, 1));
    }

    public function name(): Attribute
    {
        return Attribute::make(fn () => trim("{$this->first} {$this->last}") ?: 'Unknown User');
    }

    public function pronoun(): Attribute
    {
        return Attribute::make(fn () => match ($this->gender) {
            Gender::FEMALE => 'FEMALE',
            Gender::MALE => 'MALE',
            default => 'NEUTRAL',
        });
    }

    public function races(): Attribute
    {
        return Attribute::make(fn () => implode(', ', Arr::map($this->race, fn ($race) => $race->label())));
    }

    public function report()
    {
        return $this->hasOne(UserReport::class);
    }

    public function schools()
    {
        return $this->belongsToMany(School::class, 'user_schools')->withTimestamps()->withTrashed();
    }

    public function states()
    {
        return $this->belongsToMany(State::class, 'user_states', 'user_id', 'state_code');
    }

    public function statesPreferred()
    {
        return $this->belongsToMany(State::class, 'user_states_preferred', 'user_id', 'state_code');
    }

    public function urm(): Attribute
    {
        return Attribute::make(fn () => (bool) array_filter(
            array_map(fn ($race) => $race->urm(), $this->race)
        ));
    }
}
