<?php

namespace App\Concerns;

use App\Support\Datasets;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Collection;

trait HasEnvironment
{
    public function environment(?int $first_year = null, ?int $last_year = null): Attribute|Collection
    {
        if (func_get_args()) {
            return Datasets::environment(...func_get_args(), schools: $this);
        }

        return Attribute::make(fn () => Datasets::environment(schools: $this));
    }

    public function latestEnvironment(): Attribute
    {
        return Attribute::make(fn () => $this->environment->last());
    }
}
