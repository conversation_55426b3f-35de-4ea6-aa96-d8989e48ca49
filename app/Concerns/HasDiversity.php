<?php

namespace App\Concerns;

use App\Support\Datasets;
use App\Support\Math;
use Illuminate\Database\Eloquent\Casts\Attribute;

trait HasDiversity
{
    public function studentRacialDiversity(): Attribute
    {
        return Attribute::make(function () {
            $races = collect([
                (object) ['name' => 'Hispanic', 'count' => $this->latest_enrollments->hispanic],
                (object) ['name' => 'Native American', 'count' => $this->latest_enrollments->native],
                (object) ['name' => 'Asian', 'count' => $this->latest_enrollments->asian],
                (object) ['name' => 'Black', 'count' => $this->latest_enrollments->black],
                (object) ['name' => 'Pacific Islander', 'count' => $this->latest_enrollments->islander],
                (object) ['name' => '2+ races', 'count' => $this->latest_enrollments->tworaces],
                (object) ['name' => 'International', 'count' => $this->latest_enrollments->intl],
                (object) ['name' => 'White', 'count' => $this->latest_enrollments->white],
                (object) ['name' => 'Declined to report', 'count' => $this->latest_enrollments->unknownrace],
            ])
                ->sortByDesc('count')
                ->filter(fn ($item) => $item->count > 0);

            $total = $races->sum('count');

            return $races->values()->map(function ($item) use ($total) {
                return (object) [
                    'name' => $item->name,
                    'count' => $item->count,
                    'percent' => Math::percent($item->count, $total),
                ];
            });
        });
    }

    public function studentGenderDiversity(): Attribute
    {
        return Attribute::make(function () {
            $genders = collect([
                (object) ['name' => 'Women',  'count' => Datasets::sumEnrollmentObject('women_total', $this->latest_enrollments)],
                (object) ['name' => 'Men', 'count' => Datasets::sumEnrollmentObject('men_total', $this->latest_enrollments)],
                (object) ['name' => 'Another gender',  'count' => Datasets::sumEnrollmentObject('AGI_total', $this->latest_enrollments)],
                (object) ['name' => 'Not reported',  'count' => Datasets::sumEnrollmentObject('PNR_total', $this->latest_enrollments)],
            ])
                ->sortByDesc('count')
                ->filter(fn ($item) => $item->count > 0);

            $total = $genders->sum('count');

            return $genders->values()->map(function ($item) use ($total) {
                return (object) [
                    'name' => $item->name,
                    'count' => $item->count,
                    'percent' => Math::percent($item->count, $total),
                ];
            });
        });
    }

    public function facultyRacialDiversity(): Attribute
    {
        return Attribute::make(function () {
            $races = collect([
                (object) ['name' => 'POC', 'count' => $this->latest_faculty->poc_FT],
                (object) ['name' => 'White', 'count' => ($this->latest_faculty->fulltime - $this->latest_faculty->poc_FT)],
            ])
                ->sortByDesc('count')
                ->filter(fn ($item) => $item->count > 0);

            $total = $races->sum('count');

            return $races->values()->map(function ($item) use ($total) {
                return (object) [
                    'name' => $item->name,
                    'count' => $item->count,
                    'percent' => Math::percent($item->count, $total),
                ];
            });
        });
    }

    public function facultyGenderDiversity(): Attribute
    {
        return Attribute::make(function () {
            $genders = collect([
                (object) ['name' => 'Women', 'count' => $this->latest_faculty->women_FT],
                (object) ['name' => 'Men', 'count' => $this->latest_faculty->men_FT],
                (object) ['name' => 'Another gender', 'count' => $this->latest_faculty->AGI_FT],
                (object) ['name' => 'Not reported', 'count' => $this->latest_faculty->PNR_FT],
            ])
                ->sortByDesc('count')
                ->filter(fn ($item) => $item->count > 0);

            $total = $genders->sum('count');

            return $genders->values()->map(function ($item) use ($total) {
                return (object) [
                    'name' => $item->name,
                    'count' => $item->count,
                    'percent' => Math::percent($item->count, $total),
                ];
            });
        });
    }

    public function graduatesRacialDiversity(): Attribute
    {
        return Attribute::make(fn () => (
            collect([
                ['name' => 'Hispanic', 'count' => $this->latest_enrollments->grad_hispanic],
                ['name' => 'Native American', 'count' => $this->latest_enrollments->grad_native],
                ['name' => 'Asian', 'count' => $this->latest_enrollments->grad_asian],
                ['name' => 'Black', 'count' => $this->latest_enrollments->grad_black],
                ['name' => 'Pacific Islander', 'count' => $this->latest_enrollments->grad_islander],
                ['name' => '2+ races', 'count' => $this->latest_enrollments->grad_tworaces],
                ['name' => 'International', 'count' => $this->latest_enrollments->grad_intl],
                ['name' => 'White', 'count' => $this->latest_enrollments->grad_white],
                ['name' => 'Declined to report', 'count' => $this->latest_enrollments->grad_unknownrace],
            ])
                ->objects()
                ->filter(fn ($item) => $item->count > 0)
                ->each(function ($item) {
                    $item->percent = Math::percent($item->count, $this->latest_enrollments->grad);
                })
                ->sortByDesc('count')
                ->values()
        ));
    }
}
