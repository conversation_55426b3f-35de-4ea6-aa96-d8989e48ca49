<?php

namespace App\Concerns;

use App\Support\SearchResult;
use Illuminate\Support\Facades\Request;

trait HasSearch
{
    /**
     * Search the model using the specified term string.
     */
    public static function search(?string $term)
    {
        $model = static::make();
        $table = $model->getTable();

        $fields = collect($model->searchFields ?? [])->map(fn ($field) => (
            str($field)->whenNotContains('.', fn ($field) => "{$table}.{$field}")
        ));
        $joins = $model->searchJoins ?? [];
        $with = $model->searchWith ?? [];

        $query = static::query()->distinct()
            ->when($joins, fn ($query) => $query->select("{$table}.*")->tap(fn ($query) => (
                collect($joins)->each(fn ($join) => $query->leftJoin(...$join))
            )))
            ->whereAny($fields, 'like', str($term)->escape(['\\', '%', '_'])->wrap('%'))
            ->when($with, fn ($query) => $query->tap(fn ($query) => (
                collect($with)->each(fn ($with) => $query->with($with))
            )))
            ->when(method_exists($model, 'scopePublished'), fn ($query) => $query->published())
            ->when($model->searchTrashed, fn ($query) => $query->withTrashed());

        if (!Request::is('api/*')) {
            return SearchResult::collection($query->get()->all());
        }

        return $query;
    }
}
