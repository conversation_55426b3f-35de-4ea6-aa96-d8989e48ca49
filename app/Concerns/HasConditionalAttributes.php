<?php

namespace App\Concerns;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionMethod;
use ReflectionNamedType;
use ReflectionUnionType;

trait HasConditionalAttributes
{
    /**
     * The cache of the "Attribute" return type marked mutated attributes for each class.
     *
     * @var array
     */
    protected static $attributeMutatorCache = [];

    /**
     * The cache of the "Attribute" return type marked mutated, settable attributes for each class.
     *
     * @var array
     */
    protected static $setAttributeMutatorCache = [];

    /**
     * Determine if a "Attribute" return type marked mutator exists for an attribute.
     *
     * @param  string  $key
     * @return bool
     */
    public function hasAttributeMutator($key)
    {
        if (isset(static::$attributeMutatorCache[get_class($this)][$key])) {
            return static::$attributeMutatorCache[get_class($this)][$key];
        }

        if (!method_exists($this, $method = Str::camel($key))) {
            return static::$attributeMutatorCache[get_class($this)][$key] = false;
        }

        return static::$attributeMutatorCache[get_class($this)][$key] =
                    static::hasAttributeReturnType($this, $method);
    }

    /**
     * Determine if an "Attribute" return type marked set mutator exists for an attribute.
     *
     * @param  string  $key
     * @return bool
     */
    public function hasAttributeSetMutator($key)
    {
        $class = get_class($this);

        if (isset(static::$setAttributeMutatorCache[$class][$key])) {
            return static::$setAttributeMutatorCache[$class][$key];
        }

        if (!method_exists($this, $method = Str::camel($key))) {
            return static::$setAttributeMutatorCache[$class][$key] = false;
        }

        return static::$setAttributeMutatorCache[$class][$key] = // @codeCoverageIgnore
                    static::hasAttributeReturnType($this, $method) &&  // @codeCoverageIgnore
                    is_callable($this->{$method}()->set); // @codeCoverageIgnore
    }

    /**
     * Get all of the "Attribute" return typed attribute mutator methods.
     *
     * @param  mixed  $class
     * @return array
     */
    protected static function getAttributeMarkedMutatorMethods($class)
    {
        $instance = is_object($class) ? $class : new $class();

        return collect((new ReflectionClass($instance))->getMethods())->filter(function ($method) use ($instance) {
            if (static::hasAttributeReturnType($instance, $method->getName())) {
                $method->setAccessible(true);

                if (is_callable($method->invoke($instance)->get)) {
                    return true;
                }
            }

            return false;
        })->map->name->values()->all();
    }

    /**
     * Determine if a "Attribute" return type exists for an attribute.
     *
     * @param  string  $method
     * @return bool
     */
    protected static function hasAttributeReturnType($instance, $method)
    {
        $returnType = (new ReflectionMethod($instance, $method))->getReturnType();

        if ($returnType instanceof ReflectionNamedType && $returnType->getName() === Attribute::class) {
            return true;
        }

        if ($returnType instanceof ReflectionUnionType) {
            foreach ($returnType->getTypes() as $returnType) {
                if ($returnType->getName() === Attribute::class) {
                    return true;
                }
            }
        }

        return false;
    }
}
