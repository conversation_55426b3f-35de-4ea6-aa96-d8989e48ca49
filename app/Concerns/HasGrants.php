<?php

namespace App\Concerns;

use App\Support\Datasets;
use App\Support\Math;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Collection;

trait HasGrants
{
    public function grants(?int $first_year = null, ?int $last_year = null): Attribute|Collection
    {
        if (func_get_args()) {
            return Datasets::grants(...func_get_args(), schools: $this);
        }

        return Attribute::make(fn () => Datasets::grants(schools: $this));
    }

    public function latestGrants(): Attribute
    {
        return Attribute::make(fn () => $this->grants->last());
    }

    public function medianDiscount(): Attribute
    {
        return Attribute::make(fn () => $this->latest_grants?->grant_50_FT);
    }

    public function receivesMedianDiscountPercent(): Attribute
    {
        return Attribute::make(fn () => round(($this->grants_by_percent_ft['more_than_zero']) / 2, 1));
    }

    public function medianDiscountPt(): Attribute
    {
        return Attribute::make(fn () => $this->latest_grants?->grant_50_PT);
    }

    public function receivesMedianDiscountPercentPt(): Attribute
    {
        return Attribute::make(fn () => round(($this->grants_by_percent_pt['more_than_zero']) / 2, 1));
    }

    public function grantsByPercentFt(): Attribute
    {
        return Attribute::make(function () {
            $has_grants_total = $this->latest_grants->less_than_half_FT
                + $this->latest_grants->more_than_half_less_than_full_FT
                + $this->latest_grants->full_tuition_FT
                + $this->latest_grants->more_than_full_FT;
            $has_grants_percent = $this->latest_grants->enrollment_FT < $has_grants_total ? 100 : Math::percent($has_grants_total, $this->latest_grants->enrollment_FT);

            $has_grants_breakdown = [
                'none' => round(100 - $has_grants_percent, 1),
                'more_than_zero' => $has_grants_percent,
            ];

            foreach (
                [
                    'less_than_half' => 'less_than_half_FT',
                    'more_than_half_less_than_full' => 'more_than_half_less_than_full_FT',
                    'full' => 'full_tuition_FT',
                    'more_than_full' => 'more_than_full_FT',
                ] as $key => $db_field
            ) {
                $has_grants_breakdown[$key] = $this->latest_grants->{$db_field} ? round(($this->latest_grants->{$db_field} / $has_grants_total * $has_grants_percent), 1) : 0;
            }

            return $has_grants_breakdown;
        });
    }

    public function grantsByPercentPt(): Attribute
    {
        return Attribute::make(function () {
            $has_grants_total = $this->latest_grants->less_than_half_PT
                + $this->latest_grants->more_than_half_less_than_full_PT
                + $this->latest_grants->full_tuition_PT
                + $this->latest_grants->more_than_full_PT;
            $has_grants_percent = $this->latest_grants->enrollment_PT < $has_grants_total ? 100 : Math::percent($has_grants_total, $this->latest_grants->enrollment_PT);

            $has_grants_breakdown = [
                'none' => round(100 - $has_grants_percent, 1),
                'more_than_zero' => $has_grants_percent,
            ];

            foreach (
                [
                    'less_than_half' => 'less_than_half_PT',
                    'more_than_half_less_than_full' => 'more_than_half_less_than_full_PT',
                    'full' => 'full_tuition_PT',
                    'more_than_full' => 'more_than_full_PT',
                ] as $key => $db_field
            ) {
                $has_grants_breakdown[$key] = $this->latest_grants->{$db_field} ? round(($this->latest_grants->{$db_field} / $has_grants_total * $has_grants_percent), 1) : 0;
            }

            return $has_grants_breakdown;
        });
    }
}
