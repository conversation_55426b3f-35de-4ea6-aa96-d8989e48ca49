name: Auto Merge Main to Staging

on:
  push:
    branches:
      - main

jobs:
  merge-main-to-staging:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}

      - name: Configure Git
        run: |
          git config user.name "GitHub Actions Bot"
          git config user.email "<EMAIL>"

      - name: Merge main into staging
        run: |
          git checkout staging
          git pull
          git merge --no-ff origin/main -m "Auto-merge main into staging"
          git push origin staging