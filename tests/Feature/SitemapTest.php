<?php

use App\Announcement;
use App\Guide;
use App\Podcast;
use App\School;
use App\User;

use function Pest\Laravel\be;
use function Pest\Laravel\get;

test('sitemap', function () {
    $article = Announcement::factory()->create();
    $guide = Guide::factory()->create();
    $podcast = Podcast\Episode::factory()->create();
    $school = School::factory()->create();
    $user = User::factory()->create();

    get('/sitemap')
        ->assertOk()
        ->assertSeeInOrder([
            'Home',
            'Browse law schools',
            'Compare law schools',
            'Law schools by state job placement',
            'Build your personal report',
            'Legal Career Compass',
            'Trends',
            'Cost of attendance',
            'Enrollment and admission standards',
            'Job outcomes and salaries',
            'Announcements',
            $article->title,
            'Podcasts',
            'I Am The Law Podcast',
            'Women In The Law Podcast',
            'About',
            'Honors and awards',
            'Impact',
            'Press and scholarly citations',
            'Progress',
            'Publications',
            'Speaking engagements',
            'Team',
            'Vision',
            'Help',
            'Cookie policy',
            'Privacy policy',
            'Terms of use',
        ]);

    be($user)->get('/sitemap')
        ->assertOk()
        ->assertSeeInOrder([
            'Your law schools list',
            'Budgets',
            'Settings',
        ]);

    get('/sitemap.xml')
        ->assertOk()
        ->assertSeeInOrder([
            url('/schools'),
            url("/schools/{$school->slug}"),
            url("/schools/{$school->slug}/jobs"),
            url("/schools/{$school->slug}/admissions"),
            url("/schools/{$school->slug}/salaries"),
            url("/schools/{$school->slug}/bar"),
            url("/schools/{$school->slug}/costs"),
            url("/schools/{$school->slug}/environment"),
            url("/schools/{$school->slug}/pt"),
            url("/schools/{$school->slug}/aba"),
            url('/compare'),
            url('/states'),
            url('/report'),
            url('/compass'),
            url('/trends'),
            url('/trends/tuition'),
            url('/trends/tuition-vs-job-outcomes'),
            url('/trends/tuition-vs-bar-results'),
            url('/trends/tuition-per-school'),
            url('/trends/net-tuition'),
            url('/trends/net-tuition-vs-job-outcomes'),
            url('/trends/net-tuition-vs-bar-results'),
            url('/trends/net-tuition-per-school'),
            url('/trends/debt'),
            url('/trends/debt-vs-job-outcomes'),
            url('/trends/debt-vs-bar-results'),
            url('/trends/projected-debt'),
            url('/trends/debt-per-law-school'),
            url('/trends/investments'),
            url('/trends/investment-vs-job-outcomes'),
            url('/trends/investment-vs-bar-results'),
            url('/trends/investment-per-law-school'),
            url('/trends/debt-to-income'),
            url('/trends/debt-service-vs-income'),
            url('/trends/debt-to-income-per-school'),
            url('/trends/scholarships'),
            url('/trends/cost-of-living'),
            url('/trends/enrollment'),
            url('/trends/enrollment-by-state'),
            url('/trends/enrollment-per-school'),
            url('/trends/admissions-standards'),
            url('/trends/legal-jobs'),
            url('/trends/job-outcomes-vs-schools'),
            url('/trends/salaries'),
            url('/trends/the-job-search'),
            url('/announcements'),
            url("/announcements/{$article->slug}"),
            url('/podcasts'),
            url('/podcasts/iatl'),
            url("/podcasts/iatl/{$podcast->id}"),
            url('/podcasts/witl'),
            url('/podcasts/witl/sexism'),
            url('/podcasts/witl/media'),
            url('/podcasts/witl/lp1'),
            url('/podcasts/witl/lp2'),
            url('/podcasts/witl/intersectionality'),
            url('/podcasts/witl/solutions'),
            url('/about'),
            url('/about/awards'),
            url('/about/impact'),
            url('/about/press'),
            url('/about/vision/progress'),
            url('/about/publications'),
            url('/about/speaking'),
            url('/about/team'),
            url('/about/vision'),
            url('/help'),
            url("/help/{$guide->slug}"),
            url('/cookie-policy'),
            url('/terms'),
        ]);
});
