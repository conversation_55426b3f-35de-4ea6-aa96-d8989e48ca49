<?php

use App\User;

use function Pest\Laravel\be;
use function Pest\Laravel\call;

$lcc = (object) [
    'title' => 'Legal Career Compass',
    'heading' => 'Guide students to find their fit for law',
    'button' => 'Contact sales',
    'cta' => 'Contact sales',
    'upsell' => 'Contact sales',
    'pay' => null,
    'url' => fn () => config('compass.url'),
];

dataset('sites', [
    'lcc' => $lcc,
    'lst' => (object) [
        'title' => 'Legal Career Compass',
        'heading' => 'Understand your fit for law',
        'button' => 'Get started for $39 + tax',
        'cta' => 'Get started for $39 + tax',
        'upsell' => 'Purchase Premium Reports Today for $39 (one-time fee)',
        'pay' => ['compass-pay'],
        'url' => fn () => config('app.url') . '/compass',
    ],
]);

test('compass guest', function ($site) {
    call('GET', ($site->url)())
        ->assertOk()
        ->assertSee($site->title)
        ->assertSeeInOrder(explode(' ', $site->heading))
        ->assertSeeInOrder(Arr::pluck(config('compass.toolkits'), 'name'))
        ->assertSee($site->button)
        ->assertSee('View demo')
        ->assertDontSee(Arr::pluck(config('compass.reports'), 'name'))
        ->assertDontSee('Our records indicate that you took an older version')
        ->assertDontSee('Start the assessment');

    expect(DB::hits())->toBe(0);
})->with('sites');

test('compass premium', function ($site) {
    $user = User::factory()->compass('premium')->create([
        'lcc_id' => null,
        'lcc_latest' => null,
        'lcc_token' => null,
    ]);

    be($user)->call('GET', ($site->url)())
        ->assertOk()
        ->assertSee($site->title)
        ->assertSeeInOrder(explode(' ', $site->heading))
        ->assertSee('Start the assessment')
        ->assertSee('View demo')
        ->assertDontSee('Create an account')
        ->assertDontSee('Our records indicate that you took an older version')
        ->assertDontSee($site->cta);
})->with('sites');

test('compass completed premium', function ($site) {
    $user = User::factory()->compass('premium')->create();

    be($user)->call('GET', ($site->url)())
        ->assertOk()
        ->assertSee($site->title)
        ->assertSeeInOrder(explode(' ', $site->heading))
        ->assertSeeInOrder(Arr::pluck(config('compass.reports'), 'name'))
        ->assertSeeInOrder(Arr::pluck(config('compass.toolkits'), 'name'))
        ->assertDontSee('Our records indicate that you took an older version')
        ->assertDontSee('Create an account')
        ->assertDontSee($site->cta)
        ->assertDontSee('Start the assessment')
        ->assertDontSee('View demo');
})->with('sites');

test('compass needs to retake', function ($site) {
    $user = User::factory()->compass('premium')->create([
        'lcc_id' => 100000,
        'lcc_latest' => null,
    ]);

    be($user)->call('GET', ($site->url)())
        ->assertOk()
        ->assertSee($site->title)
        ->assertSeeInOrder(explode(' ', $site->heading))
        ->assertSee('Our records indicate that you took an older version')
        ->assertSee('Start the assessment')
        ->assertSee('View demo')
        ->assertDontSee('Create an account')
        ->assertDontSee($site->cta);
})->with('sites');

test('compass user', function ($site) {
    $user = User::factory()->create();

    be($user)->call('GET', ($site->url)())
        ->assertOk()
        ->assertSee($site->title)
        ->assertSeeInOrder(explode(' ', $site->heading))
        ->assertSeeInOrder(Arr::pluck(config('compass.toolkits'), 'name'))
        ->assertSee($site->button)
        ->assertSee('View demo')
        ->assertSee($site->pay)
        ->assertDontSee(Arr::pluck(config('compass.reports'), 'name'))
        ->assertDontSee('Create an account')
        ->assertDontSee('Start the assessment')
        ->assertDontSee('View toolkit');
})->with('sites');

test('compass assessments with data set "lcc"', function () use ($lcc) {
    $user = User::factory()->create();

    be($user)->call('GET', ($lcc->url)())
        ->assertOk()
        ->assertSee($lcc->heading);
});

test('compass toolkits with data set "lcc"', function () use ($lcc) {
    $user = User::factory()->create();

    be($user)->call('GET', ($lcc->url)())
        ->assertOk()
        ->assertSee($lcc->heading);
});

test('compass assessments premium with data set "lcc"', function () use ($lcc) {
    $user = User::factory()->compass('premium')->create([
        'lcc_id' => 100000,
        'lcc_latest' => null,
    ]);

    be($user)->call('GET', ($lcc->url)())
        ->assertOk()
        ->assertSeeInOrder(['/assessments', '/toolkits'])
        ->assertSee($lcc->heading);
});

test('compass toolkits premium with data set "lcc"', function () use ($lcc) {
    $user = User::factory()->compass('premium')->create([
        'lcc_id' => 100000,
        'lcc_latest' => null,
    ]);

    be($user)->call('GET', ($lcc->url)())
        ->assertOk()
        ->assertSeeInOrder(['/assessments', '/toolkits'])
        ->assertSee($lcc->heading);
});

test('compass assessments completed premium with data set "lcc"', function () use ($lcc) {
    $user = User::factory()->compass('premium')->create();

    be($user)->call('GET', ($lcc->url)())
        ->assertOk()
        ->assertSee('Your assessments')
        ->assertSeeInOrder(['/assessments', '/toolkits'])
        ->assertSeeInOrder(Arr::pluck(config('compass.reports'), 'name'))
        ->assertSee('View report');
});

test('compass toolkits completed premium with data set "lcc"', function () use ($lcc) {
    $user = User::factory()->compass('premium')->create();

    be($user)->call('GET', ($lcc->url)())
        ->assertOk()
        ->assertSee('Your toolkits')
        ->assertSeeInOrder(['/assessments', '/toolkits'])
        ->assertSeeInOrder(Arr::pluck(config('compass.toolkits'), 'name'))
        ->assertSee('View toolkit');
});
