<?php

use App\User;

use function Pest\Laravel\be;

test('account nav', function () {
    $user = User::factory()->compass()->admin()->create();

    be($user)->get('/settings')
        ->assertOk()
        ->assertSeeInOrder([
            'Account profile',
            'Education',
            'Finances',
            'Job preferences',
            'Change password',
            'Delete account',
        ]);
});
