<?php

use App\Http\Requests\TrendRequest;

use function Pest\Laravel\get;

test('national', function () {
    get(route('trends'))
        ->assertOk()
        ->assertSee('Trends');

    expect(DB::hits())->toBe(0);
});

test('costs', function () {
    get(route('trends.costs'))
        ->assertOk()
        ->assertSee('Tuition');
});

test('costs tuition', function () {
    get(route('trends.costs.tuition'))
        ->assertOk()
        ->assertSee('Tuition');
});

test('costs tuition scope bar', function () {
    get(route('trends.costs.tuition.bar'))
        ->assertOk()
        ->assertSee('Tuition')
        ->assertSee('bar');
});

test('costs tuition scope jobs', function () {
    get(route('trends.costs.tuition.jobs'))
        ->assertOk()
        ->assertSee('Tuition')
        ->assertSee('jobs');
});

test('costs tuition scope schools', function () {
    get(route('trends.costs.tuition.schools'))
        ->assertOk()
        ->assertSee('Tuition')
        ->assertSee('schools');
});

test('costs net tuition', function () {
    get(route('trends.costs.net-tuition'))
        ->assertOk()
        ->assertSee('Net Tuition');
});

test('costs net tuition scope bar', function () {
    get(route('trends.costs.net-tuition.bar'))
        ->assertOk()
        ->assertSee('Net Tuition')
        ->assertSee('bar');
});

test('costs net tuition scope jobs', function () {
    get(route('trends.costs.net-tuition.jobs'))
        ->assertOk()
        ->assertSee('Net Tuition')
        ->assertSee('jobs');
});

test('costs net tuition scope schools', function () {
    get(route('trends.costs.net-tuition.schools'))
        ->assertOk()
        ->assertSee('Net Tuition')
        ->assertSee('schools');
});

test('costs conditional scholarships', function () {
    get(route('trends.costs.conditional-scholarships'))
        ->assertOk()
        ->assertSee('Conditional Scholarships');
});

test('costs living expenses', function () {
    get(route('trends.costs.living-expenses'))
        ->assertOk()
        ->assertSee('Living Expenses');
});

test('costs debt', function () {
    get(route('trends.costs.debt'))
        ->assertOk()
        ->assertSee('Debt');
});

test('costs debt scope jobs', function () {
    get(route('trends.costs.debt.jobs'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('jobs');
});

test('costs debt scope bar', function () {
    get(route('trends.costs.debt.bar'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('bar');
});

test('costs debt scope projected', function () {
    get(route('trends.costs.debt.projected'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('projected');
});

test('costs debt scope schools', function () {
    get(route('trends.costs.debt.schools'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('schools');
});

test('costs debt income', function () {
    get(route('trends.costs.debt-income'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('total');
});

test('costs debt income scope repayment', function () {
    get(route('trends.costs.debt-income.repayment'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('repayment');
});

test('costs debt income scope schools', function () {
    get(route('trends.costs.debt-income.schools'))
        ->assertOk()
        ->assertSee('Debt')
        ->assertSee('schools');
});

test('costs federal investment', function () {
    get(route('trends.costs.federal-investment'))
        ->assertOk()
        ->assertSee('Federal Investment');
});

test('costs federal investment scope jobs', function () {
    get(route('trends.costs.federal-investment.jobs'))
        ->assertOk()
        ->assertSee('Federal Investment')
        ->assertSee('jobs');
});

test('costs federal investment scope bar', function () {
    get(route('trends.costs.federal-investment.bar'))
        ->assertOk()
        ->assertSee('Federal Investment')
        ->assertSee('bar');
});

test('costs federal investment scope schools', function () {
    get(route('trends.costs.federal-investment.schools'))
        ->assertOk()
        ->assertSee('Federal Investment')
        ->assertSee('schools');
});

test('enrollment', function () {
    get(route('trends.enrollment'))
        ->assertOk()
        ->assertSee('Law School Enrollment Trends');
});

test('enrollment all', function () {
    get(route('trends.enrollment.all'))
        ->assertOk()
        ->assertSee('Law School Enrollment Trends');
});

test('enrollment all scope schools', function () {
    get(route('trends.enrollment.all.schools'))
        ->assertOk()
        ->assertSee('schools');
});

test('enrollment all scope state', function () {
    get(route('trends.enrollment.all.state'))
        ->assertOk()
        ->assertSee('state');
});

test('enrollment admissions standards', function () {
    get(route('trends.enrollment.admissions-standards'))
        ->assertOk()
        ->assertSee('Admissions Standards');
});

test('jobs', function () {
    get(route('trends.jobs'))
        ->assertOk()
        ->assertSee('Legal Jobs');
});

test('jobs legal jobs', function () {
    get(route('trends.jobs.legal-jobs'))
        ->assertOk()
        ->assertSee('Legal Jobs');
});

test('jobs legal jobs scope schools', function () {
    get(route('trends.jobs.legal-jobs.schools'))
        ->assertOk()
        ->assertSee('Legal Jobs')
        ->assertSee('schools');
});

test('jobs salaries', function () {
    get(route('trends.jobs.salaries'))
        ->assertOk()
        ->assertSee('Salaries');
});

test('jobs the job search', function () {
    get(route('trends.jobs.the-job-search'))
        ->assertOk()
        ->assertSee('The Job Search');
});

test('trends rules', function () {
    $rules = (new TrendRequest())->rules();

    expect(Validator::make(['firm_size' => 100], $rules)->passes())->toBeTrue();
    expect(Validator::make(['scope' => 'lsat_50'], $rules)->passes())->toBeTrue();
    expect(Validator::make(['y1' => 2020], $rules)->passes())->toBeTrue();
    expect(Validator::make(['y2' => 2022], $rules)->passes())->toBeTrue();

    expect(Validator::make(['firm_size' => Str::random()], $rules)->passes())->toBeFalse();
    expect(Validator::make(['scope' => Str::random()], $rules)->passes())->toBeFalse();
    expect(Validator::make(['y1' => Str::random()], $rules)->passes())->toBeFalse();
    expect(Validator::make(['y2' => Str::random()], $rules)->passes())->toBeFalse();
});

test('trends dropdown options', function () {
    $trendRequest = new TrendRequest();

    $items = ['Item 1', 'Item 2', 'Item 3'];
    $value = 'Item 2';

    $expectedOptions = "<option >Item 1</option>\n<option selected>Item 2</option>\n<option >Item 3</option>";

    $options = $trendRequest->options($items, $value);

    expect($options)->toBe($expectedOptions);
});

test('redirect costs tuition scope bar', function () {
    get(route('trends.costs.tuition', ['scope' => 'bar']))
        ->assertRedirectToRoute('trends.costs.tuition.bar');
});

test('redirect costs tuition scope jobs', function () {
    get(route('trends.costs.tuition', ['scope' => 'jobs']))
        ->assertRedirectToRoute('trends.costs.tuition.jobs');
});

test('redirect costs tuition scope national', function () {
    get(route('trends.costs.tuition', ['scope' => 'national']))
        ->assertRedirectToRoute('trends.costs.tuition');
});

test('redirect costs tuition scope schools', function () {
    get(route('trends.costs.tuition', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.costs.tuition.schools');
});

test('redirect costs net tuition scope bar', function () {
    get(route('trends.costs.net-tuition', ['scope' => 'bar']))
        ->assertRedirectToRoute('trends.costs.net-tuition.bar');
});

test('redirect costs net tuition scope jobs', function () {
    get(route('trends.costs.net-tuition', ['scope' => 'jobs']))
        ->assertRedirectToRoute('trends.costs.net-tuition.jobs');
});

test('redirect costs net tuition scope national', function () {
    get(route('trends.costs.net-tuition', ['scope' => 'national']))
        ->assertRedirectToRoute('trends.costs.net-tuition');
});

test('redirect costs net tuition scope schools', function () {
    get(route('trends.costs.net-tuition', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.costs.net-tuition.schools');
});

test('redirect costs debt scope jobs', function () {
    get(route('trends.costs.debt', ['scope' => 'jobs']))
        ->assertRedirectToRoute('trends.costs.debt.jobs');
});

test('redirect costs debt scope bar', function () {
    get(route('trends.costs.debt', ['scope' => 'bar']))
        ->assertRedirectToRoute('trends.costs.debt.bar');
});

test('redirect costs debt scope national', function () {
    get(route('trends.costs.debt', ['scope' => 'national']))
        ->assertRedirectToRoute('trends.costs.debt');
});

test('redirect costs debt scope projected', function () {
    get(route('trends.costs.debt', ['scope' => 'projected']))
        ->assertRedirectToRoute('trends.costs.debt.projected');
});

test('redirect costs debt scope schools', function () {
    get(route('trends.costs.debt', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.costs.debt.schools');
});

test('redirect costs debt income scope repayment', function () {
    get(route('trends.costs.debt-income', ['scope' => 'repayment']))
        ->assertRedirectToRoute('trends.costs.debt-income.repayment');
});

test('redirect costs debt income scope schools', function () {
    get(route('trends.costs.debt-income', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.costs.debt-income.schools');
});

test('redirect costs debt income scope total', function () {
    get(route('trends.costs.debt-income', ['scope' => 'total']))
        ->assertRedirectToRoute('trends.costs.debt-income');
});

test('redirect costs federal investment scope jobs', function () {
    get(route('trends.costs.federal-investment', ['scope' => 'jobs']))
        ->assertRedirectToRoute('trends.costs.federal-investment.jobs');
});

test('redirect costs federal investment scope bar', function () {
    get(route('trends.costs.federal-investment', ['scope' => 'bar']))
        ->assertRedirectToRoute('trends.costs.federal-investment.bar');
});

test('redirect costs federal investment scope national', function () {
    get(route('trends.costs.federal-investment', ['scope' => 'national']))
        ->assertRedirectToRoute('trends.costs.federal-investment');
});

test('redirect costs federal investment scope schools', function () {
    get(route('trends.costs.federal-investment', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.costs.federal-investment.schools');
});

test('redirect enrollment all scope national', function () {
    get(route('trends.enrollment.all', ['scope' => 'national']))
        ->assertRedirectToRoute('trends.enrollment.all');
});

test('redirect enrollment all scope national with years', function () {
    get(route('trends.enrollment.all', ['scope' => 'national', 'y1' => 2020, 'y2' => 2022]))
        ->assertRedirectToRoute('trends.enrollment.all', ['y1' => 2020, 'y2' => 2022]);
});

test('redirect enrollment all scope schools', function () {
    get(route('trends.enrollment.all', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.enrollment.all.schools');
});

test('redirect enrollment all scope schools with years', function () {
    get(route('trends.enrollment.all', ['scope' => 'schools', 'y1' => 2020, 'y2' => 2022]))
        ->assertRedirectToRoute('trends.enrollment.all.schools', ['y1' => 2020, 'y2' => 2022]);
});

test('redirect enrollment all scope state', function () {
    get(route('trends.enrollment.all', ['scope' => 'state']))
        ->assertRedirectToRoute('trends.enrollment.all.state');
});

test('redirect enrollment all scope state with years', function () {
    get(route('trends.enrollment.all', ['scope' => 'state', 'y1' => 2020, 'y2' => 2022]))
        ->assertRedirectToRoute('trends.enrollment.all.state', ['y1' => 2020, 'y2' => 2022]);
});

test('redirect jobs legal jobs scope schools', function () {
    get(route('trends.jobs.legal-jobs', ['scope' => 'schools']))
        ->assertRedirectToRoute('trends.jobs.legal-jobs.schools');
});

test('redirect paths', function () {
    get('/trends/costs/conditional-scholarships')
        ->assertRedirect('/trends/scholarships');

    get('/trends/costs/debt')
        ->assertRedirect('/trends/debt');

    get('/trends/costs/debt-income')
        ->assertRedirect('/trends/debt-to-income');

    get('/trends/costs/debt-income/repayment')
        ->assertRedirect('/trends/debt-service-vs-income');

    get('/trends/costs/debt-income/schools')
        ->assertRedirect('/trends/debt-to-income-per-school');

    get('/trends/costs/debt/bar')
        ->assertRedirect('/trends/debt-vs-bar-results');

    get('/trends/costs/debt/jobs')
        ->assertRedirect('/trends/debt-vs-job-outcomes');

    get('/trends/costs/debt/projected')
        ->assertRedirect('/trends/projected-debt');

    get('/trends/costs/debt/schools')
        ->assertRedirect('/trends/debt-per-law-school');

    get('/trends/costs/federal-investment')
        ->assertRedirect('/trends/investments');

    get('/trends/costs/federal-investment/bar')
        ->assertRedirect('/trends/investment-vs-bar-results');

    get('/trends/costs/federal-investment/jobs')
        ->assertRedirect('/trends/investment-vs-job-outcomes');

    get('/trends/costs/federal-investment/schools')
        ->assertRedirect('/trends/investment-per-law-school');

    get('/trends/costs/living-expenses')
        ->assertRedirect('/trends/cost-of-living');

    get('/trends/costs/net-tuition')
        ->assertRedirect('/trends/net-tuition');

    get('/trends/costs/net-tuition/bar')
        ->assertRedirect('/trends/net-tuition-vs-bar-results');

    get('/trends/costs/net-tuition/jobs')
        ->assertRedirect('/trends/net-tuition-vs-job-outcomes');

    get('/trends/costs/net-tuition/schools')
        ->assertRedirect('/trends/net-tuition-per-school');

    get('/trends/costs/tuition')
        ->assertRedirect('/trends/tuition');

    get('/trends/costs/tuition/bar')
        ->assertRedirect('/trends/tuition-vs-bar-results');

    get('/trends/costs/tuition/jobs')
        ->assertRedirect('/trends/tuition-vs-job-outcomes');

    get('/trends/costs/tuition/schools')
        ->assertRedirect('/trends/tuition-per-school');

    get('/trends/enrollment/admissions-standards')
        ->assertRedirect('/trends/admissions-standards');

    get('/trends/enrollment/all/schools')
        ->assertRedirect('/trends/enrollment-per-school');

    get('/trends/enrollment/all/state')
        ->assertRedirect('/trends/enrollment-by-state');

    get('/trends/jobs/legal-jobs')
        ->assertRedirect('/trends/legal-jobs');

    get('/trends/jobs/legal-jobs/schools')
        ->assertRedirect('/trends/job-outcomes-vs-schools');

    get('/trends/jobs/salaries')
        ->assertRedirect('/trends/salaries');

    get('/trends/jobs/the-job-search')
        ->assertRedirect('/trends/the-job-search');

    get('/trends/transparency/nalp-report-database')
        ->assertRedirect('/archive/2018/nalp-report-database');
});

test('redirect scopes', function () {
    get('/trends/costs/tuition?scope=jobs')
        ->assertRedirect('/trends/tuition-vs-job-outcomes');

    get('/trends/costs/tuition?scope=bar')
        ->assertRedirect('/trends/tuition-vs-bar-results');

    get('/trends/costs/tuition?scope=schools')
        ->assertRedirect('/trends/tuition-per-school');

    get('/trends/costs/net-tuition?scope=jobs')
        ->assertRedirect('/trends/net-tuition-vs-job-outcomes');

    get('/trends/costs/net-tuition?scope=bar')
        ->assertRedirect('/trends/net-tuition-vs-bar-results');

    get('/trends/costs/net-tuition?scope=schools')
        ->assertRedirect('/trends/net-tuition-per-school');

    get('/trends/costs/debt?scope=jobs')
        ->assertRedirect('/trends/debt-vs-job-outcomes');

    get('/trends/costs/debt?scope=bar')
        ->assertRedirect('/trends/debt-vs-bar-results');

    get('/trends/costs/debt?scope=projected')
        ->assertRedirect('/trends/projected-debt');

    get('/trends/costs/debt?scope=schools')
        ->assertRedirect('/trends/debt-per-law-school');

    get('/trends/costs/federal-investment?scope=jobs')
        ->assertRedirect('/trends/investment-vs-job-outcomes');

    get('/trends/costs/federal-investment?scope=bar')
        ->assertRedirect('/trends/investment-vs-bar-results');

    get('/trends/costs/federal-investment?scope=schools')
        ->assertRedirect('/trends/investment-per-law-school');

    get('/trends/costs/debt-income?scope=total')
        ->assertRedirect('/trends/debt-to-income');

    get('/trends/costs/debt-income?scope=repayment')
        ->assertRedirect('/trends/debt-service-vs-income');

    get('/trends/costs/debt-income?scope=schools')
        ->assertRedirect('/trends/debt-to-income-per-school');
});

test('validation enrollment all', function () {
    get(route('trends.enrollment.all', ['y1' => 'abc', 'y2' => 'def']))
        ->assertRedirectToRoute('trends.enrollment.all')
        ->assertSessionHasErrors([
            'y1' => 'The y1 must be a number.',
            'y2' => 'The y2 must be a number.',
        ]);
});

test('validation enrollment all scope schools', function () {
    get(route('trends.enrollment.all.schools', ['y1' => 'abc', 'y2' => 'def']))
        ->assertRedirectToRoute('trends.enrollment.all.schools')
        ->assertSessionHasErrors([
            'y1' => 'The y1 must be a number.',
            'y2' => 'The y2 must be a number.',
        ]);
});

test('validation enrollment all scope state', function () {
    get(route('trends.enrollment.all.state', ['y1' => 'abc', 'y2' => 'def']))
        ->assertRedirectToRoute('trends.enrollment.all.state')
        ->assertSessionHasErrors([
            'y1' => 'The y1 must be a number.',
            'y2' => 'The y2 must be a number.',
        ]);
});

test('validation jobs legal jobs', function () {
    get(route('trends.jobs.legal-jobs', ['firm_size' => 'abc']))
        ->assertRedirectToRoute('trends.jobs.legal-jobs')
        ->assertSessionHasErrors([
            'firm_size' => 'The firm size must be a number.',
        ]);
});

test('validation jobs salaries', function () {
    get(route('trends.jobs.salaries', ['y1' => 'abc']))
        ->assertRedirectToRoute('trends.jobs.salaries')
        ->assertSessionHasErrors([
            'y1' => 'The y1 must be a number.',
        ]);
});
