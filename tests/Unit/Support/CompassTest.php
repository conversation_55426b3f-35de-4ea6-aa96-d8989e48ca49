<?php

use App\Exceptions\CompassException;
use App\Support\Compass;
use App\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Request;

test('compass create updates user attrs', function () {
    $user = User::factory()->create();

    $id = fake()->randomNumber(5, true);
    $token = fake()->regexify('[A-Za-z0-9]{60}');
    $now = now()->setMicrosecond(0);

    Http::fake(['https://app.legalcareercompass.org/api/user' => Http::response([
        'data' => [
            'id' => $id,
            'api_token' => $token,
            'latest_source_at' => $now,
        ],
    ])]);

    expect(Compass::create($user))->toBeNull();

    $user->refresh();

    expect($user->lcc_id)->toEqual($id);
    expect($user->lcc_token)->toEqual($token);
    expect($user->lcc_latest)->toEqual($now);
});

test('compass create fails for existing users', function () {
    $user = User::factory()->compass()->create(['email' => '<EMAIL>']);

    Http::fake(['https://app.legalcareercompass.org/api/user' => Http::response([
        'message' => 'The given data was invalid.',
        'errors' => ['email' => ['The email has already been taken.']],
    ])]);

    expect(Compass::create($user))->toBeNull();
})->throws(
    CompassException::class,
    "You've already used your email (<EMAIL>) with our partner. " .
    '<NAME_EMAIL> and we will link your account for you.',
);

test('compass fakes api for playwright', function () {
    $user = User::factory()->create();

    Request::instance()->headers->set('User-Agent', 'Playwright/1');

    expect(Compass::create($user))->toBeNull();
    expect(Compass::update($user))->toBeNull();
    expect(Compass::token($user))->toBeString();
});

test('compass token returns token', function () {
    $user = User::factory()->compass()->create();

    $token = fake()->regexify('[A-Za-z0-9]{60}');

    Http::fake(['https://app.legalcareercompass.org/api/user-token' => Http::response([
        'token' => $token,
    ])]);

    expect(Compass::token($user))->toEqual($token);
});

test('compass update bumps timestamp', function () {
    $user = User::factory()->compass()->create();

    $now = now()->setMicrosecond(0);

    Http::fake(["https://app.legalcareercompass.org/api/user/{$user->lcc_id}" => Http::response([
        'data' => [
            'id' => $user->lcc_id,
            'api_token' => $user->lcc_token,
            'latest_source_at' => $now,
        ],
    ])]);

    expect(Compass::update($user))->toBeNull();

    $user->refresh();

    expect($user->lcc_latest)->toEqual($now);
});

test('compass verify validates signature', function () {
    $payload = [];
    $signature = hash_hmac('sha256', json_encode($payload), config('compass.secret'));

    $request = Request::create('', 'POST', $payload);
    $request->headers->set('Signature', $signature);

    expect(Compass::verify($request))->toBe(true);

    $request = Request::create('', 'POST', $payload);
    $request->headers->set('Signature', 'invalid signature');

    expect(Compass::verify($request))->toBe(false);
});
