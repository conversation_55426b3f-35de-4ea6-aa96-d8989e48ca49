<?php

test()
    ->expect(['dd', 'dump', 'var_dump'])
    ->not->toBeUsed();

test()
    ->expect('App\Concerns')
    ->toBeTraits();

test()
    ->expect('App\Console\Commands')
    ->toExtend('Illuminate\Console\Command');

test()
    ->expect('App\Console\Commands')
    ->toHaveSuffix('Command');

test()
    ->expect('App\Enums')
    ->toBeEnums();

test()
    ->expect('App\Exceptions')
    ->toExtend('Exception')
    ->ignoring('App\Exceptions\Handler');

test()
    ->expect('App\Exceptions')
    ->toHaveSuffix('Exception')
    ->ignoring('App\Exceptions\Handler');

test()
    ->expect('App\Http\Controllers')
    ->classes()
    ->toExtend('App\Http\Controllers\Controller')
    ->ignoring('App\Http\Controllers\Auth\SamlController');

test()
    ->expect('App\Http\Controllers')
    ->classes()
    ->toHaveSuffix('Controller');

test()
    ->expect('App\Support')
    ->toExtendNothing()
    ->ignoring('App\Support\Asset');
