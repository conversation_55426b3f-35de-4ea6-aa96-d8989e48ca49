<?php

use App\Podcast;
use App\Support\Asset;

test('host', function () {
    $host = Podcast\Host::factory()->hasPodcasts()->create([
        'image' => new Asset('public', 'avatars/test.png'),
    ]);

    expect($host->avatar)->toBe(Storage::url('avatars/test.png'));
    expect($host->headline)->toBe("{$host->name}, {$host->affiliation}");
    expect($host->podcasts)->not->toBeEmpty();
    expect($host->url)->toBe(route('podcasts.iatl.hosts.show', $host->slug));

    expect($host->delete())->toBe(true);
});
