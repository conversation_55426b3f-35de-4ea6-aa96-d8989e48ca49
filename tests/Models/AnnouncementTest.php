<?php

use App\Announcement;
use App\Support\Asset;

test('announcement', function () {
    $announcement = Announcement::factory()->create();

    expect($announcement)->toBeObject();
    expect($announcement->url)->toBe(route('announcements.show', $announcement->slug));

    expect($announcement->delete())->toBe(true);
});

test('announcement image', function () {
    $announcement = Announcement::factory()->create([
        'slug' => 'test',
    ]);

    expect($announcement->image)->toBeNull();

    $announcement->image = new Asset('public', 'path/to/file');
    expect($announcement->image)->not->toBeNull();

    $announcement->image = [new Asset('public', 'path/to/file')];
    expect($announcement->image)->not->toBeNull();

    $announcement->image = null;
    expect($announcement->image)->toBeNull();

    $announcement->image = 'path/to/file';
})->throws(InvalidArgumentException::class);

test('announcement url', function () {
    $announcement = Announcement::factory()->create([
        'slug' => 'test',
    ]);

    expect($announcement->url)->toBe(route('announcements.show', 'test'));
});
