<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE school_admissions MODIFY admission_year YEAR');
        DB::statement('ALTER TABLE school_attrition MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_baroutcomes_first MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_baroutcomes_ultimate MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_baroutcomes_ultimate MODIFY ultimate_year YEAR');
        DB::statement('ALTER TABLE school_conditionalscholarships MODIFY admission_year YEAR');
        DB::statement('ALTER TABLE school_debts MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_ed_debttoincome MODIFY reporting_year YEAR');
        DB::statement('ALTER TABLE school_ed_studentloans MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_employment_aba MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_employment_locations MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_employment_nalp MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_enrollments MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_enrollments_2011_2016 MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_environment MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_faculty MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_grants MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_nalpreports MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_prices MODIFY academic_year YEAR');
        DB::statement('ALTER TABLE school_salary MODIFY graduation_year YEAR');
        DB::statement('ALTER TABLE school_transfers MODIFY academic_year YEAR');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('ALTER TABLE school_admissions MODIFY admission_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_attrition MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_baroutcomes_first MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_baroutcomes_ultimate MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_baroutcomes_ultimate MODIFY ultimate_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_conditionalscholarships MODIFY admission_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_debts MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_ed_debttoincome MODIFY reporting_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_ed_studentloans MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_employment_aba MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_employment_locations MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_employment_nalp MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_enrollments MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_enrollments_2011_2016 MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_environment MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_faculty MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_grants MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_nalpreports MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_prices MODIFY academic_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_salary MODIFY graduation_year BIGINT UNSIGNED');
        DB::statement('ALTER TABLE school_transfers MODIFY academic_year BIGINT UNSIGNED');
    }
};
