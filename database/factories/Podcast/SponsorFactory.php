<?php

namespace Database\Factories\Podcast;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SponsorFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->company,
            'slug' => fn ($attrs) => Str::slug($attrs['name']),
            'url' => fake()->url,
        ];
    }
}
