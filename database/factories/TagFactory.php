<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class TagFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'label' => fake()->unique()->text(10),
            'slug' => fn ($attrs) => Str::slug($attrs['label']),
            'description' => fake()->paragraph(),
        ];
    }
}
