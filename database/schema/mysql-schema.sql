/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `aba_actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `aba_actions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `school_id` bigint unsigned DEFAULT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '0000-00',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `aba_actions_school_id_index` (`school_id`),
  CONSTRAINT `aba_actions_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `academic_years`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `academic_years` (
  `year` smallint unsigned NOT NULL,
  `inflation_from_previous_year` decimal(5,4) DEFAULT NULL COMMENT 'https://www.minneapolisfed.org/community/financial-and-economic-education/cpi-calculator-information/consumer-price-index-and-inflation-rates-1913',
  `schools_total` int DEFAULT NULL,
  `enr_1Ls` int DEFAULT NULL,
  `enr_JDs` int DEFAULT NULL,
  `enr_nonJDs` int DEFAULT NULL,
  `graduates_total` int DEFAULT NULL,
  `legaljobs_FT` float DEFAULT NULL,
  `legaljobs_total` float DEFAULT NULL,
  `biglaw_total` int DEFAULT NULL,
  `timing_beforeGrad` int DEFAULT NULL,
  `timing_afterGrad` int DEFAULT NULL,
  `timing_beforeBar` int DEFAULT NULL,
  `timing_afterBar` int DEFAULT NULL,
  `timing_unknown` int DEFAULT NULL,
  `source_experience` int DEFAULT NULL,
  `source_fallOCI` int DEFAULT NULL,
  `source_jobFair` int DEFAULT NULL,
  `source_jobPosting` int DEFAULT NULL,
  `source_commercialPosting` int DEFAULT NULL,
  `source_other` int DEFAULT NULL,
  `source_priorJob` int DEFAULT NULL,
  `source_referral` int DEFAULT NULL,
  `source_startedFirm` int DEFAULT NULL,
  `source_initiated` int DEFAULT NULL,
  `source_springOCI` int DEFAULT NULL,
  `source_temp` int DEFAULT NULL,
  `source_oscar` int DEFAULT NULL,
  `source_unknown` int DEFAULT NULL,
  `search_seeking` int DEFAULT NULL,
  `search_notSeeking` int DEFAULT NULL,
  `search_unknown` int DEFAULT NULL,
  `total_tuition_discount` int DEFAULT NULL,
  `schools_publics` int DEFAULT NULL,
  `schools_publics_res_tuition_avg` int DEFAULT NULL,
  `schools_publics_res_tuition_med` int DEFAULT NULL,
  `schools_publics_nonres_tuition_avg` int DEFAULT NULL,
  `schools_publics_nonres_tuition_med` int DEFAULT NULL,
  `schools_privates` int DEFAULT NULL,
  `schools_privates_tuition_avg` int DEFAULT NULL,
  `schools_privates_tuition_med` int DEFAULT NULL,
  `attr_1Ls` int DEFAULT NULL,
  `attr_2Ls` int DEFAULT NULL,
  `attr_3Ls` int DEFAULT NULL,
  `attr_4Ls` int DEFAULT NULL,
  `loan_disb_publics` int DEFAULT NULL,
  `loan_disb_privates` int DEFAULT NULL,
  `staff_tenure` int DEFAULT NULL,
  `staff_tenure_track` int DEFAULT NULL,
  `staff_405c` int DEFAULT NULL,
  `staff_visitors` int DEFAULT NULL,
  `staff_ft_skills` int DEFAULT NULL,
  `staff_ft_writing` int DEFAULT NULL,
  `staff_ft_other` int DEFAULT NULL,
  `staff_pt` int DEFAULT NULL,
  `staff_deans` int DEFAULT NULL,
  `staff_assc_deans` int DEFAULT NULL,
  `staff_asst_deans` int DEFAULT NULL,
  `staff_ft_librarians` int DEFAULT NULL,
  `lsat_june` int DEFAULT NULL,
  `lsat_oct` int DEFAULT NULL,
  `lsat_dec` int DEFAULT NULL,
  `lsat_feb` int DEFAULT NULL,
  `lsat_total` int DEFAULT NULL,
  `applicants` int DEFAULT NULL,
  `admitted` int DEFAULT NULL,
  `applications` int DEFAULT NULL,
  `applicants_139` int DEFAULT NULL,
  `applicants_140_144` int DEFAULT NULL,
  `applicants_145_149` int DEFAULT NULL,
  `applicants_150_154` int DEFAULT NULL,
  `applicants_155_159` int DEFAULT NULL,
  `applicants_160_164` int DEFAULT NULL,
  `applicants_165_169` int DEFAULT NULL,
  `applicants_170_174` int DEFAULT NULL,
  `applicants_175_180` int DEFAULT NULL,
  `college_graduates_women` float DEFAULT NULL,
  `applied_women` float DEFAULT NULL,
  `applied_men` float DEFAULT NULL,
  `admitted_women` float DEFAULT NULL,
  `admitted_men` float DEFAULT NULL,
  `enrolled_women` float DEFAULT NULL,
  `enrolled_men` float DEFAULT NULL,
  `firm_101_250` int DEFAULT NULL,
  `firm_251_500` int DEFAULT NULL,
  `firm_251` int DEFAULT NULL,
  `firm_501` int DEFAULT NULL,
  PRIMARY KEY (`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `academic_years_salaries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `academic_years_salaries` (
  `year` smallint unsigned NOT NULL,
  `ft_median` int DEFAULT NULL,
  `education_median` int DEFAULT NULL,
  `business_median` int DEFAULT NULL,
  `clerk_median` int DEFAULT NULL,
  `govt_median` int DEFAULT NULL,
  `publicinterest_median` int DEFAULT NULL,
  `firm_median` int DEFAULT NULL,
  `firm_2_10_median` int DEFAULT NULL,
  `firm_11_25_median` int DEFAULT NULL,
  `firm_26_50_median` int DEFAULT NULL,
  `firm_51_100_median` int DEFAULT NULL,
  `firm_101_250_median` int DEFAULT NULL,
  `firm_251_500_median` int DEFAULT NULL,
  `firm_501_median` int DEFAULT NULL,
  PRIMARY KEY (`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `action_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_events` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `actionable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `actionable_id` bigint unsigned NOT NULL,
  `target_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned DEFAULT NULL,
  `fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'running',
  `exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `original` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `changes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `action_events_actionable_type_actionable_id_index` (`actionable_type`,`actionable_id`),
  KEY `action_events_batch_id_model_type_model_id_index` (`batch_id`,`model_type`,`model_id`),
  KEY `action_events_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `announcements` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `published_at` datetime DEFAULT NULL,
  `image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `blogs_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `budgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `budgets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `school_id` bigint unsigned NOT NULL,
  `part_time` tinyint(1) DEFAULT NULL,
  `y1_tuition` int DEFAULT NULL,
  `y1_room` int DEFAULT NULL,
  `y1_books` int DEFAULT NULL,
  `y1_transportation` int DEFAULT NULL,
  `y1_other` int DEFAULT NULL,
  `y2_tuition` int DEFAULT NULL,
  `y2_room` int DEFAULT NULL,
  `y2_books` int DEFAULT NULL,
  `y2_transportation` int DEFAULT NULL,
  `y2_other` int DEFAULT NULL,
  `y3_tuition` int DEFAULT NULL,
  `y3_room` int DEFAULT NULL,
  `y3_books` int DEFAULT NULL,
  `y3_transportation` int DEFAULT NULL,
  `y3_other` int DEFAULT NULL,
  `y4_tuition` int DEFAULT NULL,
  `y4_room` int DEFAULT NULL,
  `y4_books` int DEFAULT NULL,
  `y4_transportation` int DEFAULT NULL,
  `y4_other` int DEFAULT NULL,
  `y5_tuition` int DEFAULT NULL,
  `y5_room` int DEFAULT NULL,
  `y5_books` int DEFAULT NULL,
  `y5_transportation` int DEFAULT NULL,
  `y5_other` int DEFAULT NULL,
  `y1_discount` int DEFAULT NULL,
  `y1_contribution_specific` int DEFAULT NULL,
  `y1_contribution_private` int DEFAULT NULL,
  `y2_discount` int DEFAULT NULL,
  `y2_contribution_specific` int DEFAULT NULL,
  `y2_contribution_private` int DEFAULT NULL,
  `y3_discount` int DEFAULT NULL,
  `y3_contribution_specific` int DEFAULT NULL,
  `y3_contribution_private` int DEFAULT NULL,
  `y4_discount` int DEFAULT NULL,
  `y4_contribution_specific` int DEFAULT NULL,
  `y4_contribution_private` int DEFAULT NULL,
  `y5_discount` int DEFAULT NULL,
  `y5_contribution_specific` int DEFAULT NULL,
  `y5_contribution_private` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `worksheets_user_id_school_id_unique` (`user_id`,`school_id`),
  KEY `worksheets_user_id_index` (`user_id`),
  KEY `worksheets_school_id_index` (`school_id`),
  CONSTRAINT `budgets_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`),
  CONSTRAINT `budgets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `citations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `citations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `date` date DEFAULT NULL,
  `alt_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pos` int DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `publication` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lst_cite` int DEFAULT NULL,
  `lst_award` int DEFAULT NULL,
  `lst_publication` int DEFAULT NULL,
  `lst_speaking` int DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `guides`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `guides` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order` tinyint NOT NULL DEFAULT '0',
  `published_at` datetime DEFAULT NULL,
  `excerpt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `guides_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `hosts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `hosts` (
  `user_id` bigint unsigned DEFAULT NULL,
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `affiliation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `hosts_slug_unique` (`slug`),
  UNIQUE KEY `hosts_name_unique` (`name`),
  KEY `hosts_user_id_index` (`user_id`),
  CONSTRAINT `authors_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_field_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_field_attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `attachable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attachable_id` bigint unsigned NOT NULL,
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `disk` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_field_attachments_attachable_type_attachable_id_index` (`attachable_type`,`attachable_id`),
  KEY `nova_field_attachments_url_index` (`url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_notifications` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_pending_field_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_pending_field_attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `draft_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `disk` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_pending_field_attachments_draft_id_index` (`draft_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pathways`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pathways` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pathways_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `podcast_host`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `podcast_host` (
  `podcast_id` bigint unsigned NOT NULL,
  `host_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`podcast_id`,`host_id`),
  KEY `podcast_host_podcast_id_index` (`podcast_id`),
  KEY `podcast_host_host_id_index` (`host_id`),
  CONSTRAINT `author_podcast_author_id_foreign` FOREIGN KEY (`host_id`) REFERENCES `hosts` (`id`),
  CONSTRAINT `author_podcast_podcast_id_foreign` FOREIGN KEY (`podcast_id`) REFERENCES `podcasts` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `podcast_pathway`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `podcast_pathway` (
  `podcast_id` bigint unsigned NOT NULL,
  `pathway_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`podcast_id`,`pathway_id`),
  KEY `podcast_pathway_podcast_id_index` (`podcast_id`),
  KEY `podcast_pathway_pathway_id_index` (`pathway_id`),
  CONSTRAINT `podcast_pathway_pathway_id_foreign` FOREIGN KEY (`pathway_id`) REFERENCES `pathways` (`id`),
  CONSTRAINT `podcast_pathway_podcast_id_foreign` FOREIGN KEY (`podcast_id`) REFERENCES `podcasts` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `podcast_sponsor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `podcast_sponsor` (
  `podcast_id` bigint unsigned NOT NULL,
  `sponsor_id` bigint unsigned NOT NULL,
  KEY `podcast_sponsor_podcast_id_foreign` (`podcast_id`),
  KEY `podcast_sponsor_sponsor_id_foreign` (`sponsor_id`),
  CONSTRAINT `podcast_sponsor_podcast_id_foreign` FOREIGN KEY (`podcast_id`) REFERENCES `podcasts` (`id`),
  CONSTRAINT `podcast_sponsor_sponsor_id_foreign` FOREIGN KEY (`sponsor_id`) REFERENCES `sponsors` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `podcast_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `podcast_tag` (
  `podcast_id` bigint unsigned NOT NULL,
  `tag_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`podcast_id`,`tag_id`),
  KEY `podcast_tag_podcast_id_index` (`podcast_id`),
  KEY `podcast_tag_tag_id_index` (`tag_id`),
  CONSTRAINT `podcast_tag_podcast_id_foreign` FOREIGN KEY (`podcast_id`) REFERENCES `podcasts` (`id`),
  CONSTRAINT `podcast_tag_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `podcasts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `podcasts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guest` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `length` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `audio_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `captivate_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `spotify_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `school_id` bigint unsigned DEFAULT NULL,
  `race_id` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `race_id2` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `race_id3` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `gender_id` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `gender_id2` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `gender_id3` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `combined_statistical_area` bigint unsigned DEFAULT NULL COMMENT 'Defined in meta_definitions',
  `transcript` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `podcasts_school_id_index` (`school_id`),
  CONSTRAINT `podcasts_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_admissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_admissions` (
  `school_id` bigint unsigned NOT NULL,
  `admission_year` smallint unsigned NOT NULL,
  `applicants_FT` int DEFAULT NULL,
  `applicants_PT` int DEFAULT NULL,
  `applications_All` int DEFAULT NULL,
  `offers_FT` int DEFAULT NULL,
  `offers_PT` int DEFAULT NULL,
  `offers_All` int DEFAULT NULL,
  `enrollees_pool_All` int DEFAULT NULL,
  `enrollees_nonpool_All` int DEFAULT NULL,
  `enrollees_All` int DEFAULT NULL,
  `enrollees_FT` int DEFAULT NULL,
  `enrollees_PT` int DEFAULT NULL,
  `lsat_75_All` int DEFAULT NULL,
  `lsat_50_All` int DEFAULT NULL,
  `lsat_25_All` int DEFAULT NULL,
  `lsat_excluded_All` int DEFAULT NULL,
  `lsat_75_FT` int DEFAULT NULL,
  `lsat_50_FT` int DEFAULT NULL,
  `lsat_25_FT` int DEFAULT NULL,
  `lsat_excluded_FT` int DEFAULT NULL,
  `lsat_75_PT` int DEFAULT NULL,
  `lsat_50_PT` int DEFAULT NULL,
  `lsat_25_PT` int DEFAULT NULL,
  `lsat_excluded_PT` int DEFAULT NULL,
  `gpa_75_All` double(8,2) DEFAULT NULL,
  `gpa_50_All` double(8,2) DEFAULT NULL,
  `gpa_25_All` double(8,2) DEFAULT NULL,
  `gpa_excluded_All` int DEFAULT NULL,
  `gpa_75_FT` double(8,2) DEFAULT NULL,
  `gpa_50_FT` double(8,2) DEFAULT NULL,
  `gpa_25_FT` double(8,2) DEFAULT NULL,
  `gpa_excluded_FT` int DEFAULT NULL,
  `gpa_75_PT` double(8,2) DEFAULT NULL,
  `gpa_50_PT` double(8,2) DEFAULT NULL,
  `gpa_25_PT` double(8,2) DEFAULT NULL,
  `gpa_excluded_PT` int DEFAULT NULL,
  `gmat_total` int DEFAULT NULL,
  `act_total` int DEFAULT NULL,
  `gre_total` int DEFAULT NULL,
  `sat_total` int DEFAULT NULL,
  `gmat_75` int DEFAULT NULL,
  `gmat_50` int DEFAULT NULL,
  `gmat_25` int DEFAULT NULL,
  `act_75` int DEFAULT NULL,
  `act_50` int DEFAULT NULL,
  `act_25` int DEFAULT NULL,
  `gre_verbal_75` int DEFAULT NULL,
  `gre_verbal_50` int DEFAULT NULL,
  `gre_verbal_25` int DEFAULT NULL,
  `gre_quant_75` int DEFAULT NULL,
  `gre_quant_50` int DEFAULT NULL,
  `gre_quant_25` int DEFAULT NULL,
  `gre_analytical_75` double(8,2) DEFAULT NULL,
  `gre_analytical_50` double(8,2) DEFAULT NULL,
  `gre_analytical_25` double(8,2) DEFAULT NULL,
  `sat_75` int DEFAULT NULL,
  `sat_50` int DEFAULT NULL,
  `sat_25` int DEFAULT NULL,
  `spanish_lsat_75_all_200` int DEFAULT NULL,
  `spanish_lsat_50_all_200` int DEFAULT NULL,
  `spanish_lsat_25_all_200` int DEFAULT NULL,
  `spanish_lsat_75_all_300` int DEFAULT NULL,
  `spanish_lsat_50_all_300` int DEFAULT NULL,
  `spanish_lsat_25_all_300` int DEFAULT NULL,
  `spanish_lsat_200_total` int DEFAULT NULL,
  `spanish_lsat_300_total` int DEFAULT NULL,
  `gre_total_FT` int DEFAULT NULL,
  `gre_total_PT` int DEFAULT NULL,
  `gre_verbal_75_FT` int DEFAULT NULL,
  `gre_verbal_50_FT` int DEFAULT NULL,
  `gre_verbal_25_FT` int DEFAULT NULL,
  `gre_verbal_75_PT` int DEFAULT NULL,
  `gre_verbal_50_PT` int DEFAULT NULL,
  `gre_verbal_25_PT` int DEFAULT NULL,
  `gre_quant_75_FT` int DEFAULT NULL,
  `gre_quant_50_FT` int DEFAULT NULL,
  `gre_quant_25_FT` int DEFAULT NULL,
  `gre_quant_75_PT` int DEFAULT NULL,
  `gre_quant_50_PT` int DEFAULT NULL,
  `gre_quant_25_PT` int DEFAULT NULL,
  `gre_analytical_75_FT` double(8,2) DEFAULT NULL,
  `gre_analytical_50_FT` double(8,2) DEFAULT NULL,
  `gre_analytical_25_FT` double(8,2) DEFAULT NULL,
  `gre_analytical_75_PT` double(8,2) DEFAULT NULL,
  `gre_analytical_50_PT` double(8,2) DEFAULT NULL,
  `gre_analytical_25_PT` double(8,2) DEFAULT NULL,
  PRIMARY KEY (`school_id`,`admission_year`),
  KEY `school_admissions_school_id_index` (`school_id`),
  KEY `school_admissions_admission_year_index` (`admission_year`),
  CONSTRAINT `school_admissions_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_attrition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_attrition` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `AcadAttrition_OtherHispJD1Total` int DEFAULT NULL,
  `AcadAttrition_OtherHispMenJD1` int DEFAULT NULL,
  `AcadAttrition_OtherHispWomenJD1` int DEFAULT NULL,
  `AcadAttrition_OtherHispAGIJD1` int DEFAULT NULL,
  `AcadAttrition_OtherHispPNRJD1` int DEFAULT NULL,
  `AcadAttrition_OtherHispOthersJD1` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianJD1Total` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianMenJD1` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianWomenJD1` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianAGIJD1` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianPNRJD1` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianOthersJD1` int DEFAULT NULL,
  `AcadAttrition_AsianJD1Total` int DEFAULT NULL,
  `AcadAttrition_AsianMenJD1` int DEFAULT NULL,
  `AcadAttrition_AsianWomenJD1` int DEFAULT NULL,
  `AcadAttrition_AsianAGIJD1` int DEFAULT NULL,
  `AcadAttrition_AsianPNRJD1` int DEFAULT NULL,
  `AcadAttrition_AsianOthersJD1` int DEFAULT NULL,
  `AcadAttrition_BlackJD1Total` int DEFAULT NULL,
  `AcadAttrition_BlackMenJD1` int DEFAULT NULL,
  `AcadAttrition_BlackWomenJD1` int DEFAULT NULL,
  `AcadAttrition_BlackAGIJD1` int DEFAULT NULL,
  `AcadAttrition_BlackPNRJD1` int DEFAULT NULL,
  `AcadAttrition_BlackOthersJD1` int DEFAULT NULL,
  `AcadAttrition_NativeJD1Total` int DEFAULT NULL,
  `AcadAttrition_NativeMenJD1` int DEFAULT NULL,
  `AcadAttrition_NativeWomenJD1` int DEFAULT NULL,
  `AcadAttrition_NativeAGIJD1` int DEFAULT NULL,
  `AcadAttrition_NativePNRJD1` int DEFAULT NULL,
  `AcadAttrition_NativeOthersJD1` int DEFAULT NULL,
  `AcadAttrition_RaceJD1Total` int DEFAULT NULL,
  `AcadAttrition_RaceMenJD1` int DEFAULT NULL,
  `AcadAttrition_RaceWomenJD1` int DEFAULT NULL,
  `AcadAttrition_RaceAGIJD1` int DEFAULT NULL,
  `AcadAttrition_RacePNRJD1` int DEFAULT NULL,
  `AcadAttrition_RaceOthersJD1` int DEFAULT NULL,
  `AcadAttrition_SubTotalJD1Total` int DEFAULT NULL,
  `AcadAttrition_SubTotalMenJD1` int DEFAULT NULL,
  `AcadAttrition_SubTotalWomenJD1` int DEFAULT NULL,
  `AcadAttrition_SubTotalAGIJD1` int DEFAULT NULL,
  `AcadAttrition_SubTotalPNRJD1` int DEFAULT NULL,
  `AcadAttrition_SubTotalOthersJD1` int DEFAULT NULL,
  `AcadAttrition_WhiteJD1Total` int DEFAULT NULL,
  `AcadAttrition_WhiteMenJD1` int DEFAULT NULL,
  `AcadAttrition_WhiteWomenJD1` int DEFAULT NULL,
  `AcadAttrition_WhiteAGIJD1` int DEFAULT NULL,
  `AcadAttrition_WhitePNRJD1` int DEFAULT NULL,
  `AcadAttrition_WhiteOthersJD1` int DEFAULT NULL,
  `AcadAttrition_NRJD1Total` int DEFAULT NULL,
  `AcadAttrition_NRMenJD1` int DEFAULT NULL,
  `AcadAttrition_NRWomenJD1` int DEFAULT NULL,
  `AcadAttrition_NRAGIJD1` int DEFAULT NULL,
  `AcadAttrition_NRPNRJD1` int DEFAULT NULL,
  `AcadAttrition_NROthersJD1` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceJD1Total` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceMenJD1` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceWomenJD1` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceAGIJD1` int DEFAULT NULL,
  `AcadAttrition_UnknownRacePNRJD1` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceOthersJD1` int DEFAULT NULL,
  `AcadAttrition_TotalJD1Total` int DEFAULT NULL,
  `AcadAttrition_TotalMenJD1` int DEFAULT NULL,
  `AcadAttrition_TotalWomenJD1` int DEFAULT NULL,
  `AcadAttrition_TotalAGIJD1` int DEFAULT NULL,
  `AcadAttrition_TotalPNRJD1` int DEFAULT NULL,
  `AcadAttrition_TotalOthersJD1` int DEFAULT NULL,
  `AcadAttrition_OtherHispJD2Total` int DEFAULT NULL,
  `AcadAttrition_OtherHispMenJD2` int DEFAULT NULL,
  `AcadAttrition_OtherHispWomenJD2` int DEFAULT NULL,
  `AcadAttrition_OtherHispAGIJD2` int DEFAULT NULL,
  `AcadAttrition_OtherHispPNRJD2` int DEFAULT NULL,
  `AcadAttrition_OtherHispOthersJD2` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianJD2Total` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianMenJD2` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianWomenJD2` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianAGIJD2` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianPNRJD2` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianOthersJD2` int DEFAULT NULL,
  `AcadAttrition_AsianJD2Total` int DEFAULT NULL,
  `AcadAttrition_AsianMenJD2` int DEFAULT NULL,
  `AcadAttrition_AsianWomenJD2` int DEFAULT NULL,
  `AcadAttrition_AsianAGIJD2` int DEFAULT NULL,
  `AcadAttrition_AsianPNRJD2` int DEFAULT NULL,
  `AcadAttrition_AsianOthersJD2` int DEFAULT NULL,
  `AcadAttrition_BlackJD2Total` int DEFAULT NULL,
  `AcadAttrition_BlackMenJD2` int DEFAULT NULL,
  `AcadAttrition_BlackWomenJD2` int DEFAULT NULL,
  `AcadAttrition_BlackAGIJD2` int DEFAULT NULL,
  `AcadAttrition_BlackPNRJD2` int DEFAULT NULL,
  `AcadAttrition_BlackOthersJD2` int DEFAULT NULL,
  `AcadAttrition_NativeJD2Total` int DEFAULT NULL,
  `AcadAttrition_NativeMenJD2` int DEFAULT NULL,
  `AcadAttrition_NativeWomenJD2` int DEFAULT NULL,
  `AcadAttrition_NativeAGIJD2` int DEFAULT NULL,
  `AcadAttrition_NativePNRJD2` int DEFAULT NULL,
  `AcadAttrition_NativeOthersJD2` int DEFAULT NULL,
  `AcadAttrition_RaceJD2Total` int DEFAULT NULL,
  `AcadAttrition_RaceMenJD2` int DEFAULT NULL,
  `AcadAttrition_RaceWomenJD2` int DEFAULT NULL,
  `AcadAttrition_RaceAGIJD2` int DEFAULT NULL,
  `AcadAttrition_RacePNRJD2` int DEFAULT NULL,
  `AcadAttrition_RaceOthersJD2` int DEFAULT NULL,
  `AcadAttrition_SubTotalJD2Total` int DEFAULT NULL,
  `AcadAttrition_SubTotalMenJD2` int DEFAULT NULL,
  `AcadAttrition_SubTotalWomenJD2` int DEFAULT NULL,
  `AcadAttrition_SubTotalAGIJD2` int DEFAULT NULL,
  `AcadAttrition_SubTotalPNRJD2` int DEFAULT NULL,
  `AcadAttrition_SubTotalOthersJD2` int DEFAULT NULL,
  `AcadAttrition_WhiteJD2Total` int DEFAULT NULL,
  `AcadAttrition_WhiteMenJD2` int DEFAULT NULL,
  `AcadAttrition_WhiteWomenJD2` int DEFAULT NULL,
  `AcadAttrition_WhiteAGIJD2` int DEFAULT NULL,
  `AcadAttrition_WhitePNRJD2` int DEFAULT NULL,
  `AcadAttrition_WhiteOthersJD2` int DEFAULT NULL,
  `AcadAttrition_NRJD2Total` int DEFAULT NULL,
  `AcadAttrition_NRMenJD2` int DEFAULT NULL,
  `AcadAttrition_NRWomenJD2` int DEFAULT NULL,
  `AcadAttrition_NRAGIJD2` int DEFAULT NULL,
  `AcadAttrition_NRPNRJD2` int DEFAULT NULL,
  `AcadAttrition_NROthersJD2` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceJD2Total` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceMenJD2` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceWomenJD2` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceAGIJD2` int DEFAULT NULL,
  `AcadAttrition_UnknownRacePNRJD2` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceOthersJD2` int DEFAULT NULL,
  `AcadAttrition_TotalJD2Total` int DEFAULT NULL,
  `AcadAttrition_TotalMenJD2` int DEFAULT NULL,
  `AcadAttrition_TotalWomenJD2` int DEFAULT NULL,
  `AcadAttrition_TotalAGIJD2` int DEFAULT NULL,
  `AcadAttrition_TotalPNRJD2` int DEFAULT NULL,
  `AcadAttrition_TotalOthersJD2` int DEFAULT NULL,
  `AcadAttrition_OtherHispJD3Total` int DEFAULT NULL,
  `AcadAttrition_OtherHispMenJD3` int DEFAULT NULL,
  `AcadAttrition_OtherHispWomenJD3` int DEFAULT NULL,
  `AcadAttrition_OtherHispAGIJD3` int DEFAULT NULL,
  `AcadAttrition_OtherHispPNRJD3` int DEFAULT NULL,
  `AcadAttrition_OtherHispOthersJD3` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianJD3Total` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianMenJD3` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianWomenJD3` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianAGIJD3` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianPNRJD3` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianOthersJD3` int DEFAULT NULL,
  `AcadAttrition_AsianJD3Total` int DEFAULT NULL,
  `AcadAttrition_AsianMenJD3` int DEFAULT NULL,
  `AcadAttrition_AsianWomenJD3` int DEFAULT NULL,
  `AcadAttrition_AsianAGIJD3` int DEFAULT NULL,
  `AcadAttrition_AsianPNRJD3` int DEFAULT NULL,
  `AcadAttrition_AsianOthersJD3` int DEFAULT NULL,
  `AcadAttrition_BlackJD3Total` int DEFAULT NULL,
  `AcadAttrition_BlackMenJD3` int DEFAULT NULL,
  `AcadAttrition_BlackWomenJD3` int DEFAULT NULL,
  `AcadAttrition_BlackAGIJD3` int DEFAULT NULL,
  `AcadAttrition_BlackPNRJD3` int DEFAULT NULL,
  `AcadAttrition_BlackOthersJD3` int DEFAULT NULL,
  `AcadAttrition_NativeJD3Total` int DEFAULT NULL,
  `AcadAttrition_NativeMenJD3` int DEFAULT NULL,
  `AcadAttrition_NativeWomenJD3` int DEFAULT NULL,
  `AcadAttrition_NativeAGIJD3` int DEFAULT NULL,
  `AcadAttrition_NativePNRJD3` int DEFAULT NULL,
  `AcadAttrition_NativeOthersJD3` int DEFAULT NULL,
  `AcadAttrition_RaceJD3Total` int DEFAULT NULL,
  `AcadAttrition_RaceMenJD3` int DEFAULT NULL,
  `AcadAttrition_RaceWomenJD3` int DEFAULT NULL,
  `AcadAttrition_RaceAGIJD3` int DEFAULT NULL,
  `AcadAttrition_RacePNRJD3` int DEFAULT NULL,
  `AcadAttrition_RaceOthersJD3` int DEFAULT NULL,
  `AcadAttrition_SubTotalJD3Total` int DEFAULT NULL,
  `AcadAttrition_SubTotalMenJD3` int DEFAULT NULL,
  `AcadAttrition_SubTotalWomenJD3` int DEFAULT NULL,
  `AcadAttrition_SubTotalAGIJD3` int DEFAULT NULL,
  `AcadAttrition_SubTotalPNRJD3` int DEFAULT NULL,
  `AcadAttrition_SubTotalOthersJD3` int DEFAULT NULL,
  `AcadAttrition_WhiteJD3Total` int DEFAULT NULL,
  `AcadAttrition_WhiteMenJD3` int DEFAULT NULL,
  `AcadAttrition_WhiteWomenJD3` int DEFAULT NULL,
  `AcadAttrition_WhiteAGIJD3` int DEFAULT NULL,
  `AcadAttrition_WhitePNRJD3` int DEFAULT NULL,
  `AcadAttrition_WhiteOthersJD3` int DEFAULT NULL,
  `AcadAttrition_NRJD3Total` int DEFAULT NULL,
  `AcadAttrition_NRMenJD3` int DEFAULT NULL,
  `AcadAttrition_NRWomenJD3` int DEFAULT NULL,
  `AcadAttrition_NRAGIJD3` int DEFAULT NULL,
  `AcadAttrition_NRPNRJD3` int DEFAULT NULL,
  `AcadAttrition_NROthersJD3` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceJD3Total` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceMenJD3` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceWomenJD3` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceAGIJD3` int DEFAULT NULL,
  `AcadAttrition_UnknownRacePNRJD3` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceOthersJD3` int DEFAULT NULL,
  `AcadAttrition_TotalJD3Total` int DEFAULT NULL,
  `AcadAttrition_TotalMenJD3` int DEFAULT NULL,
  `AcadAttrition_TotalWomenJD3` int DEFAULT NULL,
  `AcadAttrition_TotalAGIJD3` int DEFAULT NULL,
  `AcadAttrition_TotalPNRJD3` int DEFAULT NULL,
  `AcadAttrition_TotalOthersJD3` int DEFAULT NULL,
  `AcadAttrition_OtherHispJD4Total` int DEFAULT NULL,
  `AcadAttrition_OtherHispMenJD4` int DEFAULT NULL,
  `AcadAttrition_OtherHispWomenJD4` int DEFAULT NULL,
  `AcadAttrition_OtherHispAGIJD4` int DEFAULT NULL,
  `AcadAttrition_OtherHispPNRJD4` int DEFAULT NULL,
  `AcadAttrition_OtherHispOthersJD4` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianJD4Total` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianMenJD4` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianWomenJD4` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianAGIJD4` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianPNRJD4` int DEFAULT NULL,
  `AcadAttrition_AmericanIndianOthersJD4` int DEFAULT NULL,
  `AcadAttrition_AsianJD4Total` int DEFAULT NULL,
  `AcadAttrition_AsianMenJD4` int DEFAULT NULL,
  `AcadAttrition_AsianWomenJD4` int DEFAULT NULL,
  `AcadAttrition_AsianAGIJD4` int DEFAULT NULL,
  `AcadAttrition_AsianPNRJD4` int DEFAULT NULL,
  `AcadAttrition_AsianOthersJD4` int DEFAULT NULL,
  `AcadAttrition_BlackJD4Total` int DEFAULT NULL,
  `AcadAttrition_BlackMenJD4` int DEFAULT NULL,
  `AcadAttrition_BlackWomenJD4` int DEFAULT NULL,
  `AcadAttrition_BlackAGIJD4` int DEFAULT NULL,
  `AcadAttrition_BlackPNRJD4` int DEFAULT NULL,
  `AcadAttrition_BlackOthersJD4` int DEFAULT NULL,
  `AcadAttrition_NativeJD4Total` int DEFAULT NULL,
  `AcadAttrition_NativeMenJD4` int DEFAULT NULL,
  `AcadAttrition_NativeWomenJD4` int DEFAULT NULL,
  `AcadAttrition_NativeAGIJD4` int DEFAULT NULL,
  `AcadAttrition_NativePNRJD4` int DEFAULT NULL,
  `AcadAttrition_NativeOthersJD4` int DEFAULT NULL,
  `AcadAttrition_RaceJD4Total` int DEFAULT NULL,
  `AcadAttrition_RaceMenJD4` int DEFAULT NULL,
  `AcadAttrition_RaceWomenJD4` int DEFAULT NULL,
  `AcadAttrition_RaceAGIJD4` int DEFAULT NULL,
  `AcadAttrition_RacePNRJD4` int DEFAULT NULL,
  `AcadAttrition_RaceOthersJD4` int DEFAULT NULL,
  `AcadAttrition_SubTotalJD4Total` int DEFAULT NULL,
  `AcadAttrition_SubTotalMenJD4` int DEFAULT NULL,
  `AcadAttrition_SubTotalWomenJD4` int DEFAULT NULL,
  `AcadAttrition_SubTotalAGIJD4` int DEFAULT NULL,
  `AcadAttrition_SubTotalPNRJD4` int DEFAULT NULL,
  `AcadAttrition_SubTotalOthersJD4` int DEFAULT NULL,
  `AcadAttrition_WhiteJD4Total` int DEFAULT NULL,
  `AcadAttrition_WhiteMenJD4` int DEFAULT NULL,
  `AcadAttrition_WhiteWomenJD4` int DEFAULT NULL,
  `AcadAttrition_WhiteAGIJD4` int DEFAULT NULL,
  `AcadAttrition_WhitePNRJD4` int DEFAULT NULL,
  `AcadAttrition_WhiteOthersJD4` int DEFAULT NULL,
  `AcadAttrition_NRJD4Total` int DEFAULT NULL,
  `AcadAttrition_NRMenJD4` int DEFAULT NULL,
  `AcadAttrition_NRWomenJD4` int DEFAULT NULL,
  `AcadAttrition_NRAGIJD4` int DEFAULT NULL,
  `AcadAttrition_NRPNRJD4` int DEFAULT NULL,
  `AcadAttrition_NROthersJD4` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceJD4Total` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceMenJD4` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceWomenJD4` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceAGIJD4` int DEFAULT NULL,
  `AcadAttrition_UnknownRacePNRJD4` int DEFAULT NULL,
  `AcadAttrition_UnknownRaceOthersJD4` int DEFAULT NULL,
  `AcadAttrition_TotalJD4Total` int DEFAULT NULL,
  `AcadAttrition_TotalMenJD4` int DEFAULT NULL,
  `AcadAttrition_TotalWomenJD4` int DEFAULT NULL,
  `AcadAttrition_TotalAGIJD4` int DEFAULT NULL,
  `AcadAttrition_TotalPNRJD4` int DEFAULT NULL,
  `AcadAttrition_TotalOthersJD4` int DEFAULT NULL,
  `AcadAttrition_OtherHispJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_AmericanIndianJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_AsianJD1TotalPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_BlackJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_NativeJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_RaceJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_SubTotalJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_WhiteJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_NRJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_UnknownRaceJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_TotalJD1Percentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_OtherHispULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_AmericanIndianULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_AsianULTotalPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_BlackULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_NativeULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_RaceULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_SubTotalULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_WhiteULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_NRULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_UnknownRaceULPercentage` double(8,2) DEFAULT NULL,
  `AcadAttrition_TotalULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_OtherHispJD1Total` int DEFAULT NULL,
  `OtherAttrition_OtherHispMenJD1` int DEFAULT NULL,
  `OtherAttrition_OtherHispWomenJD1` int DEFAULT NULL,
  `OtherAttrition_OtherHispAGIJD1` int DEFAULT NULL,
  `OtherAttrition_OtherHispPNRJD1` int DEFAULT NULL,
  `OtherAttrition_OtherHispOthersJD1` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianJD1Total` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianMenJD1` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianWomenJD1` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianAGIJD1` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianPNRJD1` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianOthersJD1` int DEFAULT NULL,
  `OtherAttrition_AsianJD1Total` int DEFAULT NULL,
  `OtherAttrition_AsianMenJD1` int DEFAULT NULL,
  `OtherAttrition_AsianWomenJD1` int DEFAULT NULL,
  `OtherAttrition_AsianAGIJD1` int DEFAULT NULL,
  `OtherAttrition_AsianPNRJD1` int DEFAULT NULL,
  `OtherAttrition_AsianOthersJD1` int DEFAULT NULL,
  `OtherAttrition_BlackJD1Total` int DEFAULT NULL,
  `OtherAttrition_BlackMenJD1` int DEFAULT NULL,
  `OtherAttrition_BlackWomenJD1` int DEFAULT NULL,
  `OtherAttrition_BlackAGIJD1` int DEFAULT NULL,
  `OtherAttrition_BlackPNRJD1` int DEFAULT NULL,
  `OtherAttrition_BlackOthersJD1` int DEFAULT NULL,
  `OtherAttrition_NativeJD1Total` int DEFAULT NULL,
  `OtherAttrition_NativeMenJD1` int DEFAULT NULL,
  `OtherAttrition_NativeWomenJD1` int DEFAULT NULL,
  `OtherAttrition_NativeAGIJD1` int DEFAULT NULL,
  `OtherAttrition_NativePNRJD1` int DEFAULT NULL,
  `OtherAttrition_NativeOthersJD1` int DEFAULT NULL,
  `OtherAttrition_RaceJD1Total` int DEFAULT NULL,
  `OtherAttrition_RaceMenJD1` int DEFAULT NULL,
  `OtherAttrition_RaceWomenJD1` int DEFAULT NULL,
  `OtherAttrition_RaceAGIJD1` int DEFAULT NULL,
  `OtherAttrition_RacePNRJD1` int DEFAULT NULL,
  `OtherAttrition_RaceOthersJD1` int DEFAULT NULL,
  `OtherAttrition_SubTotalJD1Total` int DEFAULT NULL,
  `OtherAttrition_SubTotalMenJD1` int DEFAULT NULL,
  `OtherAttrition_SubTotalWomenJD1` int DEFAULT NULL,
  `OtherAttrition_SubTotalAGIJD1` int DEFAULT NULL,
  `OtherAttrition_SubTotalPNRJD1` int DEFAULT NULL,
  `OtherAttrition_SubTotalOthersJD1` int DEFAULT NULL,
  `OtherAttrition_WhiteJD1Total` int DEFAULT NULL,
  `OtherAttrition_WhiteMenJD1` int DEFAULT NULL,
  `OtherAttrition_WhiteWomenJD1` int DEFAULT NULL,
  `OtherAttrition_WhiteAGIJD1` int DEFAULT NULL,
  `OtherAttrition_WhitePNRJD1` int DEFAULT NULL,
  `OtherAttrition_WhiteOthersJD1` int DEFAULT NULL,
  `OtherAttrition_NRJD1Total` int DEFAULT NULL,
  `OtherAttrition_NRMenJD1` int DEFAULT NULL,
  `OtherAttrition_NRWomenJD1` int DEFAULT NULL,
  `OtherAttrition_NRAGIJD1` int DEFAULT NULL,
  `OtherAttrition_NRPNRJD1` int DEFAULT NULL,
  `OtherAttrition_NROthersJD1` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceJD1Total` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceMenJD1` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceWomenJD1` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceAGIJD1` int DEFAULT NULL,
  `OtherAttrition_UnknownRacePNRJD1` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceOthersJD1` int DEFAULT NULL,
  `OtherAttrition_TotalJD1Total` int DEFAULT NULL,
  `OtherAttrition_TotalMenJD1` int DEFAULT NULL,
  `OtherAttrition_TotalWomenJD1` int DEFAULT NULL,
  `OtherAttrition_TotalAGIJD1` int DEFAULT NULL,
  `OtherAttrition_TotalPNRJD1` int DEFAULT NULL,
  `OtherAttrition_TotalOthersJD1` int DEFAULT NULL,
  `OtherAttrition_OtherHispJD2Total` int DEFAULT NULL,
  `OtherAttrition_OtherHispMenJD2` int DEFAULT NULL,
  `OtherAttrition_OtherHispWomenJD2` int DEFAULT NULL,
  `OtherAttrition_OtherHispAGIJD2` int DEFAULT NULL,
  `OtherAttrition_OtherHispPNRJD2` int DEFAULT NULL,
  `OtherAttrition_OtherHispOthersJD2` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianJD2Total` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianMenJD2` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianWomenJD2` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianAGIJD2` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianPNRJD2` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianOthersJD2` int DEFAULT NULL,
  `OtherAttrition_AsianJD2Total` int DEFAULT NULL,
  `OtherAttrition_AsianMenJD2` int DEFAULT NULL,
  `OtherAttrition_AsianWomenJD2` int DEFAULT NULL,
  `OtherAttrition_AsianAGIJD2` int DEFAULT NULL,
  `OtherAttrition_AsianPNRJD2` int DEFAULT NULL,
  `OtherAttrition_AsianOthersJD2` int DEFAULT NULL,
  `OtherAttrition_BlackJD2Total` int DEFAULT NULL,
  `OtherAttrition_BlackMenJD2` int DEFAULT NULL,
  `OtherAttrition_BlackWomenJD2` int DEFAULT NULL,
  `OtherAttrition_BlackAGIJD2` int DEFAULT NULL,
  `OtherAttrition_BlackPNRJD2` int DEFAULT NULL,
  `OtherAttrition_BlackOthersJD2` int DEFAULT NULL,
  `OtherAttrition_NativeJD2Total` int DEFAULT NULL,
  `OtherAttrition_NativeMenJD2` int DEFAULT NULL,
  `OtherAttrition_NativeWomenJD2` int DEFAULT NULL,
  `OtherAttrition_NativeAGIJD2` int DEFAULT NULL,
  `OtherAttrition_NativePNRJD2` int DEFAULT NULL,
  `OtherAttrition_NativeOthersJD2` int DEFAULT NULL,
  `OtherAttrition_RaceJD2Total` int DEFAULT NULL,
  `OtherAttrition_RaceMenJD2` int DEFAULT NULL,
  `OtherAttrition_RaceWomenJD2` int DEFAULT NULL,
  `OtherAttrition_RaceAGIJD2` int DEFAULT NULL,
  `OtherAttrition_RacePNRJD2` int DEFAULT NULL,
  `OtherAttrition_RaceOthersJD2` int DEFAULT NULL,
  `OtherAttrition_SubTotalJD2Total` int DEFAULT NULL,
  `OtherAttrition_SubTotalMenJD2` int DEFAULT NULL,
  `OtherAttrition_SubTotalWomenJD2` int DEFAULT NULL,
  `OtherAttrition_SubTotalAGIJD2` int DEFAULT NULL,
  `OtherAttrition_SubTotalPNRJD2` int DEFAULT NULL,
  `OtherAttrition_SubTotalOthersJD2` int DEFAULT NULL,
  `OtherAttrition_WhiteJD2Total` int DEFAULT NULL,
  `OtherAttrition_WhiteMenJD2` int DEFAULT NULL,
  `OtherAttrition_WhiteWomenJD2` int DEFAULT NULL,
  `OtherAttrition_WhiteAGIJD2` int DEFAULT NULL,
  `OtherAttrition_WhitePNRJD2` int DEFAULT NULL,
  `OtherAttrition_WhiteOthersJD2` int DEFAULT NULL,
  `OtherAttrition_NRJD2Total` int DEFAULT NULL,
  `OtherAttrition_NRMenJD2` int DEFAULT NULL,
  `OtherAttrition_NRWomenJD2` int DEFAULT NULL,
  `OtherAttrition_NRAGIJD2` int DEFAULT NULL,
  `OtherAttrition_NRPNRJD2` int DEFAULT NULL,
  `OtherAttrition_NROthersJD2` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceJD2Total` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceMenJD2` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceWomenJD2` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceAGIJD2` int DEFAULT NULL,
  `OtherAttrition_UnknownRacePNRJD2` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceOthersJD2` int DEFAULT NULL,
  `OtherAttrition_TotalJD2Total` int DEFAULT NULL,
  `OtherAttrition_TotalMenJD2` int DEFAULT NULL,
  `OtherAttrition_TotalWomenJD2` int DEFAULT NULL,
  `OtherAttrition_TotalAGIJD2` int DEFAULT NULL,
  `OtherAttrition_TotalPNRJD2` int DEFAULT NULL,
  `OtherAttrition_TotalOthersJD2` int DEFAULT NULL,
  `OtherAttrition_OtherHispJD3Total` int DEFAULT NULL,
  `OtherAttrition_OtherHispMenJD3` int DEFAULT NULL,
  `OtherAttrition_OtherHispWomenJD3` int DEFAULT NULL,
  `OtherAttrition_OtherHispAGIJD3` int DEFAULT NULL,
  `OtherAttrition_OtherHispPNRJD3` int DEFAULT NULL,
  `OtherAttrition_OtherHispOthersJD3` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianJD3Total` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianMenJD3` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianWomenJD3` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianAGIJD3` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianPNRJD3` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianOthersJD3` int DEFAULT NULL,
  `OtherAttrition_AsianJD3Total` int DEFAULT NULL,
  `OtherAttrition_AsianMenJD3` int DEFAULT NULL,
  `OtherAttrition_AsianWomenJD3` int DEFAULT NULL,
  `OtherAttrition_AsianAGIJD3` int DEFAULT NULL,
  `OtherAttrition_AsianPNRJD3` int DEFAULT NULL,
  `OtherAttrition_AsianOthersJD3` int DEFAULT NULL,
  `OtherAttrition_BlackJD3Total` int DEFAULT NULL,
  `OtherAttrition_BlackMenJD3` int DEFAULT NULL,
  `OtherAttrition_BlackWomenJD3` int DEFAULT NULL,
  `OtherAttrition_BlackAGIJD3` int DEFAULT NULL,
  `OtherAttrition_BlackPNRJD3` int DEFAULT NULL,
  `OtherAttrition_BlackOthersJD3` int DEFAULT NULL,
  `OtherAttrition_NativeJD3Total` int DEFAULT NULL,
  `OtherAttrition_NativeMenJD3` int DEFAULT NULL,
  `OtherAttrition_NativeWomenJD3` int DEFAULT NULL,
  `OtherAttrition_NativeAGIJD3` int DEFAULT NULL,
  `OtherAttrition_NativePNRJD3` int DEFAULT NULL,
  `OtherAttrition_NativeOthersJD3` int DEFAULT NULL,
  `OtherAttrition_RaceJD3Total` int DEFAULT NULL,
  `OtherAttrition_RaceMenJD3` int DEFAULT NULL,
  `OtherAttrition_RaceWomenJD3` int DEFAULT NULL,
  `OtherAttrition_RaceAGIJD3` int DEFAULT NULL,
  `OtherAttrition_RacePNRJD3` int DEFAULT NULL,
  `OtherAttrition_RaceOthersJD3` int DEFAULT NULL,
  `OtherAttrition_SubTotalJD3Total` int DEFAULT NULL,
  `OtherAttrition_SubTotalMenJD3` int DEFAULT NULL,
  `OtherAttrition_SubTotalWomenJD3` int DEFAULT NULL,
  `OtherAttrition_SubTotalAGIJD3` int DEFAULT NULL,
  `OtherAttrition_SubTotalPNRJD3` int DEFAULT NULL,
  `OtherAttrition_SubTotalOthersJD3` int DEFAULT NULL,
  `OtherAttrition_WhiteJD3Total` int DEFAULT NULL,
  `OtherAttrition_WhiteMenJD3` int DEFAULT NULL,
  `OtherAttrition_WhiteWomenJD3` int DEFAULT NULL,
  `OtherAttrition_WhiteAGIJD3` int DEFAULT NULL,
  `OtherAttrition_WhitePNRJD3` int DEFAULT NULL,
  `OtherAttrition_WhiteOthersJD3` int DEFAULT NULL,
  `OtherAttrition_NRJD3Total` int DEFAULT NULL,
  `OtherAttrition_NRMenJD3` int DEFAULT NULL,
  `OtherAttrition_NRWomenJD3` int DEFAULT NULL,
  `OtherAttrition_NRAGIJD3` int DEFAULT NULL,
  `OtherAttrition_NRPNRJD3` int DEFAULT NULL,
  `OtherAttrition_NROthersJD3` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceJD3Total` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceMenJD3` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceWomenJD3` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceAGIJD3` int DEFAULT NULL,
  `OtherAttrition_UnknownRacePNRJD3` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceOthersJD3` int DEFAULT NULL,
  `OtherAttrition_TotalJD3Total` int DEFAULT NULL,
  `OtherAttrition_TotalMenJD3` int DEFAULT NULL,
  `OtherAttrition_TotalWomenJD3` int DEFAULT NULL,
  `OtherAttrition_TotalAGIJD3` int DEFAULT NULL,
  `OtherAttrition_TotalPNRJD3` int DEFAULT NULL,
  `OtherAttrition_TotalOthersJD3` int DEFAULT NULL,
  `OtherAttrition_OtherHispJD4Total` int DEFAULT NULL,
  `OtherAttrition_OtherHispMenJD4` int DEFAULT NULL,
  `OtherAttrition_OtherHispWomenJD4` int DEFAULT NULL,
  `OtherAttrition_OtherHispAGIJD4` int DEFAULT NULL,
  `OtherAttrition_OtherHispPNRJD4` int DEFAULT NULL,
  `OtherAttrition_OtherHispOthersJD4` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianJD4Total` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianMenJD4` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianWomenJD4` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianAGIJD4` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianPNRJD4` int DEFAULT NULL,
  `OtherAttrition_AmericanIndianOthersJD4` int DEFAULT NULL,
  `OtherAttrition_AsianJD4Total` int DEFAULT NULL,
  `OtherAttrition_AsianMenJD4` int DEFAULT NULL,
  `OtherAttrition_AsianWomenJD4` int DEFAULT NULL,
  `OtherAttrition_AsianAGIJD4` int DEFAULT NULL,
  `OtherAttrition_AsianPNRJD4` int DEFAULT NULL,
  `OtherAttrition_AsianOthersJD4` int DEFAULT NULL,
  `OtherAttrition_BlackJD4Total` int DEFAULT NULL,
  `OtherAttrition_BlackMenJD4` int DEFAULT NULL,
  `OtherAttrition_BlackWomenJD4` int DEFAULT NULL,
  `OtherAttrition_BlackAGIJD4` int DEFAULT NULL,
  `OtherAttrition_BlackPNRJD4` int DEFAULT NULL,
  `OtherAttrition_BlackOthersJD4` int DEFAULT NULL,
  `OtherAttrition_NativeJD4Total` int DEFAULT NULL,
  `OtherAttrition_NativeMenJD4` int DEFAULT NULL,
  `OtherAttrition_NativeWomenJD4` int DEFAULT NULL,
  `OtherAttrition_NativeAGIJD4` int DEFAULT NULL,
  `OtherAttrition_NativePNRJD4` int DEFAULT NULL,
  `OtherAttrition_NativeOthersJD4` int DEFAULT NULL,
  `OtherAttrition_RaceJD4Total` int DEFAULT NULL,
  `OtherAttrition_RaceMenJD4` int DEFAULT NULL,
  `OtherAttrition_RaceWomenJD4` int DEFAULT NULL,
  `OtherAttrition_RaceAGIJD4` int DEFAULT NULL,
  `OtherAttrition_RacePNRJD4` int DEFAULT NULL,
  `OtherAttrition_RaceOthersJD4` int DEFAULT NULL,
  `OtherAttrition_SubTotalJD4Total` int DEFAULT NULL,
  `OtherAttrition_SubTotalMenJD4` int DEFAULT NULL,
  `OtherAttrition_SubTotalWomenJD4` int DEFAULT NULL,
  `OtherAttrition_SubTotalAGIJD4` int DEFAULT NULL,
  `OtherAttrition_SubTotalPNRJD4` int DEFAULT NULL,
  `OtherAttrition_SubTotalOthersJD4` int DEFAULT NULL,
  `OtherAttrition_WhiteJD4Total` int DEFAULT NULL,
  `OtherAttrition_WhiteMenJD4` int DEFAULT NULL,
  `OtherAttrition_WhiteWomenJD4` int DEFAULT NULL,
  `OtherAttrition_WhiteAGIJD4` int DEFAULT NULL,
  `OtherAttrition_WhitePNRJD4` int DEFAULT NULL,
  `OtherAttrition_WhiteOthersJD4` int DEFAULT NULL,
  `OtherAttrition_NRJD4Total` int DEFAULT NULL,
  `OtherAttrition_NRMenJD4` int DEFAULT NULL,
  `OtherAttrition_NRWomenJD4` int DEFAULT NULL,
  `OtherAttrition_NRAGIJD4` int DEFAULT NULL,
  `OtherAttrition_NRPNRJD4` int DEFAULT NULL,
  `OtherAttrition_NROthersJD4` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceJD4Total` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceMenJD4` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceWomenJD4` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceAGIJD4` int DEFAULT NULL,
  `OtherAttrition_UnknownRacePNRJD4` int DEFAULT NULL,
  `OtherAttrition_UnknownRaceOthersJD4` int DEFAULT NULL,
  `OtherAttrition_TotalJD4Total` int DEFAULT NULL,
  `OtherAttrition_TotalMenJD4` int DEFAULT NULL,
  `OtherAttrition_TotalWomenJD4` int DEFAULT NULL,
  `OtherAttrition_TotalAGIJD4` int DEFAULT NULL,
  `OtherAttrition_TotalPNRJD4` int DEFAULT NULL,
  `OtherAttrition_TotalOthersJD4` int DEFAULT NULL,
  `OtherAttrition_OtherHispJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_AmericanIndianJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_AsianJD1TotalPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_BlackJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_NativeJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_RaceJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_SubTotalJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_WhiteJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_NRJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_UnknownRaceJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_TotalJD1Percentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_OtherHispULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_AmericanIndianULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_AsianULTotalPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_BlackULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_NativeULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_RaceULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_SubTotalULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_WhiteULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_NRULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_UnknownRaceULPercentage` double(8,2) DEFAULT NULL,
  `OtherAttrition_TotalULPercentage` double(8,2) DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_attrition_school_id_index` (`school_id`),
  KEY `school_attrition_academic_year_index` (`academic_year`),
  CONSTRAINT `school_attrition_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_baroutcomes_first`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_baroutcomes_first` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `state` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `jx_reporting` int DEFAULT NULL,
  `jx_passing` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`,`state`),
  KEY `school_baroutcomes_first_school_id_index` (`school_id`),
  KEY `school_baroutcomes_first_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_baroutcomes_first_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_baroutcomes_ultimate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_baroutcomes_ultimate` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `ultimate_year` smallint unsigned DEFAULT NULL,
  `grads` int DEFAULT NULL,
  `takers` int DEFAULT NULL,
  `passers` int DEFAULT NULL,
  `unk` int DEFAULT NULL,
  `nontakers` int DEFAULT NULL,
  `diploma_privilege` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_baroutcomes_ultimate_school_id_index` (`school_id`),
  KEY `school_baroutcomes_ultimate_graduation_year_index` (`graduation_year`),
  KEY `school_baroutcomes_ultimate_ultimate_year_index` (`ultimate_year`),
  CONSTRAINT `school_baroutcomes_ultimate_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_compares`;
/*!50001 DROP VIEW IF EXISTS `school_compares`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `school_compares` AS SELECT 
 1 AS `school_a_id`,
 1 AS `school_b_id`,
 1 AS `slug`,
 1 AS `national`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `school_conditionalscholarships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_conditionalscholarships` (
  `school_id` bigint unsigned NOT NULL,
  `admission_year` smallint unsigned NOT NULL,
  `has_conditional` int DEFAULT NULL,
  `number` int DEFAULT NULL,
  `lost` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`admission_year`),
  KEY `school_conditionalscholarships_school_id_index` (`school_id`),
  KEY `school_conditionalscholarships_admission_year_index` (`admission_year`),
  CONSTRAINT `school_conditionalscholarships_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_debts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_debts` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `debt_avg` int DEFAULT NULL,
  `debt_perc` double(8,2) DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_debts_school_id_index` (`school_id`),
  KEY `school_debts_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_debts_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_ed_debttoincome`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_ed_debttoincome` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `school_id` bigint unsigned NOT NULL,
  `reporting_year` smallint unsigned DEFAULT NULL,
  `LSTdegree` int DEFAULT NULL,
  `UNITID` int DEFAULT NULL,
  `OPEID6` int DEFAULT NULL,
  `CIPCODE` int DEFAULT NULL,
  `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `level` int DEFAULT NULL,
  `CREDDESC` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `borrowing_sample` int DEFAULT NULL,
  `borrowing_median` int DEFAULT NULL,
  `borrowing_median_payment` int DEFAULT NULL,
  `borrowing_mean` int DEFAULT NULL,
  `title_IV_count` int DEFAULT NULL,
  `earnings_count` int DEFAULT NULL,
  `earnings_median` int DEFAULT NULL,
  `y1_graduates` int DEFAULT NULL,
  `y2_graduates` int DEFAULT NULL,
  `data_combo` int DEFAULT NULL,
  `borrowing_years_graduates` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `earnings_y1_graduation_year` int DEFAULT NULL,
  `earnings_y2_graduation_year` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `school_ed_debttoincome_school_id_index` (`school_id`),
  KEY `school_ed_debttoincome_reporting_year_index` (`reporting_year`),
  CONSTRAINT `school_ed_debttoincome_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_ed_studentloans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_ed_studentloans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned DEFAULT NULL,
  `OPEID6` int DEFAULT NULL,
  `stafford_undergrad_sub_recip` int DEFAULT NULL,
  `stafford_undergrad_sub_disb` int DEFAULT NULL,
  `stafford_undergrad_sub_amount` int DEFAULT NULL,
  `stafford_grad_sub_recip` int DEFAULT NULL,
  `stafford_grad_sub_disb` int DEFAULT NULL,
  `stafford_grad_sub_amount` int DEFAULT NULL,
  `stafford_undergrad_unsub_recip` int DEFAULT NULL,
  `stafford_undergrad_unsub_disb` int DEFAULT NULL,
  `stafford_undergrad_unsub_amount` int DEFAULT NULL,
  `stafford_grad_unsub_recip` int DEFAULT NULL,
  `stafford_grad_unsub_disb` int DEFAULT NULL,
  `stafford_grad_unsub_amount` int DEFAULT NULL,
  `plus_recip` int DEFAULT NULL,
  `plus_disb` int DEFAULT NULL,
  `plus_amount` int DEFAULT NULL,
  `annual_disb` int DEFAULT NULL,
  `to_date_2015_16` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `school_ed_studentloans_school_id_index` (`school_id`),
  KEY `school_ed_studentloans_academic_year_index` (`academic_year`),
  CONSTRAINT `school_ed_studentloans_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_employment_aba`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_employment_aba` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `BPR_LT_FT` int DEFAULT NULL,
  `BPR_ST_FT` int DEFAULT NULL,
  `BPR_LT_PT` int DEFAULT NULL,
  `BPR_ST_PT` int DEFAULT NULL,
  `JDP_LT_FT` int DEFAULT NULL,
  `JDP_ST_FT` int DEFAULT NULL,
  `JDP_LT_PT` int DEFAULT NULL,
  `JDP_ST_PT` int DEFAULT NULL,
  `PRO_LT_FT` int DEFAULT NULL,
  `PRO_ST_FT` int DEFAULT NULL,
  `PRO_LT_PT` int DEFAULT NULL,
  `PRO_ST_PT` int DEFAULT NULL,
  `NP_LT_FT` int DEFAULT NULL,
  `NP_ST_FT` int DEFAULT NULL,
  `NP_LT_PT` int DEFAULT NULL,
  `NP_ST_PT` int DEFAULT NULL,
  `UNK_LT_FT` int DEFAULT NULL,
  `UNK_ST_FT` int DEFAULT NULL,
  `UNK_LT_PT` int DEFAULT NULL,
  `UNK_ST_PT` int DEFAULT NULL,
  `FTDegree` int DEFAULT NULL,
  `unemp_def` int DEFAULT NULL,
  `unemp_not_seek` int DEFAULT NULL,
  `unemp_seek` int DEFAULT NULL,
  `unk` int DEFAULT NULL,
  `grads` int DEFAULT NULL,
  `sf_BPR_LT_FT` int DEFAULT NULL,
  `sf_BPR_ST_FT` int DEFAULT NULL,
  `sf_BPR_LT_PT` int DEFAULT NULL,
  `sf_BPR_ST_PT` int DEFAULT NULL,
  `sf_JDP_LT_FT` int DEFAULT NULL,
  `sf_JDP_ST_FT` int DEFAULT NULL,
  `sf_JDP_LT_PT` int DEFAULT NULL,
  `sf_JDP_ST_PT` int DEFAULT NULL,
  `sf_PRO_LT_FT` int DEFAULT NULL,
  `sf_PRO_ST_FT` int DEFAULT NULL,
  `sf_PRO_LT_PT` int DEFAULT NULL,
  `sf_PRO_ST_PT` int DEFAULT NULL,
  `sf_NP_LT_FT` int DEFAULT NULL,
  `sf_NP_ST_FT` int DEFAULT NULL,
  `sf_NP_LT_PT` int DEFAULT NULL,
  `sf_NP_ST_PT` int DEFAULT NULL,
  `firm_LT_FT` int DEFAULT NULL,
  `firm_ST_FT` int DEFAULT NULL,
  `firm_LT_PT` int DEFAULT NULL,
  `firm_ST_PT` int DEFAULT NULL,
  `firm_solo_LT_FT` int DEFAULT NULL,
  `firm_solo_ST_FT` int DEFAULT NULL,
  `firm_solo_LT_PT` int DEFAULT NULL,
  `firm_solo_ST_PT` int DEFAULT NULL,
  `firm_2_10_LT_FT` int DEFAULT NULL,
  `firm_2_10_ST_FT` int DEFAULT NULL,
  `firm_2_10_LT_PT` int DEFAULT NULL,
  `firm_2_10_ST_PT` int DEFAULT NULL,
  `firm_11_25_LT_FT` int DEFAULT NULL,
  `firm_11_25_ST_FT` int DEFAULT NULL,
  `firm_11_25_LT_PT` int DEFAULT NULL,
  `firm_11_25_ST_PT` int DEFAULT NULL,
  `firm_26_50_LT_FT` int DEFAULT NULL,
  `firm_26_50_ST_FT` int DEFAULT NULL,
  `firm_26_50_LT_PT` int DEFAULT NULL,
  `firm_26_50_ST_PT` int DEFAULT NULL,
  `firm_51_100_LT_FT` int DEFAULT NULL,
  `firm_51_100_ST_FT` int DEFAULT NULL,
  `firm_51_100_LT_PT` int DEFAULT NULL,
  `firm_51_100_ST_PT` int DEFAULT NULL,
  `firm_101_250_LT_FT` int DEFAULT NULL,
  `firm_101_250_ST_FT` int DEFAULT NULL,
  `firm_101_250_LT_PT` int DEFAULT NULL,
  `firm_101_250_ST_PT` int DEFAULT NULL,
  `firm_251_500_LT_FT` int DEFAULT NULL,
  `firm_251_500_ST_FT` int DEFAULT NULL,
  `firm_251_500_LT_PT` int DEFAULT NULL,
  `firm_251_500_ST_PT` int DEFAULT NULL,
  `firm_501_LT_FT` int DEFAULT NULL,
  `firm_501_ST_FT` int DEFAULT NULL,
  `firm_501_LT_PT` int DEFAULT NULL,
  `firm_501_ST_PT` int DEFAULT NULL,
  `firm_unk_LT_FT` int DEFAULT NULL,
  `firm_unk_ST_FT` int DEFAULT NULL,
  `firm_unk_LT_PT` int DEFAULT NULL,
  `firm_unk_ST_PT` int DEFAULT NULL,
  `business_LT_FT` int DEFAULT NULL,
  `business_ST_FT` int DEFAULT NULL,
  `business_LT_PT` int DEFAULT NULL,
  `business_ST_PT` int DEFAULT NULL,
  `govt_LT_FT` int DEFAULT NULL,
  `govt_ST_FT` int DEFAULT NULL,
  `govt_LT_PT` int DEFAULT NULL,
  `govt_ST_PT` int DEFAULT NULL,
  `pubInt_LT_FT` int DEFAULT NULL,
  `pubInt_ST_FT` int DEFAULT NULL,
  `pubInt_LT_PT` int DEFAULT NULL,
  `pubInt_ST_PT` int DEFAULT NULL,
  `federal_LT_FT` int DEFAULT NULL,
  `federal_ST_FT` int DEFAULT NULL,
  `federal_LT_PT` int DEFAULT NULL,
  `federal_ST_PT` int DEFAULT NULL,
  `state_LT_FT` int DEFAULT NULL,
  `state_ST_FT` int DEFAULT NULL,
  `state_LT_PT` int DEFAULT NULL,
  `state_ST_PT` int DEFAULT NULL,
  `otherClerk_LT_FT` int DEFAULT NULL,
  `otherClerk_ST_FT` int DEFAULT NULL,
  `otherClerk_LT_PT` int DEFAULT NULL,
  `otherClerk_ST_PT` int DEFAULT NULL,
  `tribal_LT_FT` int DEFAULT NULL,
  `tribal_ST_FT` int DEFAULT NULL,
  `tribal_LT_PT` int DEFAULT NULL,
  `tribal_ST_PT` int DEFAULT NULL,
  `intl_LT_FT` int DEFAULT NULL,
  `intl_ST_FT` int DEFAULT NULL,
  `intl_LT_PT` int DEFAULT NULL,
  `intl_ST_PT` int DEFAULT NULL,
  `academic_LT_FT` int DEFAULT NULL,
  `academic_ST_FT` int DEFAULT NULL,
  `academic_LT_PT` int DEFAULT NULL,
  `academic_ST_PT` int DEFAULT NULL,
  `emp_unknown_LT_FT` int DEFAULT NULL,
  `emp_unknown_ST_FT` int DEFAULT NULL,
  `emp_unknown_LT_PT` int DEFAULT NULL,
  `emp_unknown_ST_PT` int DEFAULT NULL,
  `emp` int DEFAULT NULL,
  `school_funded_LT_FT` int DEFAULT NULL,
  `school_funded_ST_FT` int DEFAULT NULL,
  `school_funded_LT_PT` int DEFAULT NULL,
  `school_funded_ST_PT` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_employment_aba_school_id_index` (`school_id`),
  KEY `school_employment_aba_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_employment_aba_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_employment_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_employment_locations` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `region_US_Ter` int DEFAULT NULL,
  `region_Foreign` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_employment_locations_school_id_index` (`school_id`),
  KEY `school_employment_locations_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_employment_locations_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_employment_nalp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_employment_nalp` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `timing_beforeGrad` int DEFAULT NULL,
  `timing_beforeBar` int DEFAULT NULL,
  `timing_afterBar` int DEFAULT NULL,
  `timing_afterGrad` int DEFAULT NULL,
  `search_seek` int DEFAULT NULL,
  `search_notSeek` int DEFAULT NULL,
  `source_academic_OCI` int DEFAULT NULL,
  `source_academic_jobListing` int DEFAULT NULL,
  `source_academic_preLawEmp` int DEFAULT NULL,
  `source_academic_referral` int DEFAULT NULL,
  `source_academic_selfInitiated` int DEFAULT NULL,
  `source_academic_other` int DEFAULT NULL,
  `source_academic_UNK` int DEFAULT NULL,
  `source_business_OCI` int DEFAULT NULL,
  `source_business_jobListing` int DEFAULT NULL,
  `source_business_preLawEmp` int DEFAULT NULL,
  `source_business_referral` int DEFAULT NULL,
  `source_business_selfInitiated` int DEFAULT NULL,
  `source_business_other` int DEFAULT NULL,
  `source_business_UNK` int DEFAULT NULL,
  `source_clerkship_OCI` int DEFAULT NULL,
  `source_clerkship_jobListing` int DEFAULT NULL,
  `source_clerkship_preLawEmp` int DEFAULT NULL,
  `source_clerkship_referral` int DEFAULT NULL,
  `source_clerkship_selfInitiated` int DEFAULT NULL,
  `source_clerkship_other` int DEFAULT NULL,
  `source_clerkship_UNK` int DEFAULT NULL,
  `source_firm_solo` int DEFAULT NULL,
  `source_firm_2_50_OCI` int DEFAULT NULL,
  `source_firm_2_50_jobListing` int DEFAULT NULL,
  `source_firm_2_50_preLawEmp` int DEFAULT NULL,
  `source_firm_2_50_referral` int DEFAULT NULL,
  `source_firm_2_50_selfInitiated` int DEFAULT NULL,
  `source_firm_2_50_other` int DEFAULT NULL,
  `source_firm_2_50_UNK` int DEFAULT NULL,
  `source_firm_51_OCI` int DEFAULT NULL,
  `source_firm_51_jobListing` int DEFAULT NULL,
  `source_firm_51_preLawEmp` int DEFAULT NULL,
  `source_firm_51_referral` int DEFAULT NULL,
  `source_firm_51_selfInitiated` int DEFAULT NULL,
  `source_firm_51_other` int DEFAULT NULL,
  `source_firm_51_UNK` int DEFAULT NULL,
  `source_firm_UNK_UNK` int DEFAULT NULL,
  `source_govt_OCI` int DEFAULT NULL,
  `source_govt_jobListing` int DEFAULT NULL,
  `source_govt_preLawEmp` int DEFAULT NULL,
  `source_govt_referral` int DEFAULT NULL,
  `source_govt_selfInitiated` int DEFAULT NULL,
  `source_govt_other` int DEFAULT NULL,
  `source_govt_UNK` int DEFAULT NULL,
  `source_pubInt_OCI` int DEFAULT NULL,
  `source_pubInt_jobListing` int DEFAULT NULL,
  `source_pubInt_preLawEmp` int DEFAULT NULL,
  `source_pubInt_referral` int DEFAULT NULL,
  `source_pubInt_selfInitiated` int DEFAULT NULL,
  `source_pubInt_other` int DEFAULT NULL,
  `source_pubInt_UNK` int DEFAULT NULL,
  `source_UNK_OCI` int DEFAULT NULL,
  `source_UNK_jobListing` int DEFAULT NULL,
  `source_UNK_preLawEmp` int DEFAULT NULL,
  `source_UNK_referral` int DEFAULT NULL,
  `source_UNK_selfInitiated` int DEFAULT NULL,
  `source_UNK_other` int DEFAULT NULL,
  `source_UNK_UNK` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_employment_nalp_school_id_index` (`school_id`),
  KEY `school_employment_nalp_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_employment_nalp_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_employment_states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_employment_states` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `state_code` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `emp` smallint unsigned NOT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`,`state_code`),
  KEY `school_employment_states_state_code_foreign` (`state_code`),
  CONSTRAINT `school_employment_states_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`),
  CONSTRAINT `school_employment_states_state_code_foreign` FOREIGN KEY (`state_code`) REFERENCES `states` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_enrollments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_enrollments` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `first_total_hispanic` int DEFAULT NULL,
  `first_men_hispanic` int DEFAULT NULL,
  `first_women_hispanic` int DEFAULT NULL,
  `first_AGI_hispanic` int DEFAULT NULL,
  `first_PNR_hispanic` int DEFAULT NULL,
  `first_other_hispanic` int DEFAULT NULL,
  `first_total_native` int DEFAULT NULL,
  `first_men_native` int DEFAULT NULL,
  `first_women_native` int DEFAULT NULL,
  `first_AGI_native` int DEFAULT NULL,
  `first_PNR_native` int DEFAULT NULL,
  `first_other_native` int DEFAULT NULL,
  `first_total_asian` int DEFAULT NULL,
  `first_men_asian` int DEFAULT NULL,
  `first_women_asian` int DEFAULT NULL,
  `first_AGI_asian` int DEFAULT NULL,
  `first_PNR_asian` int DEFAULT NULL,
  `first_other_asian` int DEFAULT NULL,
  `first_total_black` int DEFAULT NULL,
  `first_men_black` int DEFAULT NULL,
  `first_women_black` int DEFAULT NULL,
  `first_AGI_black` int DEFAULT NULL,
  `first_PNR_black` int DEFAULT NULL,
  `first_other_black` int DEFAULT NULL,
  `first_total_islander` int DEFAULT NULL,
  `first_men_islander` int DEFAULT NULL,
  `first_women_islander` int DEFAULT NULL,
  `first_AGI_islander` int DEFAULT NULL,
  `first_PNR_islander` int DEFAULT NULL,
  `first_other_islander` int DEFAULT NULL,
  `first_total_tworaces` int DEFAULT NULL,
  `first_men_tworaces` int DEFAULT NULL,
  `first_women_tworaces` int DEFAULT NULL,
  `first_AGI_tworaces` int DEFAULT NULL,
  `first_PNR_tworaces` int DEFAULT NULL,
  `first_other_tworaces` int DEFAULT NULL,
  `first_total_minority` int DEFAULT NULL,
  `first_men_minority` int DEFAULT NULL,
  `first_women_minority` int DEFAULT NULL,
  `first_AGI_minority` int DEFAULT NULL,
  `first_PNR_minority` int DEFAULT NULL,
  `first_other_minority` int DEFAULT NULL,
  `first_total_white` int DEFAULT NULL,
  `first_men_white` int DEFAULT NULL,
  `first_women_white` int DEFAULT NULL,
  `first_AGI_white` int DEFAULT NULL,
  `first_PNR_white` int DEFAULT NULL,
  `first_other_white` int DEFAULT NULL,
  `first_total_intl` int DEFAULT NULL,
  `first_men_intl` int DEFAULT NULL,
  `first_women_intl` int DEFAULT NULL,
  `first_AGI_intl` int DEFAULT NULL,
  `first_PNR_intl` int DEFAULT NULL,
  `first_other_intl` int DEFAULT NULL,
  `first_total_unknownrace` int DEFAULT NULL,
  `first_men_unknownrace` int DEFAULT NULL,
  `first_women_unknownrace` int DEFAULT NULL,
  `first_AGI_unknownrace` int DEFAULT NULL,
  `first_PNR_unknownrace` int DEFAULT NULL,
  `first_other_unknownrace` int DEFAULT NULL,
  `first_total` int DEFAULT NULL,
  `first_men_total` int DEFAULT NULL,
  `first_women_total` int DEFAULT NULL,
  `first_AGI_total` int DEFAULT NULL,
  `first_PNR_total` int DEFAULT NULL,
  `first_other_total` int DEFAULT NULL,
  `second_total_hispanic` int DEFAULT NULL,
  `second_men_hispanic` int DEFAULT NULL,
  `second_women_hispanic` int DEFAULT NULL,
  `second_AGI_hispanic` int DEFAULT NULL,
  `second_PNR_hispanic` int DEFAULT NULL,
  `second_other_hispanic` int DEFAULT NULL,
  `second_total_native` int DEFAULT NULL,
  `second_men_native` int DEFAULT NULL,
  `second_women_native` int DEFAULT NULL,
  `second_AGI_native` int DEFAULT NULL,
  `second_PNR_native` int DEFAULT NULL,
  `second_other_native` int DEFAULT NULL,
  `second_total_asian` int DEFAULT NULL,
  `second_men_asian` int DEFAULT NULL,
  `second_women_asian` int DEFAULT NULL,
  `second_AGI_asian` int DEFAULT NULL,
  `second_PNR_asian` int DEFAULT NULL,
  `second_other_asian` int DEFAULT NULL,
  `second_total_black` int DEFAULT NULL,
  `second_men_black` int DEFAULT NULL,
  `second_women_black` int DEFAULT NULL,
  `second_AGI_black` int DEFAULT NULL,
  `second_PNR_black` int DEFAULT NULL,
  `second_other_black` int DEFAULT NULL,
  `second_total_islander` int DEFAULT NULL,
  `second_men_islander` int DEFAULT NULL,
  `second_women_islander` int DEFAULT NULL,
  `second_AGI_islander` int DEFAULT NULL,
  `second_PNR_islander` int DEFAULT NULL,
  `second_other_islander` int DEFAULT NULL,
  `second_total_tworaces` int DEFAULT NULL,
  `second_men_tworaces` int DEFAULT NULL,
  `second_women_tworaces` int DEFAULT NULL,
  `second_AGI_tworaces` int DEFAULT NULL,
  `second_PNR_tworaces` int DEFAULT NULL,
  `second_other_tworaces` int DEFAULT NULL,
  `second_total_minority` int DEFAULT NULL,
  `second_men_minority` int DEFAULT NULL,
  `second_women_minority` int DEFAULT NULL,
  `second_AGI_minority` int DEFAULT NULL,
  `second_PNR_minority` int DEFAULT NULL,
  `second_other_minority` int DEFAULT NULL,
  `second_total_white` int DEFAULT NULL,
  `second_men_white` int DEFAULT NULL,
  `second_women_white` int DEFAULT NULL,
  `second_AGI_white` int DEFAULT NULL,
  `second_PNR_white` int DEFAULT NULL,
  `second_other_white` int DEFAULT NULL,
  `second_total_intl` int DEFAULT NULL,
  `second_men_intl` int DEFAULT NULL,
  `second_women_intl` int DEFAULT NULL,
  `second_AGI_intl` int DEFAULT NULL,
  `second_PNR_intl` int DEFAULT NULL,
  `second_other_intl` int DEFAULT NULL,
  `second_total_unknownrace` int DEFAULT NULL,
  `second_men_unknownrace` int DEFAULT NULL,
  `second_women_unknownrace` int DEFAULT NULL,
  `second_AGI_unknownrace` int DEFAULT NULL,
  `second_PNR_unknownrace` int DEFAULT NULL,
  `second_other_unknownrace` int DEFAULT NULL,
  `second_total` int DEFAULT NULL,
  `second_men_total` int DEFAULT NULL,
  `second_women_total` int DEFAULT NULL,
  `second_AGI_total` int DEFAULT NULL,
  `second_PNR_total` int DEFAULT NULL,
  `second_other_total` int DEFAULT NULL,
  `third_total_hispanic` int DEFAULT NULL,
  `third_men_hispanic` int DEFAULT NULL,
  `third_women_hispanic` int DEFAULT NULL,
  `third_AGI_hispanic` int DEFAULT NULL,
  `third_PNR_hispanic` int DEFAULT NULL,
  `third_other_hispanic` int DEFAULT NULL,
  `third_total_native` int DEFAULT NULL,
  `third_men_native` int DEFAULT NULL,
  `third_women_native` int DEFAULT NULL,
  `third_AGI_native` int DEFAULT NULL,
  `third_PNR_native` int DEFAULT NULL,
  `third_other_native` int DEFAULT NULL,
  `third_total_asian` int DEFAULT NULL,
  `third_men_asian` int DEFAULT NULL,
  `third_women_asian` int DEFAULT NULL,
  `third_AGI_asian` int DEFAULT NULL,
  `third_PNR_asian` int DEFAULT NULL,
  `third_other_asian` int DEFAULT NULL,
  `third_total_black` int DEFAULT NULL,
  `third_men_black` int DEFAULT NULL,
  `third_women_black` int DEFAULT NULL,
  `third_AGI_black` int DEFAULT NULL,
  `third_PNR_black` int DEFAULT NULL,
  `third_other_black` int DEFAULT NULL,
  `third_total_islander` int DEFAULT NULL,
  `third_men_islander` int DEFAULT NULL,
  `third_women_islander` int DEFAULT NULL,
  `third_AGI_islander` int DEFAULT NULL,
  `third_PNR_islander` int DEFAULT NULL,
  `third_other_islander` int DEFAULT NULL,
  `third_total_tworaces` int DEFAULT NULL,
  `third_men_tworaces` int DEFAULT NULL,
  `third_women_tworaces` int DEFAULT NULL,
  `third_AGI_tworaces` int DEFAULT NULL,
  `third_PNR_tworaces` int DEFAULT NULL,
  `third_other_tworaces` int DEFAULT NULL,
  `third_total_minority` int DEFAULT NULL,
  `third_men_minority` int DEFAULT NULL,
  `third_women_minority` int DEFAULT NULL,
  `third_AGI_minority` int DEFAULT NULL,
  `third_PNR_minority` int DEFAULT NULL,
  `third_other_minority` int DEFAULT NULL,
  `third_total_white` int DEFAULT NULL,
  `third_men_white` int DEFAULT NULL,
  `third_women_white` int DEFAULT NULL,
  `third_AGI_white` int DEFAULT NULL,
  `third_PNR_white` int DEFAULT NULL,
  `third_other_white` int DEFAULT NULL,
  `third_total_intl` int DEFAULT NULL,
  `third_men_intl` int DEFAULT NULL,
  `third_women_intl` int DEFAULT NULL,
  `third_AGI_intl` int DEFAULT NULL,
  `third_PNR_intl` int DEFAULT NULL,
  `third_other_intl` int DEFAULT NULL,
  `third_total_unknownrace` int DEFAULT NULL,
  `third_men_unknownrace` int DEFAULT NULL,
  `third_women_unknownrace` int DEFAULT NULL,
  `third_AGI_unknownrace` int DEFAULT NULL,
  `third_PNR_unknownrace` int DEFAULT NULL,
  `third_other_unknownrace` int DEFAULT NULL,
  `third_total` int DEFAULT NULL,
  `third_men_total` int DEFAULT NULL,
  `third_women_total` int DEFAULT NULL,
  `third_AGI_total` int DEFAULT NULL,
  `third_PNR_total` int DEFAULT NULL,
  `third_other_total` int DEFAULT NULL,
  `fourth_total_hispanic` int DEFAULT NULL,
  `fourth_men_hispanic` int DEFAULT NULL,
  `fourth_women_hispanic` int DEFAULT NULL,
  `fourth_AGI_hispanic` int DEFAULT NULL,
  `fourth_PNR_hispanic` int DEFAULT NULL,
  `fourth_other_hispanic` int DEFAULT NULL,
  `fourth_total_native` int DEFAULT NULL,
  `fourth_men_native` int DEFAULT NULL,
  `fourth_women_native` int DEFAULT NULL,
  `fourth_AGI_native` int DEFAULT NULL,
  `fourth_PNR_native` int DEFAULT NULL,
  `fourth_other_native` int DEFAULT NULL,
  `fourth_total_asian` int DEFAULT NULL,
  `fourth_men_asian` int DEFAULT NULL,
  `fourth_women_asian` int DEFAULT NULL,
  `fourth_AGI_asian` int DEFAULT NULL,
  `fourth_PNR_asian` int DEFAULT NULL,
  `fourth_other_asian` int DEFAULT NULL,
  `fourth_total_black` int DEFAULT NULL,
  `fourth_men_black` int DEFAULT NULL,
  `fourth_women_black` int DEFAULT NULL,
  `fourth_AGI_black` int DEFAULT NULL,
  `fourth_PNR_black` int DEFAULT NULL,
  `fourth_other_black` int DEFAULT NULL,
  `fourth_total_islander` int DEFAULT NULL,
  `fourth_men_islander` int DEFAULT NULL,
  `fourth_women_islander` int DEFAULT NULL,
  `fourth_AGI_islander` int DEFAULT NULL,
  `fourth_PNR_islander` int DEFAULT NULL,
  `fourth_other_islander` int DEFAULT NULL,
  `fourth_total_tworaces` int DEFAULT NULL,
  `fourth_men_tworaces` int DEFAULT NULL,
  `fourth_women_tworaces` int DEFAULT NULL,
  `fourth_AGI_tworaces` int DEFAULT NULL,
  `fourth_PNR_tworaces` int DEFAULT NULL,
  `fourth_other_tworaces` int DEFAULT NULL,
  `fourth_total_minority` int DEFAULT NULL,
  `fourth_men_minority` int DEFAULT NULL,
  `fourth_women_minority` int DEFAULT NULL,
  `fourth_AGI_minority` int DEFAULT NULL,
  `fourth_PNR_minority` int DEFAULT NULL,
  `fourth_other_minority` int DEFAULT NULL,
  `fourth_total_white` int DEFAULT NULL,
  `fourth_men_white` int DEFAULT NULL,
  `fourth_women_white` int DEFAULT NULL,
  `fourth_AGI_white` int DEFAULT NULL,
  `fourth_PNR_white` int DEFAULT NULL,
  `fourth_other_white` int DEFAULT NULL,
  `fourth_total_intl` int DEFAULT NULL,
  `fourth_men_intl` int DEFAULT NULL,
  `fourth_women_intl` int DEFAULT NULL,
  `fourth_AGI_intl` int DEFAULT NULL,
  `fourth_PNR_intl` int DEFAULT NULL,
  `fourth_other_intl` int DEFAULT NULL,
  `fourth_total_unknownrace` int DEFAULT NULL,
  `fourth_men_unknownrace` int DEFAULT NULL,
  `fourth_women_unknownrace` int DEFAULT NULL,
  `fourth_AGI_unknownrace` int DEFAULT NULL,
  `fourth_PNR_unknownrace` int DEFAULT NULL,
  `fourth_other_unknownrace` int DEFAULT NULL,
  `fourth_total` int DEFAULT NULL,
  `fourth_men_total` int DEFAULT NULL,
  `fourth_women_total` int DEFAULT NULL,
  `fourth_AGI_total` int DEFAULT NULL,
  `fourth_PNR_total` int DEFAULT NULL,
  `fourth_other_total` int DEFAULT NULL,
  `hispanic` int DEFAULT NULL,
  `native` int DEFAULT NULL,
  `asian` int DEFAULT NULL,
  `black` int DEFAULT NULL,
  `islander` int DEFAULT NULL,
  `tworaces` int DEFAULT NULL,
  `minority` int DEFAULT NULL,
  `white` int DEFAULT NULL,
  `intl` int DEFAULT NULL,
  `unknownrace` int DEFAULT NULL,
  `total` int DEFAULT NULL,
  `grad_hispanic` int DEFAULT NULL,
  `grad_native` int DEFAULT NULL,
  `grad_asian` int DEFAULT NULL,
  `grad_black` int DEFAULT NULL,
  `grad_islander` int DEFAULT NULL,
  `grad_tworaces` int DEFAULT NULL,
  `grad_minority` int DEFAULT NULL,
  `grad_white` int DEFAULT NULL,
  `grad_intl` int DEFAULT NULL,
  `grad_unknownrace` int DEFAULT NULL,
  `grad` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_enrollments_school_id_index` (`school_id`),
  KEY `school_enrollments_academic_year_index` (`academic_year`),
  CONSTRAINT `school_enrollments_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_enrollments_2011_2016`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_enrollments_2011_2016` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `all_men_hispanic` int DEFAULT NULL,
  `all_men_native` int DEFAULT NULL,
  `all_men_asian` int DEFAULT NULL,
  `all_men_black` int DEFAULT NULL,
  `all_men_islander` int DEFAULT NULL,
  `all_men_tworaces` int DEFAULT NULL,
  `all_men_allminority` int DEFAULT NULL,
  `all_men_white` int DEFAULT NULL,
  `all_men_intl` int DEFAULT NULL,
  `all_men_unknownrace` int DEFAULT NULL,
  `all_men` int DEFAULT NULL,
  `all_women_hispanic` int DEFAULT NULL,
  `all_women_native` int DEFAULT NULL,
  `all_women_asian` int DEFAULT NULL,
  `all_women_black` int DEFAULT NULL,
  `all_women_islander` int DEFAULT NULL,
  `all_women_tworaces` int DEFAULT NULL,
  `all_women_allminority` int DEFAULT NULL,
  `all_women_white` int DEFAULT NULL,
  `all_women_intl` int DEFAULT NULL,
  `all_women_unknownrace` int DEFAULT NULL,
  `all_women` int DEFAULT NULL,
  `all_other_hispanic` int DEFAULT NULL,
  `all_other_native` int DEFAULT NULL,
  `all_other_asian` int DEFAULT NULL,
  `all_other_black` int DEFAULT NULL,
  `all_other_islander` int DEFAULT NULL,
  `all_other_tworaces` int DEFAULT NULL,
  `all_other_allminority` int DEFAULT NULL,
  `all_other_white` int DEFAULT NULL,
  `all_other_intl` int DEFAULT NULL,
  `all_other_unknownrace` int DEFAULT NULL,
  `all_other` int DEFAULT NULL,
  `ft_hispanic` int DEFAULT NULL,
  `ft_native` int DEFAULT NULL,
  `ft_asian` int DEFAULT NULL,
  `ft_black` int DEFAULT NULL,
  `ft_islander` int DEFAULT NULL,
  `ft_tworaces` int DEFAULT NULL,
  `ft_allminority` int DEFAULT NULL,
  `ft_white` int DEFAULT NULL,
  `ft_intl` int DEFAULT NULL,
  `ft_unknownrace` int DEFAULT NULL,
  `ft` int DEFAULT NULL,
  `pt_hispanic` int DEFAULT NULL,
  `pt_native` int DEFAULT NULL,
  `pt_asian` int DEFAULT NULL,
  `pt_black` int DEFAULT NULL,
  `pt_islander` int DEFAULT NULL,
  `pt_tworaces` int DEFAULT NULL,
  `pt_allminority` int DEFAULT NULL,
  `pt_white` int DEFAULT NULL,
  `pt_intl` int DEFAULT NULL,
  `pt_unknownrace` int DEFAULT NULL,
  `pt` int DEFAULT NULL,
  `first_hispanic` int DEFAULT NULL,
  `first_native` int DEFAULT NULL,
  `first_asian` int DEFAULT NULL,
  `first_black` int DEFAULT NULL,
  `first_islander` int DEFAULT NULL,
  `first_tworaces` int DEFAULT NULL,
  `first_allminority` int DEFAULT NULL,
  `first_white` int DEFAULT NULL,
  `first_intl` int DEFAULT NULL,
  `first_unknownrace` int DEFAULT NULL,
  `first` int DEFAULT NULL,
  `hispanic` int DEFAULT NULL,
  `native` int DEFAULT NULL,
  `asian` int DEFAULT NULL,
  `black` int DEFAULT NULL,
  `islander` int DEFAULT NULL,
  `tworaces` int DEFAULT NULL,
  `allminority` int DEFAULT NULL,
  `white` int DEFAULT NULL,
  `intl` int DEFAULT NULL,
  `unknownrace` int DEFAULT NULL,
  `enrollment` int DEFAULT NULL,
  `grad_hispanic` int DEFAULT NULL,
  `grad_native` int DEFAULT NULL,
  `grad_asian` int DEFAULT NULL,
  `grad_black` int DEFAULT NULL,
  `grad_islander` int DEFAULT NULL,
  `grad_tworaces` int DEFAULT NULL,
  `grad_allminority` int DEFAULT NULL,
  `grad_white` int DEFAULT NULL,
  `grad_intl` int DEFAULT NULL,
  `grad_unknownrace` int DEFAULT NULL,
  `grad` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_enrollments_2011_2016_school_id_index` (`school_id`),
  KEY `school_enrollments_2011_2016_academic_year_index` (`academic_year`),
  CONSTRAINT `school_enrollments_2011_2016_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_environment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_environment` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `term` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fall_start` int DEFAULT NULL,
  `winter_start` int DEFAULT NULL,
  `summer_start` int DEFAULT NULL,
  `credits_to_graduate` int DEFAULT NULL,
  `section_size` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_environment_school_id_index` (`school_id`),
  KEY `school_environment_academic_year_index` (`academic_year`),
  CONSTRAINT `school_environment_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_faculty`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_faculty` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `men_FT` int DEFAULT NULL,
  `men_nonFT` int DEFAULT NULL,
  `men_total` int DEFAULT NULL,
  `women_FT` int DEFAULT NULL,
  `women_nonFT` int DEFAULT NULL,
  `women_total` int DEFAULT NULL,
  `AGI_FT` int DEFAULT NULL,
  `AGI_nonFT` int DEFAULT NULL,
  `AGI_total` int DEFAULT NULL,
  `PNR_FT` int DEFAULT NULL,
  `PNR_nonFT` int DEFAULT NULL,
  `PNR_total` int DEFAULT NULL,
  `other_FT` int DEFAULT NULL,
  `other_nonFT` int DEFAULT NULL,
  `other_total` int DEFAULT NULL,
  `poc_FT` int DEFAULT NULL,
  `poc_nonFT` int DEFAULT NULL,
  `poc_total` int DEFAULT NULL,
  `fulltime` int DEFAULT NULL,
  `nonfulltime` int DEFAULT NULL,
  `allfaculty` int DEFAULT NULL,
  `librarians_FT` int DEFAULT NULL,
  `librarians_nonFT` int DEFAULT NULL,
  `librarians_total` int DEFAULT NULL,
  `admin_FT` int DEFAULT NULL,
  `admin_PT` int DEFAULT NULL,
  `admin_total` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_faculty_school_id_index` (`school_id`),
  KEY `school_faculty_academic_year_index` (`academic_year`),
  CONSTRAINT `school_faculty_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_grants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_grants` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `enrollment_All` int DEFAULT NULL,
  `enrollment_FT` int DEFAULT NULL,
  `enrollment_PT` int DEFAULT NULL,
  `receiving_grants_All` int DEFAULT NULL,
  `receiving_grants_FT` int DEFAULT NULL,
  `receiving_grants_PT` int DEFAULT NULL,
  `less_than_half_All` int DEFAULT NULL,
  `less_than_half_FT` int DEFAULT NULL,
  `less_than_half_PT` int DEFAULT NULL,
  `more_than_half_less_than_full_All` int DEFAULT NULL,
  `more_than_half_less_than_full_FT` int DEFAULT NULL,
  `more_than_half_less_than_full_PT` int DEFAULT NULL,
  `full_tuition_All` int DEFAULT NULL,
  `full_tuition_FT` int DEFAULT NULL,
  `full_tuition_PT` int DEFAULT NULL,
  `more_than_full_All` int DEFAULT NULL,
  `more_than_full_FT` int DEFAULT NULL,
  `more_than_full_PT` int DEFAULT NULL,
  `grant_25_FT` int DEFAULT NULL,
  `grant_50_FT` int DEFAULT NULL,
  `grant_75_FT` int DEFAULT NULL,
  `grant_25_PT` int DEFAULT NULL,
  `grant_50_PT` int DEFAULT NULL,
  `grant_75_PT` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_grants_school_id_index` (`school_id`),
  KEY `school_grants_academic_year_index` (`academic_year`),
  CONSTRAINT `school_grants_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_nalpreports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_nalpreports` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `publish_status` int DEFAULT NULL,
  `data_entry_user` int DEFAULT NULL,
  `data_entry_flag` int DEFAULT NULL,
  `data_approval_user` int DEFAULT NULL,
  `data_approval_flag` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_nalpreports_school_id_index` (`school_id`),
  KEY `school_nalpreports_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_nalpreports_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_prices` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `tuition_FT` int DEFAULT NULL,
  `resident_tuition_FT` int DEFAULT NULL,
  `tuition_PT` int DEFAULT NULL,
  `tuition_PT_totaled` int DEFAULT NULL,
  `tuition_PT_bycredit` int DEFAULT NULL,
  `tuition_PT_bycreditfees` int DEFAULT NULL,
  `resident_tuition_PT` int DEFAULT NULL,
  `resident_tuition_PT_totaled` int DEFAULT NULL,
  `resident_tuition_PT_bycredit` int DEFAULT NULL,
  `resident_tuition_PT_bycreditfees` int DEFAULT NULL,
  `guarantee` int DEFAULT NULL,
  `col_campus` int DEFAULT NULL,
  `col` int DEFAULT NULL,
  `col_home` int DEFAULT NULL,
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_prices_school_id_index` (`school_id`),
  KEY `school_prices_academic_year_index` (`academic_year`),
  CONSTRAINT `school_prices_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_reports`;
/*!50001 DROP VIEW IF EXISTS `school_reports`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `school_reports` AS SELECT 
 1 AS `school_id`,
 1 AS `national`,
 1 AS `employment_score`,
 1 AS `underemployment_score`,
 1 AS `grads`,
 1 AS `emp`,
 1 AS `biglaw`,
 1 AS `clerk`,
 1 AS `firm`,
 1 AS `lawyer`,
 1 AS `natl`,
 1 AS `public`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `school_salary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_salary` (
  `school_id` bigint unsigned NOT NULL,
  `graduation_year` smallint unsigned NOT NULL,
  `emp_sal` int DEFAULT NULL,
  `emp_sal_25` int DEFAULT NULL,
  `emp_sal_50` int DEFAULT NULL,
  `emp_sal_75` int DEFAULT NULL,
  `emp_sal_mean` int DEFAULT NULL,
  `emp_private_sal` int DEFAULT NULL,
  `emp_private_sal_25` int DEFAULT NULL,
  `emp_private_sal_50` int DEFAULT NULL,
  `emp_private_sal_75` int DEFAULT NULL,
  `emp_private_sal_mean` int DEFAULT NULL,
  `emp_public_sal` int DEFAULT NULL,
  `emp_public_sal_25` int DEFAULT NULL,
  `emp_public_sal_50` int DEFAULT NULL,
  `emp_public_sal_75` int DEFAULT NULL,
  `emp_public_sal_mean` int DEFAULT NULL,
  `BPR_sal` int DEFAULT NULL,
  `BPR_sal_25` int DEFAULT NULL,
  `BPR_sal_50` int DEFAULT NULL,
  `BPR_sal_75` int DEFAULT NULL,
  `BPR_sal_mean` int DEFAULT NULL,
  `JDP_sal` int DEFAULT NULL,
  `JDP_sal_25` int DEFAULT NULL,
  `JDP_sal_50` int DEFAULT NULL,
  `JDP_sal_75` int DEFAULT NULL,
  `JDP_sal_mean` int DEFAULT NULL,
  `PRO_sal` int DEFAULT NULL,
  `PRO_sal_25` int DEFAULT NULL,
  `PRO_sal_50` int DEFAULT NULL,
  `PRO_sal_75` int DEFAULT NULL,
  `PRO_sal_mean` int DEFAULT NULL,
  `NP_sal` int DEFAULT NULL,
  `NP_sal_25` int DEFAULT NULL,
  `NP_sal_50` int DEFAULT NULL,
  `NP_sal_75` int DEFAULT NULL,
  `NP_sal_mean` int DEFAULT NULL,
  `academic_sal` int DEFAULT NULL,
  `academic_sal_25` int DEFAULT NULL,
  `academic_sal_50` int DEFAULT NULL,
  `academic_sal_75` int DEFAULT NULL,
  `academic_sal_mean` int DEFAULT NULL,
  `academic_BPR` int DEFAULT NULL,
  `academic_BPR_sal` int DEFAULT NULL,
  `academic_BPR_sal_25` int DEFAULT NULL,
  `academic_BPR_sal_50` int DEFAULT NULL,
  `academic_BPR_sal_75` int DEFAULT NULL,
  `academic_BPR_sal_mean` int DEFAULT NULL,
  `academic_JDP` int DEFAULT NULL,
  `academic_JDP_sal` int DEFAULT NULL,
  `academic_JDP_sal_25` int DEFAULT NULL,
  `academic_JDP_sal_50` int DEFAULT NULL,
  `academic_JDP_sal_75` int DEFAULT NULL,
  `academic_JDP_sal_mean` int DEFAULT NULL,
  `academic_PRO` int DEFAULT NULL,
  `academic_PRO_sal` int DEFAULT NULL,
  `academic_PRO_sal_25` int DEFAULT NULL,
  `academic_PRO_sal_50` int DEFAULT NULL,
  `academic_PRO_sal_75` int DEFAULT NULL,
  `academic_PRO_sal_mean` int DEFAULT NULL,
  `academic_NP` int DEFAULT NULL,
  `academic_NP_sal` int DEFAULT NULL,
  `academic_NP_sal_25` int DEFAULT NULL,
  `academic_NP_sal_50` int DEFAULT NULL,
  `academic_NP_sal_75` int DEFAULT NULL,
  `academic_NP_sal_mean` int DEFAULT NULL,
  `academic_Cred_UNK` int DEFAULT NULL,
  `business_sal` int DEFAULT NULL,
  `business_sal_25` int DEFAULT NULL,
  `business_sal_50` int DEFAULT NULL,
  `business_sal_75` int DEFAULT NULL,
  `business_sal_mean` int DEFAULT NULL,
  `business_BPR` int DEFAULT NULL,
  `business_BPR_sal` int DEFAULT NULL,
  `business_BPR_sal_25` int DEFAULT NULL,
  `business_BPR_sal_50` int DEFAULT NULL,
  `business_BPR_sal_75` int DEFAULT NULL,
  `business_BPR_sal_mean` int DEFAULT NULL,
  `business_JDP` int DEFAULT NULL,
  `business_JDP_sal` int DEFAULT NULL,
  `business_JDP_sal_25` int DEFAULT NULL,
  `business_JDP_sal_50` int DEFAULT NULL,
  `business_JDP_sal_75` int DEFAULT NULL,
  `business_JDP_sal_mean` int DEFAULT NULL,
  `business_PRO` int DEFAULT NULL,
  `business_PRO_sal` int DEFAULT NULL,
  `business_PRO_sal_25` int DEFAULT NULL,
  `business_PRO_sal_50` int DEFAULT NULL,
  `business_PRO_sal_75` int DEFAULT NULL,
  `business_PRO_sal_mean` int DEFAULT NULL,
  `business_NP` int DEFAULT NULL,
  `business_NP_sal` int DEFAULT NULL,
  `business_NP_sal_25` int DEFAULT NULL,
  `business_NP_sal_50` int DEFAULT NULL,
  `business_NP_sal_75` int DEFAULT NULL,
  `business_NP_sal_mean` int DEFAULT NULL,
  `business_Cred_UNK` int DEFAULT NULL,
  `clerkship_sal` int DEFAULT NULL,
  `clerkship_sal_25` int DEFAULT NULL,
  `clerkship_sal_50` int DEFAULT NULL,
  `clerkship_sal_75` int DEFAULT NULL,
  `clerkship_sal_mean` int DEFAULT NULL,
  `fed_sal` int DEFAULT NULL,
  `fed_sal_25` int DEFAULT NULL,
  `fed_sal_50` int DEFAULT NULL,
  `fed_sal_75` int DEFAULT NULL,
  `fed_sal_mean` int DEFAULT NULL,
  `state_sal` int DEFAULT NULL,
  `state_sal_25` int DEFAULT NULL,
  `state_sal_50` int DEFAULT NULL,
  `state_sal_75` int DEFAULT NULL,
  `state_sal_mean` int DEFAULT NULL,
  `local_sal` int DEFAULT NULL,
  `local_sal_25` int DEFAULT NULL,
  `local_sal_50` int DEFAULT NULL,
  `local_sal_75` int DEFAULT NULL,
  `local_sal_mean` int DEFAULT NULL,
  `firm_BPR` int DEFAULT NULL,
  `firm_BPR_sal` int DEFAULT NULL,
  `firm_BPR_sal_25` int DEFAULT NULL,
  `firm_BPR_sal_50` int DEFAULT NULL,
  `firm_BPR_sal_75` int DEFAULT NULL,
  `firm_BPR_sal_mean` int DEFAULT NULL,
  `firm_JDP` int DEFAULT NULL,
  `firm_JDP_sal` int DEFAULT NULL,
  `firm_JDP_sal_25` int DEFAULT NULL,
  `firm_JDP_sal_50` int DEFAULT NULL,
  `firm_JDP_sal_75` int DEFAULT NULL,
  `firm_JDP_sal_mean` int DEFAULT NULL,
  `firm_PRO` int DEFAULT NULL,
  `firm_PRO_sal` int DEFAULT NULL,
  `firm_PRO_sal_25` int DEFAULT NULL,
  `firm_PRO_sal_50` int DEFAULT NULL,
  `firm_PRO_sal_75` int DEFAULT NULL,
  `firm_PRO_sal_mean` int DEFAULT NULL,
  `firm_NP` int DEFAULT NULL,
  `firm_NP_sal` int DEFAULT NULL,
  `firm_NP_sal_25` int DEFAULT NULL,
  `firm_NP_sal_50` int DEFAULT NULL,
  `firm_NP_sal_75` int DEFAULT NULL,
  `firm_NP_sal_mean` int DEFAULT NULL,
  `firm_Cred_UNK` int DEFAULT NULL,
  `firm_attorney` int DEFAULT NULL,
  `firm_attorney_sal` int DEFAULT NULL,
  `firm_attorney_sal_25` int DEFAULT NULL,
  `firm_attorney_sal_50` int DEFAULT NULL,
  `firm_attorney_sal_75` int DEFAULT NULL,
  `firm_attorney_sal_mean` int DEFAULT NULL,
  `firm_staff` int DEFAULT NULL,
  `firm_staff_sal` int DEFAULT NULL,
  `firm_staff_sal_25` int DEFAULT NULL,
  `firm_staff_sal_50` int DEFAULT NULL,
  `firm_staff_sal_75` int DEFAULT NULL,
  `firm_staff_sal_mean` int DEFAULT NULL,
  `firm_clerk` int DEFAULT NULL,
  `firm_clerk_sal` int DEFAULT NULL,
  `firm_clerk_sal_25` int DEFAULT NULL,
  `firm_clerk_sal_50` int DEFAULT NULL,
  `firm_clerk_sal_75` int DEFAULT NULL,
  `firm_clerk_sal_mean` int DEFAULT NULL,
  `firm_paralegal` int DEFAULT NULL,
  `firm_admin` int DEFAULT NULL,
  `firm_patent` int DEFAULT NULL,
  `firm_UNKjob` int DEFAULT NULL,
  `firm_sal` int DEFAULT NULL,
  `firm_sal_25` int DEFAULT NULL,
  `firm_sal_50` int DEFAULT NULL,
  `firm_sal_75` int DEFAULT NULL,
  `firm_sal_mean` int DEFAULT NULL,
  `firm_solo_sal` int DEFAULT NULL,
  `firm_solo_sal_25` int DEFAULT NULL,
  `firm_solo_sal_50` int DEFAULT NULL,
  `firm_solo_sal_75` int DEFAULT NULL,
  `firm_solo_sal_mean` int DEFAULT NULL,
  `firm_2_10_sal` int DEFAULT NULL,
  `firm_2_10_sal_25` int DEFAULT NULL,
  `firm_2_10_sal_50` int DEFAULT NULL,
  `firm_2_10_sal_75` int DEFAULT NULL,
  `firm_2_10_sal_mean` int DEFAULT NULL,
  `firm_11_25_sal` int DEFAULT NULL,
  `firm_11_25_sal_25` int DEFAULT NULL,
  `firm_11_25_sal_50` int DEFAULT NULL,
  `firm_11_25_sal_75` int DEFAULT NULL,
  `firm_11_25_sal_mean` int DEFAULT NULL,
  `firm_26_50_sal` int DEFAULT NULL,
  `firm_26_50_sal_25` int DEFAULT NULL,
  `firm_26_50_sal_50` int DEFAULT NULL,
  `firm_26_50_sal_75` int DEFAULT NULL,
  `firm_26_50_sal_mean` int DEFAULT NULL,
  `firm_51_100_sal` int DEFAULT NULL,
  `firm_51_100_sal_25` int DEFAULT NULL,
  `firm_51_100_sal_50` int DEFAULT NULL,
  `firm_51_100_sal_75` int DEFAULT NULL,
  `firm_51_100_sal_mean` int DEFAULT NULL,
  `firm_101_250_sal` int DEFAULT NULL,
  `firm_101_250_sal_25` int DEFAULT NULL,
  `firm_101_250_sal_50` int DEFAULT NULL,
  `firm_101_250_sal_75` int DEFAULT NULL,
  `firm_101_250_sal_mean` int DEFAULT NULL,
  `firm_251_500_sal` int DEFAULT NULL,
  `firm_251_500_sal_25` int DEFAULT NULL,
  `firm_251_500_sal_50` int DEFAULT NULL,
  `firm_251_500_sal_75` int DEFAULT NULL,
  `firm_251_500_sal_mean` int DEFAULT NULL,
  `firm_501_sal` int DEFAULT NULL,
  `firm_501_sal_25` int DEFAULT NULL,
  `firm_501_sal_50` int DEFAULT NULL,
  `firm_501_sal_75` int DEFAULT NULL,
  `firm_501_sal_mean` int DEFAULT NULL,
  `govt_sal` int DEFAULT NULL,
  `govt_sal_25` int DEFAULT NULL,
  `govt_sal_50` int DEFAULT NULL,
  `govt_sal_75` int DEFAULT NULL,
  `govt_sal_mean` int DEFAULT NULL,
  `govt_fed_LT_FT` int DEFAULT NULL,
  `govt_fed_ST_FT` int DEFAULT NULL,
  `govt_fed_LT_PT` int DEFAULT NULL,
  `govt_fed_ST_PT` int DEFAULT NULL,
  `govt_fed_UNK` int DEFAULT NULL,
  `govt_fed_sal` int DEFAULT NULL,
  `govt_fed_sal_25` int DEFAULT NULL,
  `govt_fed_sal_50` int DEFAULT NULL,
  `govt_fed_sal_75` int DEFAULT NULL,
  `govt_fed_sal_mean` int DEFAULT NULL,
  `govt_state_LT_FT` int DEFAULT NULL,
  `govt_state_ST_FT` int DEFAULT NULL,
  `govt_state_LT_PT` int DEFAULT NULL,
  `govt_state_ST_PT` int DEFAULT NULL,
  `govt_state_UNK` int DEFAULT NULL,
  `govt_state_sal` int DEFAULT NULL,
  `govt_state_sal_25` int DEFAULT NULL,
  `govt_state_sal_50` int DEFAULT NULL,
  `govt_state_sal_75` int DEFAULT NULL,
  `govt_state_sal_mean` int DEFAULT NULL,
  `govt_BPR` int DEFAULT NULL,
  `govt_BPR_sal` int DEFAULT NULL,
  `govt_BPR_sal_25` int DEFAULT NULL,
  `govt_BPR_sal_50` int DEFAULT NULL,
  `govt_BPR_sal_75` int DEFAULT NULL,
  `govt_BPR_sal_mean` int DEFAULT NULL,
  `govt_JDP` int DEFAULT NULL,
  `govt_JDP_sal` int DEFAULT NULL,
  `govt_JDP_sal_25` int DEFAULT NULL,
  `govt_JDP_sal_50` int DEFAULT NULL,
  `govt_JDP_sal_75` int DEFAULT NULL,
  `govt_JDP_sal_mean` int DEFAULT NULL,
  `govt_PRO` int DEFAULT NULL,
  `govt_PRO_sal` int DEFAULT NULL,
  `govt_PRO_sal_25` int DEFAULT NULL,
  `govt_PRO_sal_50` int DEFAULT NULL,
  `govt_PRO_sal_75` int DEFAULT NULL,
  `govt_PRO_sal_mean` int DEFAULT NULL,
  `govt_NP` int DEFAULT NULL,
  `govt_NP_sal` int DEFAULT NULL,
  `govt_NP_sal_25` int DEFAULT NULL,
  `govt_NP_sal_50` int DEFAULT NULL,
  `govt_NP_sal_75` int DEFAULT NULL,
  `govt_NP_sal_mean` int DEFAULT NULL,
  `govt_Cred_UNK` int DEFAULT NULL,
  `pubInt_sal` int DEFAULT NULL,
  `pubInt_sal_25` int DEFAULT NULL,
  `pubInt_sal_50` int DEFAULT NULL,
  `pubInt_sal_75` int DEFAULT NULL,
  `pubInt_sal_mean` int DEFAULT NULL,
  `pubInt_BPR` int DEFAULT NULL,
  `pubInt_BPR_sal` int DEFAULT NULL,
  `pubInt_BPR_sal_25` int DEFAULT NULL,
  `pubInt_BPR_sal_50` int DEFAULT NULL,
  `pubInt_BPR_sal_75` int DEFAULT NULL,
  `pubInt_BPR_sal_mean` int DEFAULT NULL,
  `pubInt_JDP` int DEFAULT NULL,
  `pubInt_JDP_sal` int DEFAULT NULL,
  `pubInt_JDP_sal_25` int DEFAULT NULL,
  `pubInt_JDP_sal_50` int DEFAULT NULL,
  `pubInt_JDP_sal_75` int DEFAULT NULL,
  `pubInt_JDP_sal_mean` int DEFAULT NULL,
  `pubInt_PRO` int DEFAULT NULL,
  `pubInt_PRO_sal` int DEFAULT NULL,
  `pubInt_PRO_sal_25` int DEFAULT NULL,
  `pubInt_PRO_sal_50` int DEFAULT NULL,
  `pubInt_PRO_sal_75` int DEFAULT NULL,
  `pubInt_PRO_sal_mean` int DEFAULT NULL,
  `pubInt_NP` int DEFAULT NULL,
  `pubInt_NP_sal` int DEFAULT NULL,
  `pubInt_NP_sal_25` int DEFAULT NULL,
  `pubInt_NP_sal_50` int DEFAULT NULL,
  `pubInt_NP_sal_75` int DEFAULT NULL,
  `pubInt_NP_sal_mean` int DEFAULT NULL,
  `pubInt_Cred_UNK` int DEFAULT NULL,
  `inState_sal` int DEFAULT NULL,
  `inState_sal_25` int DEFAULT NULL,
  `inState_sal_50` int DEFAULT NULL,
  `inState_sal_75` int DEFAULT NULL,
  `inState_sal_mean` int DEFAULT NULL,
  `outOfState_sal` int DEFAULT NULL,
  `outOfState_sal_25` int DEFAULT NULL,
  `outOfState_sal_50` int DEFAULT NULL,
  `outOfState_sal_75` int DEFAULT NULL,
  `outOfState_sal_mean` int DEFAULT NULL,
  `region_NE` int DEFAULT NULL,
  `region_NE_sal` int DEFAULT NULL,
  `region_NE_sal_25` int DEFAULT NULL,
  `region_NE_sal_50` int DEFAULT NULL,
  `region_NE_sal_75` int DEFAULT NULL,
  `region_NE_sal_mean` int DEFAULT NULL,
  `region_MidAtl` int DEFAULT NULL,
  `region_MidAtl_sal` int DEFAULT NULL,
  `region_MidAtl_sal_25` int DEFAULT NULL,
  `region_MidAtl_sal_50` int DEFAULT NULL,
  `region_MidAtl_sal_75` int DEFAULT NULL,
  `region_MidAtl_sal_mean` int DEFAULT NULL,
  `region_ENC` int DEFAULT NULL,
  `region_ENC_sal` int DEFAULT NULL,
  `region_ENC_sal_25` int DEFAULT NULL,
  `region_ENC_sal_50` int DEFAULT NULL,
  `region_ENC_sal_75` int DEFAULT NULL,
  `region_ENC_sal_mean` int DEFAULT NULL,
  `region_WNC` int DEFAULT NULL,
  `region_WNC_sal` int DEFAULT NULL,
  `region_WNC_sal_25` int DEFAULT NULL,
  `region_WNC_sal_50` int DEFAULT NULL,
  `region_WNC_sal_75` int DEFAULT NULL,
  `region_WNC_sal_mean` int DEFAULT NULL,
  `region_SAtl` int DEFAULT NULL,
  `region_SAtl_sal` int DEFAULT NULL,
  `region_SAtl_sal_25` int DEFAULT NULL,
  `region_SAtl_sal_50` int DEFAULT NULL,
  `region_SAtl_sal_75` int DEFAULT NULL,
  `region_SAtl_sal_mean` int DEFAULT NULL,
  `region_ESC` int DEFAULT NULL,
  `region_ESC_sal` int DEFAULT NULL,
  `region_ESC_sal_25` int DEFAULT NULL,
  `region_ESC_sal_50` int DEFAULT NULL,
  `region_ESC_sal_75` int DEFAULT NULL,
  `region_ESC_sal_mean` int DEFAULT NULL,
  `region_WSC` int DEFAULT NULL,
  `region_WSC_sal` int DEFAULT NULL,
  `region_WSC_sal_25` int DEFAULT NULL,
  `region_WSC_sal_50` int DEFAULT NULL,
  `region_WSC_sal_75` int DEFAULT NULL,
  `region_WSC_sal_mean` int DEFAULT NULL,
  `region_Pac` int DEFAULT NULL,
  `region_Pac_sal` int DEFAULT NULL,
  `region_Pac_sal_25` int DEFAULT NULL,
  `region_Pac_sal_50` int DEFAULT NULL,
  `region_Pac_sal_75` int DEFAULT NULL,
  `region_Pac_sal_mean` int DEFAULT NULL,
  `region_Mtn` int DEFAULT NULL,
  `region_Mtn_sal` int DEFAULT NULL,
  `region_Mtn_sal_25` int DEFAULT NULL,
  `region_Mtn_sal_50` int DEFAULT NULL,
  `region_Mtn_sal_75` int DEFAULT NULL,
  `region_Mtn_sal_mean` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`graduation_year`),
  KEY `school_salary_school_id_index` (`school_id`),
  KEY `school_salary_graduation_year_index` (`graduation_year`),
  CONSTRAINT `school_salary_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_scorecards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_scorecards` (
  `school_id` bigint unsigned NOT NULL,
  `report_year` smallint unsigned NOT NULL,
  `UNITID` int DEFAULT NULL COMMENT 'IPEDS: Unit ID for institution',
  `OPEID6` int DEFAULT NULL COMMENT 'IPEDS/PEPS: 6-digit OPE ID for institution',
  `INSTNM` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IPEDS/PEPS: Institution name',
  `CREDDESC` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Text description of the level of credential',
  `ipedscount1` int DEFAULT NULL COMMENT 'IPEDS: Number of awards to all students in year 1 of the pooled debt cohort',
  `ipedscount2` int DEFAULT NULL COMMENT 'IPEDS: Number of awards to all students in year 2 of the pooled debt cohort',
  `debt_all_pp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed at all institutions',
  `debt_all_pp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed at all institutions',
  `debt_all_pp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed at all institutions',
  `debt_male_pp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to males at all institutions',
  `debt_male_pp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to males at all institutions',
  `debt_male_pp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to males at all institutions',
  `debt_notmale_pp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to non-males at all institutions',
  `debt_notmale_pp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to non-males at all institutions',
  `debt_notmale_pp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to non-males at all institutions',
  `debt_pell_pp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to Pell recipients at all institutions',
  `debt_pell_pp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to Pell recipients at all institutions',
  `debt_pell_pp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to Pell recipients at all institutions',
  `debt_nopell_pp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to non-Pell-recipients at all institutions',
  `debt_nopell_pp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to non-Pell-recipients at all institutions',
  `debt_nopell_pp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to non-Pell-recipients at all institutions',
  `debt_all_pp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed at this institution',
  `debt_all_pp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed at this institution',
  `debt_all_pp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed at this institution',
  `debt_male_pp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to males at this institution',
  `debt_male_pp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to males at this institution',
  `debt_male_pp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to males at this institution',
  `debt_notmale_pp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to non-males at this institution',
  `debt_notmale_pp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to non-males at this institution',
  `debt_notmale_pp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to non-males at this institution',
  `debt_pell_pp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to Pell recipients at this institution',
  `debt_pell_pp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to Pell recipients at this institution',
  `debt_pell_pp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to Pell recipients at this institution',
  `debt_nopell_pp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Student recipient count for average/median Parent PLUS loan debt disbursed to non-Pell-recipients at this institution',
  `debt_nopell_pp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Parent PLUS loan debt disbursed to non-Pell-recipients at this institution',
  `debt_nopell_pp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Parent PLUS loan debt disbursed to non-Pell-recipients at this institution',
  `debt_all_stgp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed at all institutions',
  `debt_all_stgp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed at all institutions',
  `debt_all_stgp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed at all institutions',
  `debt_male_stgp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to males at all institutions',
  `debt_male_stgp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to males at all institutions',
  `debt_male_stgp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to males at all institutions',
  `debt_notmale_stgp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to non-males at all institutions',
  `debt_notmale_stgp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to non-males at all institutions',
  `debt_notmale_stgp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to non-males at all institutions',
  `debt_pell_stgp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to Pell recipients at all institutions',
  `debt_pell_stgp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to Pell recipients at all institutions',
  `debt_pell_stgp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to Pell recipients at all institutions',
  `debt_nopell_stgp_any_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to non-Pell-recipients at all institutions',
  `debt_nopell_stgp_any_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to non-Pell-recipients at all institutions',
  `debt_nopell_stgp_any_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to non-Pell-recipients at all institutions',
  `debt_all_stgp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed at this institution',
  `debt_all_stgp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed at this institution',
  `debt_all_stgp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed at this institution',
  `debt_male_stgp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to males at this institution',
  `debt_male_stgp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to males at this institution',
  `debt_male_stgp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to males at this institution',
  `debt_notmale_stgp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to non-males at this institution',
  `debt_notmale_stgp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to non-males at this institution',
  `debt_notmale_stgp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to non-males at this institution',
  `debt_pell_stgp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to Pell recipients at this institution',
  `debt_pell_stgp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to Pell recipients at this institution',
  `debt_pell_stgp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to Pell recipients at this institution',
  `debt_nopell_stgp_eval_n` int DEFAULT NULL COMMENT 'NSLDS: Borrower count for average/median Stafford and Grad PLUS loan debt disbursed to non-Pell-recipients at this institution',
  `debt_nopell_stgp_eval_mean` int DEFAULT NULL COMMENT 'NSLDS: Average Stafford and Grad PLUS loan debt disbursed to non-Pell-recipients at this institution',
  `debt_nopell_stgp_eval_mdn` int DEFAULT NULL COMMENT 'NSLDS: Median Stafford and Grad PLUS loan debt disbursed to non-Pell-recipients at this institution',
  `debt_all_pp_any_mdn10yrpay` double(8,2) DEFAULT NULL COMMENT 'NSLDS: Median estimated monthly payment for Parent PLUS loan debt disbursed at all institutions',
  `debt_all_pp_eval_mdn10yrpay` double(8,2) DEFAULT NULL COMMENT 'NSLDS: Median estimated monthly payment for Parent PLUS loan debt disbursed at this institution',
  `debt_all_stgp_any_mdn10yrpay` double(8,2) DEFAULT NULL COMMENT 'NSLDS: Median estimated monthly payment for Stafford and Grad PLUS loan debt disbursed at all institutions',
  `debt_all_stgp_eval_mdn10yrpay` double(8,2) DEFAULT NULL COMMENT 'NSLDS: Median estimated monthly payment for Stafford and Grad PLUS loan debt disbursed at this institution',
  `earn_count_nwne_hi_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates not working and not enrolled 1 year after completing highest credential',
  `earn_count_wne_hi_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 1 year after completing highest credential',
  `earn_mdn_hi_1yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates working and not enrolled 1 year after completing highest credential',
  `earn_cntover150_hi_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled who earned more than 150% of the single-person household poverty threshold 1 year after completing highest credential.',
  `earn_count_nwne_hi_2yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates not working and not enrolled 2 years after completing highest credential',
  `earn_count_wne_hi_2yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 2 years after completing highest credential',
  `earn_mdn_hi_2yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates working and not enrolled 2 years after completing highest credential',
  `earn_cntover150_hi_2yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled who earned more than 150% of the single-person household poverty threshold 2 years after completing highest credential.',
  `bbrr2_fed_comp_n` int DEFAULT NULL COMMENT 'NSLDS: Federal student loan borrower-based 2-year borrower count of completers',
  `bbrr2_fed_comp_dflt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in default after 2 years',
  `bbrr2_fed_comp_dlnq` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in delinquency after 2 years',
  `bbrr2_fed_comp_fbr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in forbearance after 2 years',
  `bbrr2_fed_comp_dfr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in deferment after 2 years',
  `bbrr2_fed_comp_noprog` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers not making progress after 2 years',
  `bbrr2_fed_comp_makeprog` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers making progress after 2 years',
  `bbrr2_fed_comp_paidinfull` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers paid in full after 2 years',
  `bbrr2_fed_comp_discharge` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers with all loans discharged after 2 years',
  `bbrr3_fed_comp_n` int DEFAULT NULL COMMENT 'NSLDS: Federal student loan borrower-based 3-year borrower count of completers',
  `bbrr3_fed_comp_dflt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in default after 3 years',
  `bbrr3_fed_comp_dlnq` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in delinquency after 3 years',
  `bbrr3_fed_comp_fbr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in forbearance after 3 years',
  `bbrr3_fed_comp_dfr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in deferment after 3 years',
  `bbrr3_fed_comp_noprog` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers not making progress after 3 years',
  `bbrr3_fed_comp_makeprog` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers making progress after 3 years',
  `bbrr3_fed_comp_paidinfull` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers paid in full after 3 years',
  `bbrr3_fed_comp_discharge` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers with all loans discharged after 3 years',
  `earn_count_pell_wne_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates who received a Pell Grant and were working and not enrolled 1 year after completing highest credential',
  `earn_pell_wne_mdn_1yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates who received a Pell Grant and were working and not enrolled 1 year after completing highest credential',
  `earn_count_nopell_wne_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates who did not receive a Pell Grant and were working and not enrolled 1 year after completing highest credential',
  `earn_nopell_wne_mdn_1yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates who did not receive a Pell Grant and were working and not enrolled 1 year after completing highest credential',
  `earn_count_male_wne_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of male graduates working and not enrolled 1 year after completing highest credential',
  `earn_male_wne_mdn_1yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of male graduates working and not enrolled 1 year after completing highest credential',
  `earn_count_nomale_wne_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of non-male graduates working and not enrolled 1 year after completing highest credential',
  `earn_nomale_wne_mdn_1yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of non-male graduates working and not enrolled 1 year after completing highest credential',
  `earn_count_ne_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates not enrolled 3 years after completing highest credential',
  `earn_ne_mdn_3yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates not enrolled 3 years after completing highest credential',
  `earn_count_wne_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 3 years after completing highest credential',
  `earn_cntover150_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled who earned more than 150% of the single-person household poverty threshold 3 years after completing highest credential.',
  `earn_count_pell_ne_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates who received a Pell Grant and were not enrolled 3 years after completing highest credential',
  `earn_pell_ne_mdn_3yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates who received a Pell Grant and were not enrolled 3 years after completing highest credential',
  `earn_count_nopell_ne_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates who did not receive a Pell Grant and were not enrolled 3 years after completing highest credential',
  `earn_nopell_ne_mdn_3yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates who did not receive a Pell Grant and were not enrolled 3 years after completing highest credential',
  `earn_count_male_ne_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of male graduates not enrolled 3 years after completing highest credential',
  `earn_male_ne_mdn_3yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of male graduates not enrolled 3 years after completing highest credential',
  `earn_count_nomale_ne_3yr` int DEFAULT NULL COMMENT 'Treasury: Number of non-male graduates not enrolled 3 years after completing highest credential',
  `earn_nomale_ne_mdn_3yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of non-male graduates not enrolled 3 years after completing highest credential',
  `earn_count_nwne_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates not working and not enrolled 1 year after completing',
  `earn_count_wne_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 1 year after completing',
  `earn_mdn_1yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates working and not enrolled 1 year after completing',
  `earn_gt_threshold_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 1 year after completing who earned more than a high school graduate',
  `earn_count_high_cred_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 1 year after completing who went on to earn a higher credential',
  `earn_in_state_1yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 1 year after completing who were employed within the same state as the institution',
  `earn_count_nwne_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates not working and not enrolled 4 year after completing',
  `earn_count_wne_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 4 year after completing',
  `earn_mdn_4yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates working and not enrolled 4 year after completing',
  `earn_gt_threshold_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 4 years after completing who earned more than a high school graduate',
  `earn_count_pell_wne_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates who received a Pell Grant and were working and not enrolled 4 year after completing',
  `earn_pell_wne_mdn_4yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates who received a Pell Grant and were working and not enrolled 4 year after completing',
  `earn_count_nopell_wne_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates who did not receive a Pell Grant and were working and not enrolled 4 year after completing',
  `earn_nopell_wne_mdn_4yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of graduates who did not receive a Pell Grant and were working and not enrolled 4 year after completing',
  `earn_count_male_wne_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of male graduates working and not enrolled 4 year after completing',
  `earn_male_wne_mdn_4yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of male graduates working and not enrolled 4 year after completing',
  `earn_count_nomale_wne_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of non-male graduates working and not enrolled 4 year after completing',
  `earn_nomale_wne_mdn_4yr` int DEFAULT NULL COMMENT 'Treasury: Median earnings of non-male graduates working and not enrolled 4 year after completing',
  `earn_count_high_cred_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 4 years after completing who went on to earn a higher credential',
  `earn_in_state_4yr` int DEFAULT NULL COMMENT 'Treasury: Number of graduates working and not enrolled 4 years after completing who were employed within the same state as the institution',
  `bbrr1_fed_comp_n` int DEFAULT NULL COMMENT 'NSLDS: Federal student loan borrower-based 1-year borrower count of completers',
  `bbrr1_fed_comp_dflt` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in default after 1 years',
  `bbrr1_fed_comp_dlnq` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in delinquency after 1 years',
  `bbrr1_fed_comp_fbr` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in forbearance after 1 years',
  `bbrr1_fed_comp_dfr` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in deferment after 1 years',
  `bbrr1_fed_comp_noprog` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers not making progress after 1 years',
  `bbrr1_fed_comp_makeprog` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers making progress after 1 years',
  `bbrr1_fed_comp_paidinfull` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers paid in full after 1 years',
  `bbrr1_fed_comp_discharge` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers with all loans discharged after 1 years',
  `bbrr4_fed_comp_n` int DEFAULT NULL COMMENT 'NSLDS: Federal student loan borrower-based 4-year borrower count of completers',
  `bbrr4_fed_comp_dflt` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in default after 4 years',
  `bbrr4_fed_comp_dlnq` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in delinquency after 4 years',
  `bbrr4_fed_comp_fbr` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in forbearance after 4 years',
  `bbrr4_fed_comp_dfr` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers in deferment after 4 years',
  `bbrr4_fed_comp_noprog` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers not making progress after 4 years',
  `bbrr4_fed_comp_makeprog` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers making progress after 4 years',
  `bbrr4_fed_comp_paidinfull` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers paid in full after 4 years',
  `bbrr4_fed_comp_discharge` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NSLDS: Percentage of undergraduate completer undergraduate federal student loan borrowers with all loans discharged after 4 years',
  PRIMARY KEY (`school_id`,`report_year`),
  CONSTRAINT `school_scorecards_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `school_scores`;
/*!50001 DROP VIEW IF EXISTS `school_scores`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `school_scores` AS SELECT 
 1 AS `school_id`,
 1 AS `graduation_year`,
 1 AS `employment_score`,
 1 AS `underemployment_score`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `school_states`;
/*!50001 DROP VIEW IF EXISTS `school_states`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `school_states` AS SELECT 
 1 AS `school_id`,
 1 AS `graduation_year`,
 1 AS `state_code`,
 1 AS `emp`,
 1 AS `percent`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `school_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `school_transfers` (
  `school_id` bigint unsigned NOT NULL,
  `academic_year` smallint unsigned NOT NULL,
  `transfers_in` int DEFAULT NULL,
  `gpa_75` double(8,2) DEFAULT NULL,
  `gpa_50` double(8,2) DEFAULT NULL,
  `gpa_25` double(8,2) DEFAULT NULL,
  `transfers_out` int DEFAULT NULL,
  PRIMARY KEY (`school_id`,`academic_year`),
  KEY `school_transfers_school_id_index` (`school_id`),
  KEY `school_transfers_academic_year_index` (`academic_year`),
  CONSTRAINT `school_transfers_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `schools`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schools` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_limited` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `old_slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `aba_id` int DEFAULT NULL,
  `lsac_id` int DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` point DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `indicators` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `accepts_gre` tinyint(1) NOT NULL,
  `note` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `delete_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `schools_short_unique` (`slug`),
  UNIQUE KEY `schools_name_unique` (`name`),
  UNIQUE KEY `schools_lsac_id_unique` (`lsac_id`),
  KEY `schools_state_code_foreign` (`state_code`),
  CONSTRAINT `schools_state_code_foreign` FOREIGN KEY (`state_code`) REFERENCES `states` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sponsors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sponsors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sponsors_name_unique` (`name`),
  UNIQUE KEY `sponsors_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `state_borders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `state_borders` (
  `state_code` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `border_code` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`state_code`,`border_code`),
  KEY `state_borders_border_code_foreign` (`border_code`),
  CONSTRAINT `state_borders_border_code_foreign` FOREIGN KEY (`border_code`) REFERENCES `states` (`code`),
  CONSTRAINT `state_borders_state_code_foreign` FOREIGN KEY (`state_code`) REFERENCES `states` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `state_reports`;
/*!50001 DROP VIEW IF EXISTS `state_reports`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `state_reports` AS SELECT 
 1 AS `state_code`,
 1 AS `school_count`,
 1 AS `employment_score`,
 1 AS `underemployment_score`,
 1 AS `cost_of_living`,
 1 AS `median_earnings`,
 1 AS `tuition`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `states` (
  `code` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`code`),
  UNIQUE KEY `states_name_unique` (`name`),
  UNIQUE KEY `states_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `employer_type` tinyint(1) NOT NULL DEFAULT '0',
  `practice_area` tinyint(1) NOT NULL DEFAULT '0',
  `state` tinyint(1) NOT NULL DEFAULT '0',
  `glossary` tinyint(1) NOT NULL DEFAULT '0',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tags_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `family_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT '1',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  KEY `telescope_entries_tags_entry_uuid_tag_index` (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `term_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `term_tag` (
  `term_id` bigint unsigned NOT NULL,
  `tag_id` bigint unsigned NOT NULL,
  `order` int DEFAULT NULL,
  PRIMARY KEY (`term_id`,`tag_id`),
  KEY `term_tag_term_id_index` (`term_id`),
  KEY `term_tag_tag_id_index` (`tag_id`),
  CONSTRAINT `term_tag_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`),
  CONSTRAINT `term_tag_term_id_foreign` FOREIGN KEY (`term_id`) REFERENCES `terms` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `terms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `terms` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `terms_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_finances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_finances` (
  `user_id` bigint unsigned NOT NULL,
  `advisor_available` int NOT NULL DEFAULT '0',
  `y1_treasury` double(4,2) DEFAULT NULL,
  `y2_treasury` double(4,2) DEFAULT NULL,
  `y3_treasury` double(4,2) DEFAULT NULL,
  `y4_treasury` double(4,2) DEFAULT NULL,
  `y5_treasury` double(4,2) DEFAULT NULL,
  `y1_contribution` int DEFAULT NULL,
  `y2_contribution` int DEFAULT NULL,
  `y3_contribution` int DEFAULT NULL,
  `y4_contribution` int DEFAULT NULL,
  `y5_contribution` int DEFAULT NULL,
  `target_debt` int DEFAULT NULL,
  `target_debt_period` int DEFAULT NULL,
  `target_tax` int DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_finances_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_reports` (
  `user_id` bigint unsigned NOT NULL,
  `step1` tinyint(1) NOT NULL DEFAULT '0',
  `step2` tinyint(1) NOT NULL DEFAULT '0',
  `step3` tinyint(1) NOT NULL DEFAULT '0',
  `step4` tinyint(1) NOT NULL DEFAULT '0',
  `step5` tinyint(1) NOT NULL DEFAULT '0',
  `states` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `national` tinyint(1) DEFAULT NULL,
  `priority_law` tinyint DEFAULT NULL,
  `priority_firm` tinyint DEFAULT NULL,
  `priority_biglaw` tinyint DEFAULT NULL,
  `priority_public` tinyint DEFAULT NULL,
  `priority_clerk` tinyint DEFAULT NULL,
  `flag_pt` tinyint DEFAULT NULL,
  `flag_race` tinyint DEFAULT NULL,
  `flag_gender` tinyint DEFAULT NULL,
  `flag_bar` tinyint DEFAULT NULL,
  `flag_attrition` tinyint DEFAULT NULL,
  `flag_size` tinyint DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_reports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_schools`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_schools` (
  `user_id` bigint unsigned NOT NULL,
  `school_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `user_schools_school_id_user_id_unique` (`school_id`,`user_id`),
  KEY `user_schools_user_id_foreign` (`user_id`),
  CONSTRAINT `user_schools_school_id_foreign` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`),
  CONSTRAINT `user_schools_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_states` (
  `user_id` bigint unsigned NOT NULL,
  `state_code` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`user_id`,`state_code`),
  KEY `user_states_state_code_foreign` (`state_code`),
  CONSTRAINT `user_states_state_code_foreign` FOREIGN KEY (`state_code`) REFERENCES `states` (`code`),
  CONSTRAINT `user_states_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_states_preferred`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_states_preferred` (
  `user_id` bigint unsigned NOT NULL,
  `state_code` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`user_id`,`state_code`),
  KEY `user_states_preferred_state_code_foreign` (`state_code`),
  CONSTRAINT `user_states_preferred_state_code_foreign` FOREIGN KEY (`state_code`) REFERENCES `states` (`code`),
  CONSTRAINT `user_states_preferred_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `first` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT '0',
  `avatax_code` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `saml_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lcc_id` bigint unsigned DEFAULT NULL,
  `lcc_latest` timestamp NULL DEFAULT NULL,
  `lcc_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` tinyint unsigned DEFAULT NULL,
  `race` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lsat` tinyint unsigned DEFAULT NULL,
  `gpa` decimal(3,2) unsigned DEFAULT NULL,
  `parental_education` tinyint unsigned DEFAULT NULL,
  `lsac_waiver` tinyint unsigned DEFAULT NULL,
  `degree_year` smallint unsigned DEFAULT NULL,
  `lds` tinyint unsigned DEFAULT NULL,
  `stripe_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stripe_payment_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stripe_last_four` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stripe_brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stripe_expiration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `compass_plan` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `compass_status` tinyint DEFAULT NULL,
  `compass_joined_at` datetime DEFAULT NULL,
  `compass_terms_accepted_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_saml_id_unique` (`saml_id`),
  UNIQUE KEY `users_avatax_code_unique` (`avatax_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `webhooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhooks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 DROP FUNCTION IF EXISTS `config` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` FUNCTION `config`(name VARCHAR(255)) RETURNS text CHARSET utf8mb4
    DETERMINISTIC
BEGIN
    RETURN CASE name
        WHEN "employment_year" THEN (SELECT max(graduation_year) FROM school_employment_aba)
        WHEN "prices_year" THEN (SELECT max(academic_year) FROM school_prices)
        WHEN "scorecard_year" THEN (SELECT max(report_year) FROM school_scorecards)
        ELSE (SELECT `value` FROM settings WHERE `key` = cast(name AS CHAR))
    end;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50001 DROP VIEW IF EXISTS `school_compares`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `school_compares` AS select `reports_a`.`school_id` AS `school_a_id`,`reports_b`.`school_id` AS `school_b_id`,concat_ws('-vs-',`school_a`.`slug`,`school_b`.`slug`) AS `slug`,true AS `national` from (((`school_reports` `reports_a` left join `school_reports` `reports_b` on(((`reports_b`.`national` = true) and (`reports_a`.`school_id` <> `reports_b`.`school_id`)))) left join `schools` `school_a` on(((`reports_a`.`school_id` = `school_a`.`id`) and (`school_a`.`deleted_at` is null)))) left join `schools` `school_b` on(((`reports_b`.`school_id` = `school_b`.`id`) and (`school_b`.`deleted_at` is null)))) where ((`reports_a`.`national` = true) and (`school_a`.`slug` < `school_b`.`slug`)) union select `states_a`.`school_id` AS `school_a_id`,`states_b`.`school_id` AS `school_b_id`,concat_ws('-vs-',`school_a`.`slug`,`school_b`.`slug`) AS `slug`,false AS `national` from (((((`school_states` `states_a` left join `school_states` `states_b` on(((`states_a`.`state_code` = `states_b`.`state_code`) and (`states_a`.`school_id` <> `states_b`.`school_id`)))) left join `schools` `school_a` on(((`states_a`.`school_id` = `school_a`.`id`) and (`school_a`.`deleted_at` is null)))) left join `schools` `school_b` on(((`states_b`.`school_id` = `school_b`.`id`) and (`school_b`.`deleted_at` is null)))) left join `school_reports` `report_a` on((`states_a`.`school_id` = `report_a`.`school_id`))) left join `school_reports` `report_b` on((`states_b`.`school_id` = `report_b`.`school_id`))) where ((`states_a`.`graduation_year` > (`config`('employment_year') - `config`('report_years'))) and (`states_b`.`graduation_year` > (`config`('employment_year') - `config`('report_years'))) and (`school_a`.`slug` < `school_b`.`slug`) and ((0 = `report_a`.`national`) or (0 = `report_b`.`national`))) group by `school_a_id`,`school_b_id` order by `slug` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!50001 DROP VIEW IF EXISTS `school_reports`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `school_reports` AS select `schools`.`id` AS `school_id`,(avg(((((`school_employment_aba`.`federal_LT_FT` + `school_employment_aba`.`firm_251_500_LT_FT`) + `school_employment_aba`.`firm_501_LT_FT`) / `school_employment_aba`.`grads`) * 100)) > `config`('national_min_biglaw')) AS `national`,max(`school_scores`.`employment_score`) AS `employment_score`,max(`school_scores`.`underemployment_score`) AS `underemployment_score`,cast(round(avg(`school_employment_aba`.`grads`),0) as signed) AS `grads`,cast(round(avg(`school_employment_aba`.`emp`),0) as signed) AS `emp`,round(avg(((((`school_employment_aba`.`federal_LT_FT` + `school_employment_aba`.`firm_251_500_LT_FT`) + `school_employment_aba`.`firm_501_LT_FT`) / `school_employment_aba`.`grads`) * 100)),1) AS `biglaw`,round(avg(((((((`school_employment_aba`.`federal_LT_FT` + `school_employment_aba`.`state_LT_FT`) + `school_employment_aba`.`otherClerk_LT_FT`) + `school_employment_aba`.`tribal_LT_FT`) + `school_employment_aba`.`intl_LT_FT`) / `school_employment_aba`.`grads`) * 100)),1) AS `clerk`,round(avg((((`school_employment_aba`.`firm_LT_FT` + `school_employment_aba`.`federal_LT_FT`) / `school_employment_aba`.`grads`) * 100)),1) AS `firm`,round(avg(((`school_employment_aba`.`BPR_LT_FT` / `school_employment_aba`.`grads`) * 100)),1) AS `lawyer`,round(avg(((((`school_employment_aba`.`federal_LT_FT` + `school_employment_aba`.`firm_251_500_LT_FT`) + `school_employment_aba`.`firm_501_LT_FT`) / `school_employment_aba`.`grads`) * 100)),1) AS `natl`,round(avg((((`school_employment_aba`.`govt_LT_FT` + `school_employment_aba`.`pubInt_LT_FT`) / `school_employment_aba`.`grads`) * 100)),1) AS `public` from ((`schools` left join `school_employment_aba` on(((`school_employment_aba`.`school_id` = `schools`.`id`) and (`school_employment_aba`.`graduation_year` > (`config`('employment_year') - `config`('report_years')))))) left join `school_scores` on(((`school_scores`.`school_id` = `schools`.`id`) and (`school_scores`.`graduation_year` = (select max(`school_scores`.`graduation_year`) from `school_scores` where (`school_scores`.`school_id` = `schools`.`id`)))))) group by `schools`.`id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!50001 DROP VIEW IF EXISTS `school_scores`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `school_scores` AS select `school_employment_aba`.`school_id` AS `school_id`,`school_employment_aba`.`graduation_year` AS `graduation_year`,round(((((`school_employment_aba`.`BPR_LT_FT` - `school_employment_aba`.`firm_solo_LT_FT`) - (case when (`school_employment_aba`.`graduation_year` < 2015) then `school_employment_aba`.`sf_BPR_LT_FT` else 0 end)) / `school_employment_aba`.`grads`) * 100),1) AS `employment_score`,round(((((((((((((((((((((`school_employment_aba`.`unemp_seek` + `school_employment_aba`.`FTDegree`) + `school_employment_aba`.`BPR_LT_PT`) + `school_employment_aba`.`BPR_ST_FT`) + `school_employment_aba`.`BPR_ST_PT`) + `school_employment_aba`.`JDP_LT_PT`) + `school_employment_aba`.`JDP_ST_FT`) + `school_employment_aba`.`JDP_ST_PT`) + `school_employment_aba`.`PRO_LT_PT`) + `school_employment_aba`.`PRO_ST_FT`) + `school_employment_aba`.`PRO_ST_PT`) + `school_employment_aba`.`NP_LT_FT`) + `school_employment_aba`.`NP_LT_PT`) + `school_employment_aba`.`NP_ST_FT`) + `school_employment_aba`.`NP_ST_PT`) + `school_employment_aba`.`UNK_LT_PT`) + `school_employment_aba`.`UNK_ST_FT`) + `school_employment_aba`.`UNK_ST_PT`) + (case when (`school_employment_aba`.`graduation_year` = 2011) then `school_employment_aba`.`school_funded_LT_FT` when (`school_employment_aba`.`graduation_year` < 2015) then ((`school_employment_aba`.`sf_BPR_LT_FT` + `school_employment_aba`.`sf_JDP_LT_FT`) + `school_employment_aba`.`sf_PRO_LT_FT`) when (`school_employment_aba`.`graduation_year` > 2014) then (((`school_employment_aba`.`school_funded_LT_FT` + `school_employment_aba`.`school_funded_LT_PT`) + `school_employment_aba`.`school_funded_ST_FT`) + `school_employment_aba`.`school_funded_ST_PT`) else 0 end)) / `school_employment_aba`.`grads`) * 100),1) AS `underemployment_score` from `school_employment_aba` where ((`school_employment_aba`.`emp` > 0) and (`school_employment_aba`.`graduation_year` >= 2012)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!50001 DROP VIEW IF EXISTS `school_states`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `school_states` AS select `school_employment_states`.`school_id` AS `school_id`,`school_employment_states`.`graduation_year` AS `graduation_year`,`school_employment_states`.`state_code` AS `state_code`,`school_employment_states`.`emp` AS `emp`,round(((`school_employment_states`.`emp` / `school_employment_aba`.`grads`) * 100),1) AS `percent` from (`school_employment_states` left join `school_employment_aba` on(((`school_employment_aba`.`school_id` = `school_employment_states`.`school_id`) and (`school_employment_aba`.`graduation_year` = `school_employment_states`.`graduation_year`)))) where ((`school_employment_states`.`graduation_year` = (select max(`x`.`graduation_year`) from `school_employment_states` `x` where ((`school_employment_states`.`school_id` = `x`.`school_id`) and (`school_employment_states`.`state_code` = `x`.`state_code`)))) and (`school_employment_states`.`graduation_year` > (`config`('employment_year') - `config`('report_years')))) union select `schools`.`id` AS `id`,`school_employment_states`.`graduation_year` AS `graduation_year`,`schools`.`state_code` AS `state_code`,NULL AS `emp`,NULL AS `percent` from (`schools` left join `school_employment_states` on(((`school_employment_states`.`graduation_year` = `config`('employment_year')) and (`school_employment_states`.`school_id` = `schools`.`id`) and (`school_employment_states`.`state_code` = `schools`.`state_code`)))) where (`school_employment_states`.`emp` is null) order by `school_id`,`state_code`,`graduation_year` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!50001 DROP VIEW IF EXISTS `state_reports`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `state_reports` AS select `states`.`code` AS `state_code`,count(`schools`.`id`) AS `school_count`,round(avg(`school_reports`.`employment_score`),1) AS `employment_score`,round(avg(`school_reports`.`underemployment_score`),1) AS `underemployment_score`,cast(avg(`school_prices`.`col`) as signed) AS `cost_of_living`,cast(avg(`school_scorecards`.`earn_mdn_1yr`) as signed) AS `median_earnings`,cast(avg((case when (`school_prices`.`resident_tuition_FT` > 0) then `school_prices`.`resident_tuition_FT` else `school_prices`.`tuition_FT` end)) as signed) AS `tuition` from (((((`states` left join `school_states` on(((`school_states`.`state_code` = `states`.`code`) and ((`school_states`.`percent` >= `config`('state_min_percent')) or (`school_states`.`percent` is null))))) left join `schools` on(((`schools`.`id` = `school_states`.`school_id`) and (`schools`.`deleted_at` is null)))) left join `school_prices` on(((`school_prices`.`school_id` = `schools`.`id`) and (`school_prices`.`academic_year` = (select max(`school_prices`.`academic_year`) from `school_prices` where (`school_prices`.`school_id` = `schools`.`id`)))))) left join `school_reports` on((`school_reports`.`school_id` = `schools`.`id`))) left join `school_scorecards` on(((`school_scorecards`.`school_id` = `schools`.`id`) and (`school_scorecards`.`report_year` = (select max(`school_scorecards`.`report_year`) from `school_scorecards` where (`school_scorecards`.`school_id` = `schools`.`id`)))))) group by `states`.`code` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2019_05_03_000001_create_customer_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2019_05_03_000002_create_subscriptions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2019_05_03_000003_create_subscription_items_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2020_06_09_002626_create_modules_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2020_06_11_001811_create_courses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2020_06_14_210542_create_blogs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2020_06_14_213505_create_course_module_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2020_06_16_165035_create_submissions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2020_06_17_045238_create_authors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2020_06_17_045931_create_author_module_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2020_06_17_050535_create_author_blog_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2020_06_19_023345_create_course_user_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2020_06_19_023753_create_module_user_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2020_06_23_140746_create_module_ratings_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2020_06_26_030107_create_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2020_07_17_025427_create_roles_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2020_07_25_174101_create_schools_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2020_07_25_180804_create_applications',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2020_07_25_184012_create_worksheets',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2020_07_25_184038_create_user_finances',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2020_07_25_220122_create_wizards',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2020_07_26_042038_create_components_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2020_07_26_042143_create_deadlines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2020_07_26_042301_create_meta_definitions',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2020_07_26_043604_create_application_component',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2020_08_11_144120_create_organizations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2020_08_11_193245_create_advisors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2020_08_17_041456_create_podcasts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2020_08_20_042038_create_component_user_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2020_08_31_032825_create_ed_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2020_08_31_044625_create_employment_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2020_08_31_185017_create_salary_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2020_09_03_143846_create_admissions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2020_09_03_165850_create_debt_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2020_09_03_173628_create_enrollments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2020_09_04_025733_create_bar_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2020_09_04_160710_create_environment_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2020_09_24_100000_create_guides_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2020_09_29_164710_create_credentials_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2020_10_12_043551_create_invites_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2020_10_23_190821_create_invoices_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2020_10_25_223912_create_user_information',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2020_10_26_174546_advisor_invite_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2020_11_25_053143_create_scholarships_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2018_01_01_000000_create_action_events_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2019_05_10_000000_add_fields_to_action_events_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2018_08_08_100000_create_telescope_entries_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2020_12_02_070528_reorder_guides',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2020_12_02_083302_switch_to_storage_assets',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2020_12_02_103803_tag_booleans',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2020_12_03_063229_add_stripe_columns_to_users',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2020_12_03_064711_switch_titles_and_slugs_to_non_nulls',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2020_12_03_065327_remove_timestamps_from_author_link_tables',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2020_12_03_082958_update_user_stripe_fields',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2020_12_03_090906_update_advisor_pivot_with_stripe',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2020_12_04_025300_add_pk_to_worksheets_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2020_12_05_034335_import_api_tokens',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2020_12_09_200211_create_webhook_calls_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2020_12_18_184543_update_admissions_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2020_12_18_191545_import2020_admissions',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2020_12_18_204233_import2020_attrition',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2020_12_18_205351_import2020CS',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2020_12_18_205652_import2020_enrollments',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2020_12_18_212333_import2020_environment',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2020_12_18_213006_import2020_faculty',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2020_12_18_213857_import2020_grants',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2020_12_18_215617_import2020_transfers',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2021_02_17_212034_lsatprep',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2021_02_22_191545_import2020_admissions_update',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2021_04_21_141010_update_aba_employment_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2021_04_22_104101_import_2020_aba_employment',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2021_04_22_191025_import2020_aba_location_data',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2021_04_23_210600_update_ultimate_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2021_04_23_214533_import_2020_ultimate_bar_data',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2021_04_23_221841_import_2020_first_bar_data',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2021_04_28_152116_import_2020_debt',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2021_07_01_211834_create_citations_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2021_07_01_211835_import_citations',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2021_08_11_145415_update_courses_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2021_08_12_181904_update_tags',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2021_08_12_182345_course_tag_table',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2021_08_24_193704_update_modules_table',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2021_08_30_162609_update_user__information',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2021_09_02_195309_create_user_school_rating_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2021_09_07_181950_upgrade_tags',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2021_09_07_182237_glossary_tables',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2021_09_16_221609_terms_update_sept',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2021_09_30_195017_fix2020grants',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2021_10_05_150007_create_webhook_log_table',28);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2021_12_29_081530_update_aba_actions_table',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2021_12_29_092345_update_academic_years_table',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2022_02_21_094359_add_saml_id_to_users_table',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2022_03_14_053352_add_compass_terms_accepted_at_to_users_table',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2022_04_20_063101_import_2021_aba_employment',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2022_04_20_063109_import_2021_aba_location_data',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2022_06_27_100000_add_spanish_lsat_and_gre_to_school_admissions_table',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2022_06_27_100001_import_2021_admissions',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2022_06_27_100001_import_2021_debt',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2022_06_27_100001_import_2021_faculty',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2022_06_27_100001_import_2021_first_bar_data',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2022_06_27_100001_import_2021_grants',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2022_06_27_100001_import_2021_transfers',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2022_06_27_100001_import_2021_ultimate_bar_data',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2022_06_27_100002_import_2021_attrition',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2022_06_27_100003_import_2021_conditional_scholarships',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2022_06_27_100004_import_2021_enrollments',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2022_06_27_100005_import_2021_environment',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2022_08_15_124507_add_address_and_lsac_id_to_schools_table',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2022_08_15_125908_import_addresses_and_lsac_ids',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (115,'2022_09_28_093759_add_part_time_to_worksheets_table',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2022_11_14_071728_burn_it_all',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2022_11_15_062042_rename_worksheets_to_budgets',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2022_11_15_071000_rename_applications_to_user_schools',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2022_11_16_084017_merge_credentials_into_user_information',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2022_11_17_060002_merge_organizations_into_users',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2022_11_18_101029_rename_user_wizard_to_user_reports',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2022_12_05_070900_backfill_user_tables',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2022_12_31_100001_import_2022_admissions',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2022_12_31_100002_import_2022_debt',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2022_12_31_100003_import_2022_faculty',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2022_12_31_100004_import_2022_grants',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2022_12_31_100005_import_2022_transfers',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2022_12_31_100006_import_2022_attrition',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2022_12_31_100007_import_2022_conditional_scholarships',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2022_12_31_100008_import_2022_enrollments',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2022_12_31_100009_import_2022_environment',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2022_12_31_100010_import_2022_prices',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2023_01_09_072641_drop_dataset_school_names',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2022_12_31_100000_import_2022_academic_years',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2023_04_18_040228_drop_unused_fields',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2023_04_18_102903_add_budgets_transportation',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2023_04_20_163039_create_states_table',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2023_04_20_182321_create_user_states_table',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2023_04_25_090658_drop_compass_basic',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2023_04_25_144126_add_user_information_lds',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2023_04_26_052936_add_avatax_code_to_users',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (142,'2023_05_01_100000_import_2022_baroutcomes_first',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2023_05_01_100000_import_2022_baroutcomes_ultimate',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2023_05_01_100000_import_2022_employment_aba',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2023_05_01_100000_import_2022_employment_locations',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2023_05_03_104520_bar_first_washington_dc',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2023_05_08_021710_import_2022_accepts_gre',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2021_08_25_193039_create_nova_notifications_table',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (149,'2022_04_26_000000_add_fields_to_nova_notifications_table',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (150,'2022_12_19_000000_create_field_attachments_table',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2023_05_05_164456_create_user_states_preferred_table',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2023_05_08_021710_move_accepts_gre_to_schools',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2023_05_18_073105_school_timestamps',45);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (154,'2023_05_19_161017_fix_school_grants_data',45);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2023_05_25_043701_announcement_and_guide_soft_deletes',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2023_05_25_061040_trash_author_relations',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2023_05_25_061154_trash_announcement_booleans',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (158,'2023_05_25_062933_author_primary_key',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2023_06_07_093953_merge_user_information',47);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2023_06_23_051439_update_spotify_urls',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2023_06_29_091858_add_transcript_to_podcasts_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (162,'2023_07_01_061212_add_ids_to_podcasts_table',49);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (163,'2023_07_30_091939_add_audio_url_and_slug_to_podcasts_table',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (164,'2023_08_16_104750_add_biglaw_count',51);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (165,'2023_08_29_070802_separate_pathways_and_tags',52);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (166,'2023_09_12_080032_add_image_url_to_podcasts_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2023_09_13_093601_add_name_to_authors_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2023_09_14_070120_create_sponsors_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2023_09_14_070903_rename_authors_to_hosts',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2023_09_20_085611_add_missing_unique_constraints',54);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2019_12_14_000001_create_personal_access_tokens_table',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2023_10_10_042653_rename_school_short_to_slug',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2023_10_13_052448_add_is_admin_to_users_table',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2023_10_12_210004_fix_reports_empty_location',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2023_10_20_104015_rename_school_types',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (176,'2023_11_23_055140_add_school_website_url',59);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (177,'2023_12_04_035521_add_delete_reason_to_schools',60);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (178,'2023_12_12_194100_add_completed_at_to_user_reports_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2023_12_19_210402_add_aba_id_to_schools_table',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2023_12_21_100000_import_2023_academic_years',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2023_12_21_100001_import_2023_admissions',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2023_12_21_100003_import_2023_faculty',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (183,'2023_12_21_100004_import_2023_grants',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (184,'2023_12_21_100005_import_2023_transfers',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (185,'2023_12_21_100006_import_2023_attrition',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (186,'2023_12_21_100007_import_2023_conditional_scholarships',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (187,'2023_12_21_100008_import_2023_enrollments',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (188,'2023_12_21_100009_import_2023_environment',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (189,'2023_12_21_100010_import_2023_prices',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (190,'2023_12_21_100011_add_school_tuition_pt',63);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2023_12_26_162828_remove_old_inflation_from_academic_years_table',64);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2023_12_26_163445_add_source_experience_to_academic_years_table',64);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2023_12_28_150912_create_school_scorecards_table',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2023_12_28_162944_change_year_fields_to_year_types',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2023_12_28_173436_change_text_fields_to_string_types',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2023_12_28_175132_import_2023_scorecards',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2024_01_01_142329_update_school_grants_year_to_2022',66);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2024_01_01_201236_soft_delete_sponsors',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2024_01_04_195251_change_year_fields_to_int_types',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2024_01_04_202840_change_inflation_to_decimal',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2024_02_21_082808_set_max_20_user_states_preferred',69);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2019_08_13_000000_create_nova_settings_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (203,'2021_02_15_000000_update_nova_settings_value_column',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2024_02_23_204430_create_school_employment_states_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2024_02_24_210709_import_settings',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2024_02_29_192936_rename_schools_state_to_state_code',71);
