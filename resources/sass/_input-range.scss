/*********** Baseline, reset styles ***********/
.l-range-input {
  input[type="range"] {
    @apply cursor-pointer bg-transparent py-3 appearance-none;
  }

  /* Removes default focus */
  input[type="range"]:focus {
    outline: none;
  }

  /******** Chrome, Safari, Opera and Edge Chromium styles ********/

  /* slider track */
  input[type="range"]::-webkit-slider-runnable-track {
    @apply bg-gray-300 rounded-full h-2;
  }

  /* slider thumb */
  input[type="range"]::-webkit-slider-thumb {
    @apply appearance-none h-7 w-4 rounded-full bg-blue-600 shadow-md -translate-y-2.5;

  }

  input[type="range"]:focus::-webkit-slider-thumb {
    outline: 2px solid theme("colors.blue.500");
    outline-offset: 0.125rem;
  }

  /*********** Firefox styles ***********/

  /* slider track */
  input[type="range"]::-moz-range-track {
    @apply bg-gray-300 rounded-full h-2;
  }

  /* slider thumb */
  input[type="range"]::-moz-range-thumb {
    @apply h-8 w-4 rounded-full bg-blue-600 shadow-md border-0;
  }

  input[type="range"]:focus::-moz-range-thumb{
    outline: 2px solid theme("colors.blue.500");
    outline-offset: 0.125rem;
  }
}
