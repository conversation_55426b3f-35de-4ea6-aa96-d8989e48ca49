/* stylelint-disable selector-class-pattern */

/* Block out the checkbox control underneath the icon */
input[type="checkbox"] {
  position: relative;

  &.fa-square,
  &.fa-square-check {
    &::before {
      display: block;
      width: 100%;
      height: 100%;
      background: white;
    }
  }
}

// temporary replacement for bootstrap form-select while Trends can't use blade/vue components
.l-select {
  @apply bg-[url("/images/chevron-down.svg")] bg-[position:right_0.5em_center] bg-[length:1.5em_1.5em] bg-no-repeat;
  @apply block w-full border border-gray-300 dark:border-gray-800;
  @apply appearance-none shadow-sm;

  &:not([class$="-sm"]) {
    @apply rounded-md pl-3 pr-8 py-2;
  }

  &-sm {
    @apply rounded pl-2 pr-8 py-1.5 text-sm;
  }
}
