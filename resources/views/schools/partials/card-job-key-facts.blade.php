@php
    $admissions = $school->admissions->take(-4);
    $a = $admissions->first()->enrollees_All;
    $b = $admissions->last()->enrollees_All;
    $change = round(abs(($a - $b) / max($a, 1)) * 100, 1);
    $admission_year = ($school->latest_employment_aba?->graduation_year - 2) ?? null;
    $transfers = $school->transfers($admission_year, $admission_year)->first();
@endphp

<card>
    <template #header>
        <h2 class="font-medium text-blue dark:text-white">
            Key facts
        </h2>
    </template>

    <template #body>
        <ul class="leading-relaxed pl-4">
            <li>
                {{ $school->latest_employment_aba->grads }} total grads in {{ $school->latest_employment_aba->graduation_year }},
                @if ($transfers)
                    including <span>{{ $transfers->transfers_in }}</span> transfer students
                @endif
            </li>
            <li><span>{{ Math::percent($school->latest_employment_aba->BPR_LT_FT, $school->latest_employment_aba->grads, "%") }}</span> had long-term, full-time legal jobs</li>
            <li>{{ $school->employed_long_term_percent }}% had any long-term job</li>
            <li>{{ $school->employed_full_time_percent }}% had any full-time job</li>
            <li>
                <span>{{ $admissions->last()->enrollees_All }}</span>
                new 1L students in {{ $admissions->last()->admission_year }},
                <span class="{{ $a < $b ? 'text-green' : 'text-red' }}">
                    {{ $a < $b  ? '+' : '-' }}{{ $change }}%
                </span>
                from {{ $admissions->first()->admission_year }}
            </li>
        </ul>
    </template>
</card>
