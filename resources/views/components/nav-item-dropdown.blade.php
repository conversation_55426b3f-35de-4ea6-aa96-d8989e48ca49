<ul @class($class)>
    @foreach ($item->children as $child)
        <li>
            <a href="{{ $child->url }}"{{ $child->active ? ' aria-current="page"' : '' }} @class([
                "rounded md:hover:bg-gray-100 md:dark:hover:bg-gray-800/50 p-3 flex gap-3 hover:text-current transition duration-150 ease-in-out",
                'bg-yellow-400/10 md:bg-transparent' => $child->active,
            ])>
                <i class="-mt-0.5 text-xl fa-regular {{ $child->icon }} fa-fw text-orange-600"></i>
                <div class="flex-1">
                    <div class="font-semibold text-blue dark:text-white text-lg mb-1">
                        {{ $child->title }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed font-normal">
                        {{ $child->description }}
                    </div>
                </div>
            </a>
        </li>
    @endforeach
</ul>
