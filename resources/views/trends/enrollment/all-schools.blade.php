@extends('layouts.trends')

@php
    $d_year1 = 2011;
    $d_year2 = $years['latestEnrollment'];

    // Settings

    if (Request::input('y1') >= $years['firstJDEnrollment'] && Request::input('y1') < $d_year2) {
        $year1 = Request::input('y1');
    } else {
        $year1 = $d_year2 - 10;
    }
    if (Request::input('y2') > $d_year1 && Request::input('y2') <= $d_year2) {
        $year2 = Request::input('y2');
    } else {
        $year2 = $d_year2;
    }
@endphp

@section('title', "Enrollment by Law School in the U.S., ${year1} vs ${year2} Comparison")

@section('description', "A list of U.S. law schools with their 1L enrollment and JD enrollment in ${year1} and ${year2}.")

@section('settings')
    <form class="grid gap-2" method="get">
        <fieldset class="flex flex-row gap-2">
            <legend class="font-medium cursor-default mb-1" onclick="this.parentElement.elements[0].focus()">Years</legend>
            <label class="sr-only" for="y1">Start year</label>
            <l-select id="y1" name="y1" class="flex-1" size="sm" :items="{{ json_encode(range($years['firstJDEnrollment'], $d_year2)) }}" :model-value="{{ $year1 }}"></l-select>
            <label class="sr-only" for="y2">End year</label>
            <l-select id="y2" name="y2" class="flex-1" size="sm" :items="{{ json_encode(range($years['firstJDEnrollment'], $d_year2)) }}" :model-value="{{ $year2 }}"></l-select>
            <l-button variant="primary" class="btn-sm" type="submit">Go</l-button>
        </fieldset>
    </form>
@endsection

@section('settings-value', "{$year1} - {$year2}")

@section('report')
<?php
    $query = "
    SELECT *
    FROM school_admissions AS A
    LEFT JOIN schools AS S ON A.school_id = S.id
    WHERE A.admission_year = {$year1} OR A.admission_year = {$year2}
";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->fetchAll();

    foreach ($result as $row) {
        $allRows[$row['school_id']][$row['admission_year']]['1Ls'] = $row['enrollees_All'];
        $allRows[$row['school_id']]['name'] = $row['name'];
        $allRows[$row['school_id']]['school_id'] = $row['school_id'];
        $allRows[$row['school_id']]['indicators'] = $row['indicators'];
        $allRows[$row['school_id']]['slug'] = $row['slug'];
    }

    if ($year1 < 2017) {
        $query = "
        SELECT school_id, academic_year, enrollment
        FROM school_enrollments_2011_2016
        WHERE academic_year = {$year1} OR academic_year = {$year2}
    ";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetchAll();

        foreach ($result as $row) {
            $allRows[$row['school_id']][$row['academic_year']]['all'] = $row['enrollment'];
        }
    }

    if ($year2 > 2016) {
        $query = "
        SELECT school_id,academic_year,total
        FROM school_enrollments
        WHERE academic_year = {$year1} OR academic_year = {$year2}
    ";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetchAll();

        foreach ($result as $row) {
            $allRows[$row['school_id']][$row['academic_year']]['all'] = $row['total'];
        }
    }
?>

    @include('trends.enrollment.tip')

    <enrollment-schools>
        <div class="overflow-x-auto overflow-y-hidden">
            <table id="compareSchoolsAdmissions" class="l-table">
                <thead>
                <tr>
                    <th rowspan="2">School</th>
                    <th colspan="2" class="no_sort"><?= $year2; ?>-<?= $year2 + 1; ?> Academic year</th>
                    <th colspan="2" class="no_sort"><?= $year1; ?>-<?= $year1 + 1; ?> Academic year</th>
                    <th colspan="2" class="no_sort">Change</th>
                </tr>
                <tr>

                    <th>1L Students</th>
                    <th>JD Students</th>
                    <th>1L Students</th>
                    <th>JD Students</th>
                    <th>1Ls</th>
                    <th>JDs</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($allRows ?? [] as $r)
                    @if (isset($r[$year1]) && isset($r[$year2]))
                    @php
                        if (isset($r[$year1]) && isset($r[$year2])) {
                            if (isset($r[$year1]['1Ls'])) {
                                $enr1 = $r[$year1]['1Ls'];
                                $enr2 = $r[$year2]['1Ls'];

                                if ($enr1 == $enr2) {
                                    $enr_change = '0';
                                    $marker = '%';
                                } elseif ($enr1 == 0) {
                                    $enr_change = "No {$year1} Data";
                                    $marker = '';
                                } elseif ($enr2 == 0) {
                                    $enr_change = "No {$year2} Data";
                                    $marker = '';
                                } else {
                                    $enr_change = -round((($enr1 - $enr2) / $enr1) * 100, 1);
                                    $marker = '%</span>';
                                    if ($enr_change > 0) {
                                        $enr_change = "<span class='text-green'>+{$enr_change}";
                                    } else {
                                        $enr_change = "<span class='text-red'>{$enr_change}";
                                    }
                                }
                            }

                            if (isset($r[$year1]['all']) && isset($r[$year2]['all'])) {
                                $enr1JD = $r[$year1]['all'];
                                $enr2JD = $r[$year2]['all'];

                                if ($enr1JD == $enr2JD) {
                                    $enr_changeJD = '0';
                                    $markerJD = '%';
                                } elseif ($enr1JD == 0) {
                                    $enr_changeJD = "No {$year1} Data";
                                    $markerJD = '';
                                } elseif ($enr2JD == 0) {
                                    $enr_changeJD = "No {$year2} Data";
                                    $markerJD = '';
                                } else {
                                    $enr_changeJD = -round((($enr1JD - $enr2JD) / $enr1JD) * 100, 1);
                                    $markerJD = '%</span>';
                                    if ($enr_changeJD > 0) {
                                        $enr_changeJD = "<span class='text-green'>+{$enr_changeJD}";
                                    } else {
                                        $enr_changeJD = "<span class='text-red'>{$enr_changeJD}";
                                    }
                                }
                            }
                        }
                    @endphp
                        <tr>
                            <td class="text-left"><a class="text-orange-600" href="/schools/{{ $r['slug'] }}/admissions">{{ $r['name'] }}</a><span hidden>{{ $r['indicators'] }}</span></td>
                            <td>{{ $r[$year2]['1Ls']  ?? '-' }}</td>
                            <td>{{ $r[$year2]['all']  ?? '-' }}</td>
                            <td>{{ $r[$year1]['1Ls']  ?? '-' }}</td>
                            <td>{{ $r[$year1]['all']  ?? '-' }}</td>
                            <td>{!! $enr_change . $marker !!}<span hidden title="{{ str_replace("<span class='text-green'>+", '', str_replace("<span class='text-red'>", '', $enr_change)) }}"></span></td>
                            <td>{!! $enr_changeJD . $markerJD !!}<span hidden title="{{ str_replace("<span class='text-green'>+", '', str_replace("<span class='text-red'>", '', $enr_changeJD)) }}"></span></td>
                        </tr>
                    @endif
                @endforeach
                </tbody>
            </table>
        </div>

        <x-note>
            <p>Enrollment data come from the American Bar Association.</p>
        </x-note>
    </enrollment-schools>
@endsection
