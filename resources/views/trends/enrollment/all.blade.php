@extends('layouts.trends')

@php
    $d_year1 = 1963;
    $d_year2 = $years['latestEnrollment'];

    // Settings

    if (Request::input('y1') >= $d_year1 && Request::input('y1') < $d_year2) {
        $year1 = Request::input('y1');
    } else {
        $year1 = $d_year1;
    }
    if (Request::input('y2') > $d_year1 && Request::input('y2') <= $d_year2) {
        $year2 = Request::input('y2');
    } else {
        $year2 = $d_year2;
    }

    $query = "SELECT enr_JDs FROM academic_years WHERE year = 2010";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->fetchAll();

    if ($result) {
        $peak = $result[0]['enr_JDs'];
    }
@endphp

@section('title', "Law School Enrollment Trends, ${year1}-${year2}")

@section('description', "Compare 1L enrollment, JD enrollment, non-JD enrollment, 1L attrition, ABA Standard 501 compliance, admissions standards, demand for law school as measured by LSAT takers, applicants, and the national acceptance rate.")

@section('settings')
    <form class="grid gap-2" method="get">
        <fieldset class="flex flex-row gap-2">
            <legend class="font-medium cursor-default mb-1" onclick="this.parentElement.elements[0].focus()">Years</legend>
            <label class="sr-only" for="y1">Start year</label>
            <l-select id="y1" name="y1" class="flex-1" size="sm" :items="{{ json_encode(range($d_year1, $d_year2)) }}" :model-value="{{ $year1 }}"></l-select>
            <label class="sr-only" for="y2">End year</label>
            <l-select id="y2" name="y2" class="flex-1" size="sm" :items="{{ json_encode(range($d_year1, $d_year2)) }}" :model-value="{{ $year2 }}"></l-select>
            <button type="submit" class="btn btn-primary btn-sm">Go</button>
        </fieldset>
    </form>
@endsection

@section('settings-value', "{$year1} - {$year2}")

@section('report')
    @php
        $xCats = [];
        $EnrlData1Ls = [];
        $EnrlDataJDs = [];
        $EnrlDataNonJDs = [];

        $query = "SELECT year,enr_1Ls,enr_JDs,enr_nonJDs FROM academic_years WHERE year >= {$year1} && year <= {$year2} ORDER BY year ASC";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($result as $row) {
            $year = $row['year'];
            $xCats []= $year;
            if ($year < 2010) {
                $EnrlData1Ls []= ['y' => $row['enr_1Ls'], 'x' => $row['year']];
            }
            if ($year < 2011) {
                $EnrlDataJDs []= ['y' => $row['enr_JDs'], 'x' => $row['year']];

            }

            if($row['enr_nonJDs']){
                $EnrlDataNonJDs []= ['y' => $row['enr_nonJDs'], 'x' => $row['year']];
            }
        }

        if ($year2 >= 2010) {
            $query = "
                SELECT admission_year, SUM(enrollees_All) AS enr
                FROM school_admissions
                WHERE admission_year >= {$year1} AND admission_year <= {$year2}
                GROUP BY admission_year
                ORDER BY admission_year ASC";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $result = $stmt->fetchAll();

            foreach ($result as $row) {
                $EnrlData1Ls []= (object) ['y' => (int) $row['enr'], 'x' => $row['admission_year']];
            }

            if ($year1 < 2017) {
                $query = "
                    SELECT academic_year, SUM(enrollment) AS enr
                    FROM school_enrollments_2011_2016
                    WHERE academic_year >= {$year1} AND academic_year <= {$year2}
                    GROUP BY academic_year
                    ORDER BY academic_year ASC
                ";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $result = $stmt->fetchAll();

                foreach ($result as $row) {
                    $EnrlDataJDs []= ['y' => (int) $row['enr'], 'x' => $row['academic_year']];
                }
            }

            if ($year2 > 2016) {
                $query = "
                    SELECT academic_year, SUM(total) AS tot
                    FROM school_enrollments
                    WHERE academic_year >= {$year1} AND academic_year <= {$year2}
                    GROUP BY academic_year
                    ORDER BY academic_year ASC
                ";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $result = $stmt->fetchAll();

                foreach ($result as $row) {
                    $EnrlDataJDs []= ['y' => (int) $row['tot'], 'x' => $row['academic_year']];
                    $lastYear = $row['tot'];
                }
            }
        }
    @endphp

    <enrollment-national
        class="prose dark:prose-invert"
        :data-1ls='<?= json_encode($EnrlData1Ls) ?>'
        :data-jds='<?= json_encode($EnrlDataJDs); ?>'
        :data-non-jds='<?= json_encode($EnrlDataNonJDs); ?>'
        :categories='<?= json_encode($xCats); ?>'
        :year1="<?= $year1 ?>"
        :year2="<?= $year2 ?>"
    >
        <p>Between 1976 and 2000, law schools steadily enrolled between ~40,000 and ~44,000 new students each year. From 1976 to 1987, the average was 40,973. From 1988 to 2000, the average was 43,497&mdash;a little over 6% higher.  But between 2000 and 2002, law schools increased first-year enrollment 11.2%.  In subsequent years, enrollment steadily creeped up, with minor ebbs and flows, until peaking in 2010 at 52,404. As law schools were pressured to become more transparent about job outcomes beginning in 2010, the media and prospective law students took notice of inflated enrollment, inadequate job prospects, and high prices&mdash;and enrollment dropped.  After 1L enrollment peaked in 2010 at 52,404 new students, enrollment fell dramatically over the next four years, where enrollment has stabilized around 38,000 new 1Ls each year.</p>

        <p>The blue plotbands reflect U.S. recessions as defined by the National Bureau of Economic Research.</p>

        <div id="EnrlGraph"></div>

        @include('trends.enrollment.tip')

        <p>Compared to enrollment in the early 2010s, significantly lower, stable enrollment benefits law students when they try to enter the legal job market, which remains relatively stable in terms of the number of
            <a href="{{ route('trends.jobs') }}">
                new legal jobs
            </a>
            available each year. With fewer seats available, law school applicants face greater competition in earning admission in years with more applicants. In years with fewer applicants, prospective law students may find it easier to get into more prestigious schools and receive more generous scholarship offers.</p>

        @if (isset($lastYear, $peak))
            <p>
                With more stable 1L enrollment comes more stable overall enrollment too. When first-year enrollment rises or falls in sequential years,
                those effects multiply. Today, overall JD enrollment has roughly stabilized at a level not seen in over 40 years. Compared to the peak in
                JD enrollment in 2010 (147,525 students), overall JD enrollment was down

                <strong class="text-red">
                    {{ Math::percent($peak - $lastYear, $peak, "%") }}
                </strong>

                in {{ $d_year2 }}.
            </p>
        @endif

        <div id="EnrlGraph2"></div>

        <p>With lower overall JD enrollment than a decade ago, many schools felt (and some still feel) financial pressure. This pressure has been relieved at some law schools by growth of non-JD programs, whether post-JD programs, masters-level programs aimed at non-lawyers, or certificate programs. Between 1999 and 2006, non-JD enrollment was steady, averaging ~7500 students. In the years since 2006, non-JD enrollment has increased substantially and consistently, with the pace accelerating in the aftermath of drastically falling JD enrollment after law schools had some time to create tuition-bearing programs. Tuition paid by these students offset costs associated with maintaining JD programs.</p>

        <x-note>
            <p>Enrollment data come from the American Bar Association. All Non-JD totals were computed by the ABA. Total JD enrollment totals before 2011 were computed by the ABA, but the totals in 2011 or later later were totaled by LST, which aggregates individual school JD enrollment as reported by the ABA. First-year enrollment totals before 2010 were computed by the ABA, but the totals in 2010 or later later were totaled by LST, which aggregates individual school first-year enrollment as reported by the ABA.</p>
        </x-note>
    </enrollment-national>
@endsection
