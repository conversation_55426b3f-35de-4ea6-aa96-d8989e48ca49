@extends('layouts.iatl')

@section('title', $podcast->headline)

@section('description', $podcast->excerpt)

@section('image', $podcast->thumbnail)

@section("twitter:card", "player")
@section('twitter:player', "{$podcast->player_url}?source=twitter")
@section('twitter:player:stream', "{$podcast->media_url}?played_on=twitter")
@section("twitter:player:stream:content_type", $podcast->media_type)
@section("twitter:player:width", $podcast->player_width)
@section("twitter:player:height", $podcast->player_height)

@section('type', 'PodcastEpisode')

@section('banner')
    <div class="grid lg:grid-cols-2 gap-8 items-center">
        <div class="flex flex-col justify-center gap-6 lg:gap-8">
            <h1 class="text-blue dark:text-white font-serif font-bold text-4xl lg:text-5xl">
                {{ $podcast->title }}
            </h1>

            <div class="flex gap-3 lg:text-lg text-gray-500">
                @if ($podcast->hosts->isNotEmpty())
                    <div class="flex gap-2 items-center">
                        @foreach ($hosts ?? $podcast->hosts as $host)
                            <a
                                href="{{ route('podcasts.iatl.hosts.show', $host) }}"
                                class="flex gap-2 items-center font-medium whitespace-nowrap"
                            >
                                <img
                                    src="{{ $host->avatar }}"
                                    class="w-6 h-6 aspect-square object-cover rounded-full bg-gray-200"
                                    alt="{{ $host->headline }}"
                                    title="{{ $host->headline }}"
                                >

                                {{ $host->name }}
                            </a>
                        @endforeach
                    </div>
                @endif

                <div>·</div>

                <div>{{ $podcast->date }}</div>
            </div>

            <div>
                <player-link autoplay @bind(['episode' => $podcast]) v-slot="{ activator, href }">
                    <l-button
                        class="btn btn-primary btn-lg md:btn-xl inline-flex items-center gap-2"
                        variant="link"
                        :href="href"
                        @click.prevent="activator"
                    >
                        Listen to this episode
                        <i class="fa-solid fa-arrow-right"></i>
                    </l-button>
                </player-link>
            </div>
        </div>

        @include('podcasts.iatl.partials.player')
    </div>
@endsection

@section('iatl-content')
    <div class="space-y-8 lg:space-y-12">
        <div class="mb-8 space-y-4">
            @if ($podcast->description)
                <div class="prose-lg ">
                    {!! $podcast->description !!}
                </div>
            @endif
        </div>

        @if ($podcast->transcript)
            <div class="grid gap-6">
                <h2 class="font-semibold text-blue dark:text-white text-xl md:text-2xl leading-relaxed">
                    Transcript
                </h2>
                <collapse v-slot="{ show }">
                    <div :class="['prose dark:prose-invert', {'h-96 overflow-hidden relative': !show}]">
                        {!! $podcast->transcript !!}

                        <div v-if="!show" class="absolute bottom-0 w-full h-24 bg-gradient-to-b from-transparent to-white dark:to-blue"></div>
                    </div>
                </collapse>
            </div>
        @endif

        <div class="flex {{ $podcast->previous ? 'justify-between ' : 'justify-end' }}">
            @if ($podcast->previous)
                <l-button
                    href="{{ route('podcasts.iatl.show', [$podcast->previous, $podcast->previous->slug])  }}"
                    class="group pl-2.5"
                    variant="light"
                >
                    <icon name="previous" class="text-orange-600 group-hover:text-orange-700"></icon>
                    <span class="">
                        Episode #{{ $podcast->previous->id }}
                    </span>
                </l-button>
            @endif

            @if ($podcast->next)
                <l-button
                    href="{{ route('podcasts.iatl.show', [$podcast->next, $podcast->next->slug])  }}"
                    class="group pr-2.5"
                    variant="light"
                >
                    <span class="">
                        Episode #{{ $podcast->next->id }}
                    </span>
                    <icon name="next" class="text-orange-600 group-hover:text-orange-700"></icon>
                </l-button>
            @endif
        </div>

        @if ($related_episodes->isNotEmpty())
            <div class="grid gap-6">
                <h2 class="font-semibold text-blue dark:text-white text-xl md:text-2xl leading-relaxed">
                    Related episodes
                </h2>

                <iatl-episode-list @bind(['episodes' => $related_episodes])></iatl-episode-list>
            </div>
        @endif
    </div>
@endsection

@section('iatl-sidebar')
    @if ($podcast->pathways->isNotEmpty() || $podcast->tags->isNotEmpty())
        <div class="grid gap-4">
            <h2 class="text-lg font-semibold text-blue dark:text-white">
                Tags
            </h2>

            <div class="flex flex-wrap gap-2">
                @if ($podcast->pathways->isNotEmpty())
                    @foreach ($podcast->pathways as $pathway)
                        <a href="{{ route('podcasts.iatl.pathways.show', $pathway) }}" class="tag tag-blue tag-small">
                            {{ $pathway->label }}
                        </a>
                    @endforeach
                @endif

                @if ($podcast->tags->isNotEmpty())
                    @foreach ($podcast->tags as $tag)
                        <a href="{{ route('podcasts.iatl.tags.show', $tag) }}" class="tag tag-yellow tag-small">
                            {{ $tag->label }}
                        </a>
                    @endforeach
                @endif
            </div>
        </div>
    @endif
@endsection
