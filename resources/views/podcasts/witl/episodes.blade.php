<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
    @component('components.card', [
        'imageUrl' => asset('images/witl/sexism.jpg'),
        'url' => route('podcasts.witl.show', 'sexism'),
    ])
        @slot('title')
            <h2 class="text-xl font-semibold">
                Hey <PERSON>ie!
            </h2>
        @endslot

        @slot('body')
            <p class="leading-relaxed mb-0">Sexism in the legal workplace</p>
        @endslot
    @endcomponent

    @component('components.card', [
        'imageUrl' => asset('images/witl/media.jpg'),
        'url' => route('podcasts.witl.show', 'media'),
    ])
        @slot('title')
            <h2 class="text-xl font-semibold">
                In the Media
            </h2>
        @endslot

        @slot('body')
            <p class="leading-relaxed mb-0">How women lawyers are portrayed on TV and in the news</p>
        @endslot
    @endcomponent

    @component('components.card', [
        'imageUrl' => asset('images/witl/lp1.png'),
        'url' => route('podcasts.witl.show', 'lp1'),
    ])
        @slot('title')
            <h2 class="text-xl font-semibold">
                Leaky Pipeline #1
            </h2>
        @endslot

        @slot('body')
            <p class="leading-relaxed mb-0">Examining the premise of a leaky pipeline</p>
        @endslot
    @endcomponent

    @component('components.card', [
        'imageUrl' => asset('images/witl/lp2.jpg'),
        'url' => route('podcasts.witl.show', 'lp2'),
    ])
        @slot('title')
            <h2 class="text-xl font-semibold">
                Leaky Pipeline #2
            </h2>
        @endslot

        @slot('body')
            <p class="leading-relaxed mb-0">From hiring to retention to leadership</p>
        @endslot
    @endcomponent

    @component('components.card', [
        'imageUrl' => asset('images/witl/intersectionality.png'),
        'url' => route('podcasts.witl.show', 'intersectionality'),
    ])
        @slot('title')
            <h2 class="text-xl font-semibold">
                Multiple Identities
            </h2>
        @endslot

        @slot('body')
            <p class="leading-relaxed mb-0">Intersectional challenges in the legal profession</p>
        @endslot
    @endcomponent

    @component('components.card', [
        'imageUrl' => asset('images/witl/solutions.jpg'),
        'url' => route('podcasts.witl.show', 'solutions'),
    ])
        @slot('title')
            <h2 class="text-xl font-semibold">
                Solutions
            </h2>
        @endslot

        @slot('body')
            <p class="leading-relaxed mb-0">Rules, sanctions, and awareness</p>
        @endslot
    @endcomponent
</div>
