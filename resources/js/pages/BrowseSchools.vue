<template>
  <div class="mt-8 max-w-lg mx-auto">
    <l-school-input
      v-model="school"
      label="Select school or state"
      placeholder="Enter school or state..."
      class="flex-1 w-full label:sr-only"
      size="lg"
      autofocus
      v-bind="{ loading }"
      @input="redirect"
    />
  </div>
</template>

<script setup>
import LSchoolInput from "@/components/inputs/LSchoolInput.vue"
import { ref } from "vue"

const loading = ref(false)

const school = ref(null)

const redirect = () => {
  loading.value = true

  window.location.href = school.value.url
}
</script>
