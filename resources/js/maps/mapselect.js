/*
Copyright 2017, <PERSON>, SimpleMaps, http://simplemaps.com
Released under MIT license - https://opensource.org/licenses/MIT
*/
window.simplemaps_select = function () {
  return {
    map: false,
    on_shift: false,
    selected_color: false,
  }
}()

let me = window.simplemaps_select
const map = me.map ? me.map : window.simplemaps_usmap // usmap is default
const on_shift = me.on_shift
const selected_color = me.selected_color ? me.selected_color : map.mapdata.main_settings.state_hover_color
const selected = []
const max = me.max ? me.max : false
const original_mapdata = JSON.parse(JSON.stringify(map.mapdata))

function check_mapdata(state) { // make sure a color exists for each state
  if (!map.mapdata.state_specific[state]) {
    map.mapdata.state_specific[state] = {}
  }

  if (!original_mapdata.state_specific[state]) {
    original_mapdata.state_specific[state] = {}; original_mapdata.state_specific[state].color = "default"
  } else if (!original_mapdata.state_specific[state].color) {
    original_mapdata.state_specific[state].color = "default"
  }
}

const deselect = function (state) {
  map.states[state].stop() // prevents fade time from interfering with deselect
  const index = selected.indexOf(state)

  if (index > -1) { // deselect state
    selected.splice(index, 1)
    check_mapdata(state)
  }
  map.mapdata.state_specific[state].color = original_mapdata.state_specific[state].color
  done(state)
}

const check_max = function () {
  if (me.max && selected.length >= me.max) {
    const first = selected[0]
    me.deselect(first)
  }
}

const select = function (state) {
  const index = selected.indexOf(state)

  if (index < 0) { // make sure a state is selectedable
    check_mapdata(state)
    check_max()
    selected.push(state)
  }
  map.mapdata.state_specific[state].color = me.selected_color
  done(state)
}

const select_all = function () {
  for (const state in window.simplemaps_usmap_mapinfo.paths) {
    select(state)
  }
}

const deselect_all = function () {
  const length = selected.length

  for (let i = 1; i < length + 1; i++) {
    const id = length - i
    const state = selected[id]
    deselect(state)
  }
}

function done(state) {
  map.refresh_state(state)
  me.selected = selected // update value
}

const upon_click = function (state, e) {
  if (me.on_shift) { // select on shift+click
    const length = me.selected.length
    const index = me.selected.indexOf(state)
    const last_state = me.selected[length - 1]

    if (length == 0) {
      me.select(state)
    } else if (length > 0) {
      if (e.shiftKey) {
        if (index > -1) {
          me.deselect(state)
        } else {
          me.select(state)
        }
      } else {
        me.deselect_all(last_state)
        me.select(state)
      }
    }
  } else { // select on click
    const index = selected.indexOf(state)

    if (index > -1) { // deselect state
      deselect(state)
    } else { // select state
      select(state)
    }
  }
}

map.plugin_hooks.click_state.push(upon_click)

window.simplemaps_select = {
  // inputs
  map,
  on_shift,
  selected_color,
  max,
  // outputs
  selected,
  // methods
  select,
  deselect,
  select_all,
  deselect_all,
}

me = window.simplemaps_select

// eslint-disable-next-line import/no-unused-modules
export default me
