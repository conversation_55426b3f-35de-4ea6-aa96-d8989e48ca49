import LawHub from "@/icons/LawHub.vue"
import PictureInPicture from "@/icons/PictureInPicture.vue"
import Spinner from "@/icons/Spinner.vue"
import {
  FaAmazon,
  FaBackwardStep,
  FaCircleQuestion,
  FaFileLines,
  FaForwardStep,
  FaGoogle,
  FaItunes,
  FaList,
  FaScroll,
  FaSpotify,
} from "vue3-icons/fa6"

export default {
  add: "fa-plus",
  affiliation: "fa-briefcase",
  amazon: FaAmazon,
  announcement: "fa-bullhorn",
  arrowLeft: "fa-arrow-left",
  arrowLeftLong: "fa-arrow-left-long",
  arrowRight: "fa-arrow-right",
  arrowRightLong: "fa-arrow-right-long",
  backward: FaBackwardStep,
  bars: "fa-bars",
  chevronDown: "fa-chevron-down",
  chevronLeft: "fa-chevron-left",
  circleCheck: "fa-circle-check",
  circleWarning: "fa-circle-exclamation",
  clipboardListCheck: "fa-clipboard-list-check",
  close: "fa-xmark-large",
  copy: "fa-copy",
  course: "fa-graduation-cap",
  date: "fa-calendar",
  decrease: "fa-circle-minus",
  delete: "fa-trash-can",
  duration: "fa-circle-play",
  edit: "fa-pen-to-square",
  ellipsis: "fa-ellipsis",
  ellipsisVertical: "fa-ellipsis-vertical",
  embed: "fa-code",
  episode: "fa-podcast",
  error: "fa-circle-xmark",
  external: "fa-external-link",
  facebook: "fa-facebook",
  filter: "fa-sliders",
  forward: FaForwardStep,
  google: FaGoogle,
  guide: "fa-memo-circle-info",
  help: "fa-question-circle",
  host: "fa-microphone",
  increase: "fa-circle-plus",
  info: "fa-circle-info",
  itunes: FaItunes,
  lawhub: LawHub,
  link: "fa-link",
  list: FaList,
  loading: Spinner,
  location: "fa-location-dot",
  map: "fa-map",
  next: "fa-angle-right",
  notes: FaFileLines,
  page: "fa-file-lines",
  pathway: "fa-signs-post",
  pip: PictureInPicture,
  play: "fa-circle-play",
  previous: "fa-angle-left",
  remove: "fa-xmark",
  school: "fa-landmark",
  search: "fa-magnifying-glass",
  settings: "fa-gear",
  share: "fa-arrow-up-from-bracket",
  sms: "fa-comments",
  spotify: FaSpotify,
  squareCheck: "fa-square-check",
  squareX: "fa-square-xmark",
  star: "fa-star",
  state: "fa-tree-city",
  tag: "fa-tag",
  telegram: "fa-telegram",
  term: "fa-book",
  tip: "fa-lightbulb-on",
  transcript: FaScroll,
  trend: "fa-chart-line",
  troubleshoot: FaCircleQuestion,
  twitter: "fa-twitter",
  warning: "fa-triangle-exclamation",
  whatsapp: "fa-whatsapp",
}
