<template>
  <div>
    <slot/>
  </div>
</template>

<script setup>
import { onMounted } from "vue"

onMounted(() => {
  $("#tuition_table").dataTable({
    bPaginate: false,
    bInfo: false,
    order: [[2, "desc"]],
    aoColumnDefs: [
      { sType: "html", aTargets: [0] },
      { targets: "data-table-no-sort", orderable: false },
      { targets: "data-table-number", type: "title-numeric" },
    ],
    oLanguage: {
      sSearch: "Filter schools:",
    },
  })
})
</script>
