<template>
  <div>
    <slot/>
  </div>
</template>

<script setup>
import Highcharts from "@/highcharts"
import { onMounted } from "vue"

const props = defineProps({
  categories: Array,
  minRisk: Array,
  lowRisk: Array,
  modRisk: Array,
  highRisk: Array,
  veryRisk: Array,
  extRisk: Array,
})

onMounted(() => {
  const {
    categories,
    minRisk,
    lowRisk,
    modRisk,
    highRisk,
    veryRisk,
    extRisk,
  } = props

  Highcharts.chart("percentage_area", {
    chart: {
      type: "area",
    },
    title: {
      text: "",
    },
    subtitle: {
      text: "",
    },
    xAxis: {
      categories,
      tickmarkPlacement: "on",
      title: {
        enabled: false,
      },
    },
    yAxis: {
      title: {
        enabled: false,
      },
      labels: {
        enabled: false,
      },
    },
    tooltip: {
      pointFormat: '<span style="color:{series.color}">{series.name} Risk</span>: <b>{point.percentage:.1f}%</b> ({point.y:,.0f} schools)<br/>',
      split: true,
    },
    plotOptions: {
      area: {
        stacking: "percent",
        lineColor: "#ffffff",
        lineWidth: 1,
        marker: {
          enabled: false,
          symbol: "circle",
        },
      },
    },
    series: [{
      name: "Minimal",
      data: minRisk,
      color: "rgba(124, 181, 236, 0.85)",
      fillColor: "rgba(124, 181, 236, 0.85)",
    }, {
      name: "Low",
      data: lowRisk,
      color: "rgba(124, 181, 236, 0.6)",
      fillColor: "rgba(124, 181, 236, 0.6)",
    }, {
      name: "Modest",
      data: modRisk,
      color: "rgba(124, 181, 236, 0.4)",
      fillColor: "rgba(124, 181, 236, 0.4)",
    }, {
      name: "High",
      data: highRisk,
      color: "rgba(255, 19, 0, 0.45)",
      fillColor: "rgba(255, 19, 0, 0.45)",
    }, {
      name: "Very High",
      data: veryRisk,
      color: "rgba(255, 19, 0, 0.6)",
      fillColor: "rgba(255, 19, 0, 0.6)",
    }, {
      name: "Extreme",
      data: extRisk,
      color: "rgba(255, 19, 0, 0.7)",
      fillColor: "rgba(255, 19, 0, 0.7)",
    }],
  })
})
</script>
