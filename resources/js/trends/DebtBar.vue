<template>
  <div>
    <slot/>
  </div>
</template>

<script setup>
import Highcharts from "@/highcharts"
import { onMounted } from "vue"
import { percent } from "@/support"

const props = defineProps({
  year1: Number,
  data: Object,
})

onMounted(() => {
  Highcharts.setOptions({ lang: { numericSymbols: ["k", " million", " billion"] } })

  const { year1, data } = props

  const series = Object.keys(data).map((key) => {
    return {
      name: data[key].title,
      data: data[key].schools.map((school) => ({
        x: percent(school.passing, school.taking),
        y: school.debt,
        name: school.name,
      })),
      color: data[key].color,
      marker: {
        symbol: "circle",
        fillColor: data[key].color,
      },
    }
  })

  const options = {
    chart: {
      type: "scatter",
      zoomType: false,
      height: 500,
    },
    title: { text: `Bar Pass Rate vs. Average Amount Borrowed, ${year1} Graduates` },
    xAxis: {
      title: { enabled: true, text: "Bar Pass Rate" },
      labels: {
        formatter() {
          return `${this.value}%`
        },
      },
    },
    yAxis: { title: { text: `${year1} Total Federal Investment` } },
    plotOptions: {
      scatter: {
        marker: {
          radius: 5,
          states: {
            hover: {
              enabled: true,
              lineColor: "rgb(100,100,100)",
            },
          },
        },
        states: {
          hover: {
            marker: {
              enabled: false,
            },
          },
        },
      },
    },
    tooltip: {
      useHTML: true,
      formatter() {
        if (this.y == 0) {
          return `<b>${this.point.name}, ${year1} Graduates</b><br>Bar Pass Rate: ${this.x}%<br>Average Borrowed: Not Reported`
        }

        return `<b>${this.point.name}, ${year1} Graduates</b><br>Bar Pass Rate: ${this.x}%<br>Average Borrowed: $${Highcharts.numberFormat(this.y, 0, ".", ",")}`
      },
    },
    series,
  }

  new Highcharts.chart("barpassage_plot", options)
})
</script>
