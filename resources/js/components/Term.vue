<template>
  <a
    v-if="href"
    :href="href"
    :aria-label="text"
    class="inline-block bg-transparent p-1 -m-1 cursor-pointer z-0"
    :data-balloon-pos="pos"
    data-balloon-blunt
  >
    <icon set="regular" name="help"/>
  </a>
  <span
    v-else
    class="inline-block bg-transparent p-1 -m-1 cursor-pointer z-0"
    :aria-label="text"
    :data-balloon-pos="pos"
    data-balloon-blunt
  >
    <icon set="regular" name="help" class="text-gray-400"/>
  </span>
</template>

<script setup>
import Icon from "@/components/Icon.vue"
import terms from "@/terms"
import { computed } from "vue"

const props = defineProps({
  text: { type: String },
  href: { type: String },
  pos: { type: String, default: "up" },
  name: { type: String },
})

const text = computed(() => (props.name ? terms[props.name] : props.text))
</script>
