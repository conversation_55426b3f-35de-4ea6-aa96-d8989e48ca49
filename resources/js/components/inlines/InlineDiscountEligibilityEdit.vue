<template>
  <inline-edit
    :action="route('settings.discount')"
    title="Edit discount eligibility"
    v-bind="{ user }"
  >
    <l-discount-input v-model="user"/>
  </inline-edit>
</template>

<script setup>
import InlineEdit from "@/components/inlines/InlineEdit.vue"
import LDiscountInput from "@/components/inputs/LDiscountInput.vue"
import route from "ziggy-js"

const user = defineModel({ type: Object })
</script>
