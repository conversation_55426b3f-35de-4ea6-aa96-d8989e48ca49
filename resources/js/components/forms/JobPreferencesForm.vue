<template>
  <report-settings-form :action="route('settings.job-priorities')">
    <l-job-priorities-input v-model="user.report"/>
  </report-settings-form>
</template>

<script setup>
import LJobPrioritiesInput from "@/components/inputs/LJobPrioritiesInput.vue"
import ReportSettingsForm from "@/components/forms/ReportSettingsForm.vue"
import route from "ziggy-js"

const user = defineModel({ type: Object, required: true })
</script>
