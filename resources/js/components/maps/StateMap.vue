<template>
  <div class="relative">
    <template v-if="!multiple">
      <a
        v-for="({ name, code, url }) in items"
        :key="code"
        :href="url"
        class="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0"
        v-text="name"
      />
    </template>

    <fieldset v-else class="sr-only focus-within:not-sr-only">
      <legend class="sr-only">
        States
      </legend>

      <label
        v-for="item in items"
        :key="item.key"
        class="sr-only focus-within:not-sr-only focus-within:absolute focus-within:top-0 focus-within:left-0"
      >
        <input
          v-model="model"
          type="checkbox"
          :value="item"
        >
        {{ item.name }}
      </label>
    </fieldset>

    <div id="map" aria-hidden="true"/>
  </div>
</template>

<script setup>
import mapdata from "@/maps/mapdata"
import mapinfo from "@/maps/mapinfo"
import usmap from "@/maps/usmap"
import { computed, watch } from "vue"
import { find, indexOf, keyBy } from "lodash-es"
import { useStates } from "@/composables/useStates"

const model = defineModel({ type: Array, default: () => [] })

const props = defineProps({
  multiple: { type: Boolean },
})

let mapselect

const { states } = useStates()

const items = computed(() => {
  if (props.multiple) {
    return states.map(({ code, name }) => ({ code, name, hide: "no" }))
  }

  return states.map(state => ({ ...state, hide: "no" }))
})

watch(() => props.modelValue, value => {
  model.value = value

  mapselect.deselect_all()

  model.value.forEach(state => mapselect.select(state.code))
})

watch([items], () => {
  model.value = props.modelValue

  usmap.mapdata = {
    ...mapdata,
    main_settings: {
      ...mapdata.main_settings,
      state_description: props.multiple ? "Select state" : "State report",
    },
    state_specific: keyBy(items.value, "code"),
  }

  usmap.mapinfo = mapinfo

  usmap.load()

  mapselect = require("@/maps/mapselect").default

  usmap.plugin_hooks.click_state.push(code => {
    const state = find(items.value, { code })

    if (!find(model.value, { code })) {
      model.value.push(state)
    } else {
      model.value.splice(indexOf(model.value, { code }))
    }
  })

  usmap.plugin_hooks.complete.push(() => {
    mapselect.deselect_all()

    model.value.forEach(state => mapselect.select(state.code))
  })
})
</script>
