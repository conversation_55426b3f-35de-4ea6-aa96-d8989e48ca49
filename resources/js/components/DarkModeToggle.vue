<template>
  <l-button variant="link" @click="setDarkMode(!active)">
    {{ !active ? 'Dark mode' : 'Light mode' }}
  </l-button>
</template>

<script setup>
import LButton from "@/components/buttons/LButton.vue"
import { ref } from "vue"

const prefersDarkMode = window.matchMedia("(prefers-color-scheme: dark)")

prefersDarkMode.addEventListener("change", setDarkMode)

const active = ref(localStorage.theme === "dark" || !("theme" in localStorage) && prefersDarkMode.matches)

const setDarkMode = (value) => {
  active.value = value

  localStorage.theme = value ? "dark" : "light"

  document.documentElement.classList.toggle("dark", value)
}
</script>
