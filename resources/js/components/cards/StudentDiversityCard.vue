<template>
  <card @mouseleave="reset">
    <template #header>
      <h2 class="font-medium text-blue dark:text-white">
        Diversity in students
      </h2>
    </template>
    <div class="grid sm:grid-cols-2 rounded-b-lg overflow-hidden">
      <div class="p-3 flex flex-col gap-3">
        <div class="leading-relaxed">
          Out of {{ enrollment.total }} students in {{ enrollment.academic_year }},
          {{ womenPercent }}% were women, and {{ pocPercent }}% were racially or ethnically minoritized.
        </div>

        <div v-if="active" class="highcharts-description">
          <div class=" mb-3">
            <div
              v-for="item in activeGender"
              :key="item.name"
              class="text-sm"
            >
              <div
                v-if="item.percent"
                class="flex items-center justify-between gap-2 p-1"
              >
                <div class="flex gap-2 items-center">
                  <div class="size-2 rounded-full bg-gray-300"/>
                  {{ item.name }}
                </div>
                <div class="font-semibold text-blue dark:text-white">
                  {{ item.percent }}%
                </div>
              </div>
            </div>
          </div>
          <div
            v-for="item in active"
            :key="item.name"
            class="text-sm"
          >
            <div
              v-if="item.y"
              class="flex items-center justify-between gap-2 p-1"
            >
              <div class="flex gap-2 items-center">
                <div class="size-2 rounded-full" :style="{ backgroundColor: item.color }"/>
                {{ item.name }}
              </div>
              <div class="font-semibold text-blue dark:text-white">
                {{ item.y }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pt-4 pr-4 pl-1.5 bg-gray-50 dark:bg-gray-800/50 border-t sm:border-t-0 sm:border-l border-gray-300 dark:border-gray-800">
        <div :id="id" class="h-full"/>
      </div>
    </div>
  </card>
</template>

<script setup>
import Card from "@/components/cards/Card.vue"
import Highcharts from "@/highcharts"
import colors from "@/colors"
import { computed, onMounted, ref } from "vue"
import { percent } from "@/support"

const props = defineProps({
  school: { type: Object, required: true },
  id: { type: String, required: true },
})

const seriesDefaults = {
  type: "column",
  stacking: "normal",
  animation: false,
}

const races = [
  { color: colors.gray[300], field: "unknownrace", name: "Unknown race" },
  { color: colors.indigo[400], field: "tworaces", name: "2+ races", label: "Mixed race" },
  { color: colors.teal[400], field: "intl", name: "International" },
  { color: colors.purple[400], field: "native", name: "Native American" },
  { color: colors.green[400], field: "islander", name: "Pacific Islander" },
  { color: colors.rose[400], field: "asian", name: "Asian" },
  { color: colors.yellow[400], field: "black", name: "Black" },
  { color: colors.blue[400], field: "hispanic", name: "Hispanic" },
  { color: colors.orange[400], field: "white", name: "White" },
]

const genders = [
  { color: colors.blue[400], field: "men", name: "Men" },
  { color: colors.pink[400], field: "women", name: "Women" },
  { color: colors.green[400], field: "AGI", name: "Another gender" },
  { color: colors.gray[300], field: "PNR", name: "Unknown gender" },
]

const series = computed(() => {
  const raceSeries = []

  races.forEach((race) => {
    raceSeries.push({
      ...seriesDefaults,
      name: race.name,
      color: race.color,
      stack: "race",
      data: props.school.enrollments.map(enrollment => {
        return {
          y: percent((enrollment[`first_total_${race.field}`] +
            enrollment[`second_total_${race.field}`] +
            enrollment[`third_total_${race.field}`] +
            enrollment[`fourth_total_${race.field}`]), enrollment.total),
          x: enrollment.academic_year,
          ...race,
        }
      }),
    })
  })

  return raceSeries
})

const getSeries = (year) => {
  return series.value.map((raceSeries) => {
    return raceSeries.data.filter((item) => item.x == year)[0]
  }).reverse()
}

const latestEnrollment = computed(() => props.school.enrollments[props.school.enrollments.length - 1])
const enrollment = ref(latestEnrollment.value)

const active = ref(getSeries(enrollment.value.academic_year))
const activeGender = computed(() => {
  return genders.map((gender) => {
    return {
      ...gender,
      percent: percent((enrollment.value[`first_${gender.field}_total`]
        + enrollment.value[`second_${gender.field}_total`]
        + enrollment.value[`third_${gender.field}_total`]
        + enrollment.value[`fourth_${gender.field}_total`]), enrollment.value.total),
    }
  })
})
const womenPercent = computed(() => {
  return percent((enrollment.value.first_women_total
    + enrollment.value.second_women_total
    + enrollment.value.third_women_total
    + enrollment.value.fourth_women_total), enrollment.value.total)
})
const pocPercent = computed(() => {
  return percent((enrollment.value.first_total_minority
    + enrollment.value.second_total_minority
    + enrollment.value.third_total_minority
    + enrollment.value.fourth_total_minority), enrollment.value.total)
})

const hover = (year) => {
  active.value = getSeries(year)
  enrollment.value = props.school.enrollments.find((e) => e.academic_year == year)
}

const reset = () => {
  enrollment.value = latestEnrollment.value
  active.value = getSeries(enrollment.value.academic_year)
}

onMounted(() => {
  const options = {
    chart: {
      type: "column",
      zoomType: "xy",
      events: {
        mouseOut() {
        },
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    title: {
      text: "",
    },
    yAxis: {
      min: 0,
      title: {
        text: "% of students by race",
      },
      max: 100,
    },
    xAxis: {
      tickInterval: 1,
    },
    tooltip: {
      formatter() {
        hover(this.x)

        return `${this.point.label || this.series.name} students comprise ${this.y}% of students`
      },
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        accessibility: {
          point: {
            descriptionFormatter(point) {
              return `${point.series.name}: ${point.y} out of ${point.stackTotal} students in ${point.x}`
            },
          },
          enabled: true,
        },
      },
      stacking: "normal",

    },
    series: series.value,
  }

  new Highcharts.Chart(props.id, options)
})
</script>
