<template>
  <card>
    <template #header>
      <h2 class="font-medium text-blue dark:text-white">
        Jobs by employer type
      </h2>
    </template>
    <div class="relative pt-3 px-3 pb-2 bg-gray-50 dark:bg-gray-800/50 aspect-[4/3] border-b border-gray-300 dark:border-gray-800">
      <div :id="id" class="w-auto h-full px-12 py-4"/>
      <div class="text-xs text-gray-500 flex gap-2 leading-none absolute top-4 left-4">
        <div v-if="active" class="flex gap-2">
          <l-button
            v-if="active"
            class="text-gray-700 hover:text-orange"
            @click="() => select(null)"
          >
            Overview
          </l-button>
          <span>/</span>
          {{ active.name }}
        </div>
        <span v-else>Overview</span>
      </div>
    </div>
    <div class="flex flex-col justify-center px-1 py-2">
      <template v-for="(job, index) in (active ? active.items : school.jobs_by_employer_type)">
        <l-button
          v-if="job.items?.length && job.percent"
          :key="job.name"
          class="py-1 px-3 rounded-md flex justify-between gap-3 text-sm hover:bg-gray-400/10"
          @click="() => select(job)"
        >
          <div class="flex items-center gap-2">
            <div>
              <div class="size-2 rounded-full" :style="{ backgroundColor: chartColors[index] }"/>
            </div>
            {{ job.name }}
          </div>
          <div class="font-semibold text-blue dark:text-white">
            {{ job.percent }}%
          </div>
        </l-button>
        <div v-else :key="job.name" class="py-1 px-3 flex justify-between gap-3 text-sm">
          <div class="flex items-center gap-2">
            <div>
              <div class="size-2 rounded-full" :style="{ backgroundColor: chartColors[index] }"/>
            </div>
            {{ job.name }}
          </div>
          <div class="font-semibold text-blue dark:text-white">
            {{ job.percent }}%
          </div>
        </div>
      </template>
    </div>
  </card>
</template>

<script setup>
import Card from "@/components/cards/Card.vue"
import Highcharts from "@/highcharts"
import LButton from "@/components/buttons/LButton.vue"
import colors from "@/colors"
import { computed, onMounted, ref } from "vue"

const props = defineProps({
  school: { type: Object, required: true },
})

const id = "employment-breakdown"

const active = ref(null)
const chart = ref(null)

const chartColors = [
  colors.yellow[400],
  colors.teal[400],
  colors.orange[400],
  colors.blue[400],
  colors.rose[400],
  colors.indigo[400],
  colors.gray[400],
]

const select = (job) => {
  active.value = job

  if (!job) {
    chart.value.series[0].update({
      ...allSeries.value,
    }, true)
  } else if (job.items?.length) {
    const data = []

    let sum = 0
    job.items.forEach((item) => {
      const y = item.percent
      sum += y
      data.push({
        ...item,
        y,
      })
    })

    data.push({
      name: `Not ${job.name.toLowerCase()}`,
      color: colors.gray[200],
      y: 100 - sum,
      events: {
        click: () => select(null),
      },
    })

    chart.value.series[0].update({
      ...series,
      name: job.name,
      data,
    }, true)
  }
}

const chartOptions = {
  chart: {
    type: "pie",
    margin: [0, 0, 0, 0],
    spacing: [0, 0, 0, 0],
  },
  title: {
    text: "",
  },
  tooltip: {
    formatter() {
      return `<b>${this.key}</b> ${this.y}%`
    },
  },
  colors: chartColors,
  plotOptions: {
    pie: {
      shadow: false,
      allowPointSelect: false,
      cursor: "pointer",
      dataLabels: {
        enabled: false,
      },
      center: ["50%", "50%"],
    },
  },
}

const click = (event) => {
  if (event.point?.options?.items?.length) {
    select(event.point.options)
  }
}

const series = {
  type: "pie",
  size: "100%", innerSize: "50%",
  animation: false,
  states: { hover: { enabled: false }, inactive: { opacity: .8 } },
  groupPadding: 0,
  pointPadding: 0,
  events: {
    click,
  },
}

const allSeries = computed(() => {
  return {
    ...series,
    name: "Employer type",
    data: props.school.jobs_by_employer_type.map((job) => ({
      name: job.name,
      y: job.percent,
      items: job.items || [],
    })),
  }
})

onMounted(() => {
  chart.value = new Highcharts.Chart(id, {
    ...chartOptions,
    series: [
      allSeries.value,
    ],
  })
})
</script>
