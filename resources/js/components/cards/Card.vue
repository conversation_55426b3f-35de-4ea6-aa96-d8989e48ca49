<template>
  <div class="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-800 shadow-sm rounded-lg flex flex-col">
    <div class="p-3 leading-none flex gap-3 justify-between items-center border-b border-gray-300 dark:border-gray-800">
      <slot v-if="$slots.header" name="header"/>
      <component :is="`h${level}`" v-else-if="$slots.title || title" class="font-medium text-blue dark:text-white">
        <slot v-if="$slots.title" name="title"/>
        <template v-else>
          {{ title }}
        </template>
      </component>

      <l-menu v-if="href" type="overflow">
        <l-menu-item :href="href">
          View details
        </l-menu-item>
      </l-menu>
    </div>

    <slot/>

    <div v-if="$slots.body || $slots.footer" class="flex-1 rounded-b-lg flex flex-col p-3">
      <slot name="body"/>
      <slot name="footer"/>
    </div>
  </div>
</template>

<script setup>
import LMenu from "@/components/menus/LMenu.vue"
import LMenuItem from "@/components/menus/LMenuItem.vue"

defineProps({
  title: String,
  href: String,
  level: { type: Number, default: 2 },
})
</script>
