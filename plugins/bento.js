// Docs: https://bentonow.com/docs/bento-js-sdk

export default ({ app }, inject) => {
  (function () {
    const d = document
    const s = d.createElement('script')

    s.src = 'https://app.bentonow.com/' + process.env.BENTO_SITE_UUID + '.js'
    s.async = 1
    d.getElementsByTagName('head')[0].appendChild(s)
  })()

  const identify = ({ email }) => {
    try {
      window.bento.identify(email)
      window.bento.view()
    } catch (err) { return false }
  }

  const track = (name, data) => {
    try {
      window.bento.track(name, data)
    } catch (err) { return false }
  }

  const tag = (tag) => {
    try {
      window.bento.tag(tag)
    } catch (err) { return false }
  }

  const updateFields = (data) => {
    try {
      window.bento.updateFields(data)
    } catch (err) { return false }
  }

  const showChat = () => {
    try {
      window.bento.showChat()
    } catch (err) { return false }
  }

  const Bento = { track, identify, tag, updateFields, showChat }

  inject('bento', Bento)

  app.router.afterEach((to, from) => {
    if (window?.bento) {
      window.bento.view()
    }
  })

  // // Inject $hello(msg) in Vue, context and store.
  // inject('crisp', action => window.$crisp.push(action))
  // window.$crisp.push(['safe', true])
}
