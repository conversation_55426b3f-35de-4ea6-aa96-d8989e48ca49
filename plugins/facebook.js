/* eslint-disable */ 
export default ({ app, $axios, $cookies, route, store }, inject) => {
  
  const trackPurchase = ({transactionId}) => {
    // We don't really use this. 
    // Purchase is being tracked in webhook and PayPal payment capture
    $axios.$post('/t/fb/sale', {
      cookies: $cookies.getAll(),
      transactionId,
      currentUrl: 'https://www.headshotpro.com'+ route.fullPath,
      uid: store?.state?.user?.uid || null
    }).then((response) => {
    }).catch((err) => {
      console.log(err)
    })
  }

  const trackEvent = (payload) => {
    let data, name = null
    if(typeof payload === 'string'){
      name = payload
    }
    if(typeof payload === 'object'){
      name = payload.name
      data = payload.data
    }

    // console.log($cookies)

    // Now we can properly use store

    $axios.$post('/t/fb/event', {
      cookies: $cookies.getAll(),
      eventName: name,
      customData: data,
      currentUrl: 'https://www.headshotpro.com'+ route.fullPath,
      uid: store?.state?.user?.uid || null
    }).then((response) => {
    }).catch((err) => {
      console.log(err)
    })
  }

  // const identify = (data) => {
  //   splitbee.user.set(data)
  // }

  inject('facebook', { trackPurchase, trackEvent })

  app.router.afterEach((to, from) => {
    // console.log(, from)
    if(to?.query?.fbclid && !$cookies.get('_fbc')){
      $cookies.set('_fbc', `fb.1.${Date.now()}.${to.query.fbclid}`)
    }

    if(from?.query?.fbclid && !$cookies.get('_fbc')){
      $cookies.set('_fbc', `fb.1.${Date.now()}.${from.query.fbclid}`)
    }

    if(!$cookies.get('_fbp')){
      const random_number = Math.floor(Math.random() * 900000000) + 100000000;
      $cookies.set('_fbp', `fb.1.${Date.now()}.${random_number}`)
    }
    // fb.${subdomain_index}.${creation_time}.${fbclid}.
    trackEvent('PageView')
  })
}

// const createFbpCookie = ($cookies) => {
//   const randomNumber = Math.floor(Math.random() * 900000000) + 100000000;
//   $cookies.set('_fbp', `fb.1.${Date.now()}.${randomNumber}`)
// }
