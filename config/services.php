<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of about, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'avatax' => [
        'account_id' => env('AVATAX_ACCOUNT_ID', '**********'),
        'endpoint' => env('AVATAX_ENDPOINT', 'https://sandbox-rest.avatax.com'),
        'license_key' => env('AVATAX_LICENSE_KEY', 'B46802C88BB6B69D'),
    ],

    'chatgpt' => [
        'api_key' => env('CHATGPT_API_KEY'),
    ],

    'github' => [
        'assets_token' => env('GITHUB_ASSETS_TOKEN'),
    ],

    'mapbox' => [
        'token' => env('MAPBOX_TOKEN'),
    ],

    'sentry' => [
        'js' => [
            'dsn' => env('SENTRY_JAVASCRIPT_DSN', env('SENTRY_DSN')),
            'environment' => config('sentry.environment'),
            'release' => config('sentry.release'),
        ],
    ],

    'spotify' => [
        'client_id' => env('SPOTIFY_CLIENT_ID'),
        'client_secret' => env('SPOTIFY_CLIENT_SECRET'),
        'show_id' => env('SPOTIFY_SHOW_ID', '2p2SWIkNKUeWUJ2NFY0xNu'),
    ],

    'stripe' => [
        'key' => env('STRIPE_KEY', 'pk_test_afV3R6b116VWIeAOmXGKZoP9001PJ9H89v'),
        'secret' => env('STRIPE_SECRET', 'sk_test_51G8Y3BL41VJErhSCI6crAL0tXW7eIsaFJ4NPgja0hBLgE3HS6glNLaL0QxAFfxMyxLt2Dw47uZMPwAzW96ApozQS00L9PJptEn'),
    ],

];
