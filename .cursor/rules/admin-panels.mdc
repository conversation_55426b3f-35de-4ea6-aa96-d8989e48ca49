---
description:
globs:
alwaysApply: true
---
# Admin Panels Conventions

This project has two distinct admin areas:
1.  **Super Admin / General Admin Panel**: For internal company administrators.
2.  **Team Admin Panel / App Admin**: For users with roles like `TeamLead` to manage their teams.

## 1. Super Admin Panel (General Admin)
- **Location**: Code primarily resides in `pages/admin/`.
- **Layout**: Uses `layouts/admin.vue`.
- **Vuex Store**: May use `store/admin.js` for some state management, but also interacts with other store modules.
- **Functionality**: Comprehensive control over the application, including:
    - User management (`pages/admin/user/`)
    - Content management (styles, clothing, blog, testimonials - various files/folders in `pages/admin/`)
    - Financials (invoices, transactions, coupons, refunds)
    - Operational settings (studio, models, parameters, audit logs)
    - Support tools, playground, etc.
- **Access Control**: Likely controlled by Firebase user roles/custom claims, checked in page middleware or `mounted()` hooks, and potentially via the `adminRole` in the Vuex user state.

## 2. Team Admin Panel (App Admin)
- **Location**: Code primarily resides in `pages/app/admin/`.
- **Layout**: Often uses the `protected.vue` layout (as it's part of the authenticated app experience), but could also use a specific `teamLead.vue` layout or `admin.vue` if styled similarly to super admin.
- **Vuex Store**: Interacts heavily with `store/user.js` (for `TeamLead` role) and `store/organization.js`.
- **Functionality**: Focused on team management:
    - Dashboard (`pages/app/admin/index.vue`)
    - Team member management (`pages/app/admin/team/`)
    - Team settings, credits, invoices
    - Team-specific styling (clothing, styles)
    - Branding, API access for team
- **Access Control**: Primarily based on the user's role (e.g., `this.$store.state.user.role === 'TeamLead'`). Seen in computed properties like `isTeamLead` in the global mixin.

## General Conventions for Admin Areas
- **Data Display**: Tables, lists, and cards are common for displaying data. Look for reusable components for these (e.g., in `components/table/`, `components/list/`, `components/card/`).
- **Forms**: CRUD operations (Create, Read, Update, Delete) are frequent. Forms will use `<Input />` and other custom input components.
- **API Interaction**: Heavy use of `this.$axios` to fetch and manipulate data.
- **Loading States**: Use `this.$loading.show()` or component-local `isLoading` flags for async operations.
- **Modals/Popups**: Used for confirmations, editing forms, etc. (e.g., `<ModalConfirmDelete />`).
