---
description: Guide for the <InputCheckbox> component (components/input/Checkbox.vue), a styled checkbox with a label, supporting v-model and dark mode. Emphasizes providing a unique id.
globs:
alwaysApply: false
---
# Component Guide: InputCheckbox (Checkbox)

**File Path**: `components/input/Checkbox.vue`
(Typically used as `<InputCheckbox>` in templates).

## Overview
The `<InputCheckbox>` component provides a styled checkbox with an associated label. It wraps the native `<input type="checkbox">` element and handles data binding.

## Template Structure (Simplified Internal)
```html
<div class="flex content-center items-center justify-start space-x-2">
  <input
    :id="id"
    v-model="modelValue"
    type="checkbox"
  />
  <label
    v-if="label"
    :for="id"
    class="text-left align-middle block text-sm font-medium leading-[16px]"
    :class="{'text-white/70': darkMode, 'text-gray-700':!darkMode}"
  >
    {{ label }}
  </label>
</div>
```
- Uses a `div` to group the checkbox and its label.
- The internal `v-model="modelValue"` on the input is linked to a computed property that gets/sets the `value` prop.

## Props

- **`value`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: The checked state of the checkbox. `true` if checked, `false` otherwise. This is the prop to use with `v-model`.
- **`label`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Text label displayed next to the checkbox. Clicking the label also toggles the checkbox due to the `for` attribute.
- **`id`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Sets the `id` attribute for the input element and the `for` attribute of the label, linking them. **It is important to provide a unique `id` if you have multiple checkboxes on a page to ensure labels work correctly.**
- **`darkMode`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Applies a dark mode theme, primarily affecting the label text color.
- **`description`**: (Declared in props, but not used in the template provided in the prompt)
    - Type: `String`
    - Default: `''`
    - Purpose: Intended for a description, but currently has no visual output in the component's template.

## Slots
- None.

## Events

- **`input`**:
    - Payload: The new boolean state of the checkbox (`true` or `false`).
    - Purpose: Emitted when the checkbox state changes. This is used by `v-model`.

## Computed Properties

- **`modelValue`**:
    - `get()`: Returns `this.value`.
    - `set(newValue)`: Emits `this.$emit('input', newValue)`.
    - Purpose: Facilitates `v-model` usage on the internal `<input type="checkbox">`.

## Usage Examples

1.  **Basic Checkbox with Label and `v-model`**:
    ```html
    <InputCheckbox
      id="agreeToTerms"
      v-model="form.agreedToTerms"
      label="I agree to the terms and conditions"
    />
    ```
    *Remember to provide a unique `id`.*

2.  **Checkbox with Dark Mode**:
    ```html
    <InputCheckbox
      id="enableNotifications"
      v-model="settings.notificationsEnabled"
      label="Enable email notifications"
      darkMode
    />
    ```

3.  **Multiple Checkboxes (ensure unique IDs)**:
    ```html
    <InputCheckbox
      id="optionA"
      v-model="selectedOptions.a"
      label="Option A"
    />
    <InputCheckbox
      id="optionB"
      v-model="selectedOptions.b"
      label="Option B"
      class="mt-2"
    />
    ```

4.  **Transactional Email Toggle (from Grep results)**:
    ```html
    <!-- File: pages/admin/email/create.vue -->
    <InputCheckbox
      id="transactional"
      v-model="email.transactional"
      label="Transactional email"
    />
    ```

## Styling
- The component uses the browser's default rendering for the checkbox input itself. If global styles are applied to `input[type="checkbox"]` (e.g., via `@tailwindcss/forms` if configured for it), those will apply.
- The label is styled with Tailwind classes (`text-sm font-medium leading-[16px]`).
- `darkMode` prop affects the label's text color (`text-white/70` vs `text-gray-700`).
- The checkbox and label are arranged horizontally using `flex` and `space-x-2`.

## Best Practices
- **Always provide a unique `id` prop.** This is crucial for accessibility and ensuring that clicking the label correctly toggles the associated checkbox.
- Use `v-model` for straightforward two-way data binding of the checkbox state.
- Ensure the `label` text clearly describes the purpose of the checkbox.
- If you need a group of checkboxes where only one can be selected, use radio buttons instead.
- The `description` prop currently does not render anything; avoid using it unless the component is updated.
