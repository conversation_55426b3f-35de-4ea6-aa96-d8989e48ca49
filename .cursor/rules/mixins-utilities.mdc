---
description:
globs:
alwaysApply: true
---
# Mixins and Utility Functions Conventions

## Global Mixin (`plugins/mixins.js`)
- A very large global mixin is registered in `plugins/mixins.js`, making a wide array of computed properties and methods available to ALL Vue components.
- **Key Provided Functionalities (Examples - refer to the file for the full list)**:
    - **State Access**: Computed properties for Vuex state (e.g., `isLoggedIn`, `isTeamMember`, `packages`, `userCurrency`).
    - **Environment/Context**: `env`, `isDevelopment`, `isSafari`, `isIframe`, `isMaintenanceMode`.
    - **User & Auth**: Methods like `setupUserDetails`, `logout`, `createDatabaseAccount` (often interacting with Firebase).
    - **Pricing & Currency**: `formatPrice`, `getFinalPrice`, `getLocalizedPrice` (works with `plugins/localization.js`).
    - **Tracking**: `trackUniqueVisitor`, `trackReferral`, `trackGclid`, `trackFb` (PostHog and other tracking integrations).
    - **Image Handling**: `dnsImage(url)` - use this for image URLs to potentially leverage CDN/optimizations.
    - **File Downloads**: `downloadFromUrl(urls)` - downloads multiple files as a zip using JSZip.
    - **String/Data Formatting**: `titleCase`, `convertToSlug`, `formatDate`, `timestampToDate`, `capitalizeFirstLetter`.
    - **Input Validation**: `isNumber`, `inputNumbersOnly`, `isValidEmail`.
    - **UI Helpers**: `openLiveChat`, `setModalStatus`, `refreshMasonry`, `handleError`.
    - **PostHog Integration**: Incorporates `PosthogMixin` and has methods like `setupPosthogExperiments`.
- This mixin is a central hub for shared logic. Be aware of its existence when looking for common functionalities.

## Other Mixins (`mixins/` directory)
- Specific mixins are located in `mixins/` (e.g., `CheckoutMixin.js`, `EmailSignatureMixin.js`, `ModelPersonalizationMixin.js`, `DownloadTrackingMixin.js`).
- These are typically more focused than the global mixin and might be imported and used by a smaller set of components related to their specific domain.
- Example: `DownloadTrackingMixin.js` has `trackDownloads()` method used to track photo downloads and potentially trigger a review modal.

## Utility Functions (`utils/` directory)
- The `utils/` directory is the place for generic, reusable JavaScript functions that don't necessarily fit into a Vue mixin or component.
- Example: `utils/axios.js` (custom Axios instance), `utils/template-to-render.js` (used by `vue-loading.js` plugin).
- Organize utility functions into logical files (e.g., `stringUtils.js`, `arrayUtils.js`).

## Usage
- **Global Mixin**: Its properties and methods are directly available on `this` in any Vue component.
- **Specific Mixins**: If not globally registered, import and include them in the `mixins: [MySpecificMixin]` array in a component's script section.
- **Utility Functions**: Import them directly into the JavaScript file or Vue component where they are needed:
  `import { someUtil } from '@/utils/myUtils.js'`

## Considerations
- While the global mixin provides easy access to many functions, be mindful that it can make code harder to trace. Prefer specific mixins or utility functions if the logic is not truly global.
- When adding new shared logic, consider if it belongs in the global mixin, a new specific mixin, or a utility file.
