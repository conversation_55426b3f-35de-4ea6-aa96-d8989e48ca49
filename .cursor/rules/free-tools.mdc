---
description:
globs:
alwaysApply: false
---
# Free Tools Section

## Overview
- The application offers a set of free tools for users, accessible without requiring an account or payment.
- These tools are located as individual page components within the `pages/tools/` directory.

## Available Tools
Based on files in `pages/tools/`:
- `free-team-page-generator.vue`
- `free-linkedin-headline-generator.vue`
- `free-linkedin-profile-generator.vue`
- `free-linkedin-bio-generator.vue`
- `free-headshot-generator.vue` (This might be a limited version or a lead magnet for the main product)
- `free-email-signature-generator.vue`
- `free-pfp-generator.vue` (Profile Picture Generator)

## Layout
- These tool pages likely use the `layouts/tools.vue` layout, which might provide a different branding or navigation structure compared to the main application or marketing pages.

## Functionality
- Each tool is self-contained within its Vue page component.
- They likely involve:
    - Input fields for users to enter data (using `<Input />` and other form components).
    - Logic to process the input and generate an output.
    - Display of the generated result.
    - Option to copy or download the result.
- These tools might interact with specific utility functions or, in some cases, make non-authenticated API calls if backend processing is needed.

## Development Considerations
- When working on or creating new free tools:
    - Place the new page component in `pages/tools/`.
    - Ensure it uses the `layouts/tools.vue` layout if appropriate.
    - Keep the tool focused on a single, clear utility.
    - Maintain a user-friendly and straightforward UI.
    - Consider how the tool might encourage users to explore the main paid features of the application (e.g., via subtle CTAs or by showcasing a limited version of a core feature).
