---
description: Guide for the <AlertWarning> component (components/alert/Warning.vue), used for displaying non-dismissible warning messages with a title, description, and optional slotted content.
globs:
alwaysApply: false
---
# Component Guide: AlertWarning

**File Path**: `components/alert/Warning.vue`
(Typically used as `<AlertWarning>` in templates).

## Overview
The `<AlertWarning>` component is used to display warning messages. It features a yellow color scheme, a warning icon, and is suitable for drawing attention to important information, potential issues, or non-critical errors that the user should be aware of.

## Template Structure (Simplified)
```html
<div class="text-left border border-yellow-300 rounded-lg bg-yellow-50">
  <div class="p-3">
    <div class="flex items-start justify-start">
      <svg class="flex-shrink-0 w-5 h-5 text-yellow-500"> <!-- Warning Icon --> </svg>
      <div class="ml-3">
        <p class="text-sm font-bold text-yellow-900">{{ title }}</p>
        <p class="text-xs mt-0.5 font-medium text-yellow-900">{{ description }}</p>
        <slot /> <!-- For additional content like buttons or links -->
      </div>
      <!-- Commented out dismiss button -->
    </div>
  </div>
</div>
```
- The component has a commented-out section for a dismiss button. This means it's non-dismissible by default in its current state.

## Props

- **`title`**:
    - Type: `String`
    - Required: No (props are not marked `required: true`)
    - Purpose: The main heading for the warning alert.
- **`description`**:
    - Type: `String`
    - Required: No
    - Purpose: A more detailed message below the title.

## Slots

- **Default Slot**:
    - Purpose: Allows injecting additional content below the description, such as action buttons, links, or more detailed text.
    - Example:
      ```html
      <AlertWarning title="Low Disk Space" description="Your account is nearing its storage limit.">
        <ButtonWhite size="xs" class="mt-2" @click="manageStorage">Manage Storage</ButtonWhite>
      </AlertWarning>
      ```

## Events
- None directly from the component itself (as the dismiss button is commented out).

## Usage Examples

1.  **Basic Warning with Title and Description**:
    ```html
    <AlertWarning
      title="Session Expiring Soon"
      description="Please save your work. You will be logged out in 5 minutes."
    />
    ```
    *Source Example*: `pages/app/index.vue` uses `<AlertWarning title="Error loading data" description="We couldn't load your data. Please try a different browser or again later." />`.

2.  **Warning with Slotted Content (e.g., an action link)**:
    ```html
    <AlertWarning
      title="Action Required"
      description="Your payment method needs to be updated."
    >
      <nuxt-link to="/billing" class="mt-1 text-xs font-semibold text-yellow-900 hover:text-yellow-700">
        Update Payment Method &rarr;
      </nuxt-link>
    </AlertWarning>
    ```

3.  **Conditional Warning (from Grep result)**:
    ```html
    <!-- File: components/upload/Package.vue -->
    <AlertWarning
      v-if="hasInvites && showInviteWarning"
      class="mb-2"
      title="Accept invite to continue for free."
      description="You have invites to join a team. Click on the 'Invites' button to accept them"
    />
    ```

## Styling
- Uses a light yellow background (`bg-yellow-50`) with a yellow border (`border-yellow-300`).
- Text is dark yellow/brown (`text-yellow-900`).
- Includes a hardcoded SVG warning icon (`text-yellow-500`).
- The title is bold (`font-bold text-sm`) and the description is slightly smaller and medium weight (`text-xs font-medium`).

## Best Practices
- Use `<AlertWarning>` for messages that require user attention but may not be critical errors (for critical errors, a red-themed error alert might be more appropriate, if available).
- Provide both `title` and `description` for clarity.
- Utilize the default slot for calls to action or links if the user needs to resolve the warning.
- Since it's not dismissible by default, ensure the warning is relevant or use `v-if` to control its visibility appropriately.
- If a dismissible warning is needed, this component might need modification, or `<AlertWarningV2>` (if it supports dismissal) or another component should be used.
