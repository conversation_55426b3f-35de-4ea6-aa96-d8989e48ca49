---
description:
globs:
alwaysApply: false
---
# PostHog Experiments & Feature Flags (Using PosthogMixin.js)

## Overview
PostHog is used for A/B testing and feature flagging. The integration allows for dynamic control over features and UI variations based on flags retrieved from the PostHog platform. The primary way to interact with these flags is through methods provided by `mixins/PosthogMixin.js`, which is globally available in components.

## Initialization
- Feature flags are initialized and fetched during page load via the `setupPosthogExperiments()` method within the global mixin (`plugins/mixins.js`). This method is called as part of `initPageLoad()`.
- `this.$posthog.onFeatureFlags()`: Ensures that flag-dependent logic runs only after flags are loaded from PostHog.
- Raw flag data from `this.$posthog.getFeatureFlag(flagName)` is fetched and committed to the Vuex store via `this.$store.commit('SET_POSTHOG_FEATURE_FLAGS', flagsAndVersion)`. The `PosthogMixin` then reads from this store cache.

## Accessing Feature Flags/Variants via `PosthogMixin.js`

The mixin provides helper methods to access and check feature flags. These methods should be preferred over directly accessing the Vuex store or raw `$posthog` calls for feature flags.

1.  **`getFeatureFlagVariant(testId)`**
    - **Purpose**: Retrieves the variant for a given `testId` (feature flag key).
    - **Logic**:
        - First checks if the flag is being overridden by URL parameters: `?testFlag=testId&testVariant=variantValue`. If so, returns `variantValue`.
        - Otherwise, returns the variant from `this.$store.state.posthogFeatureFlags[testId]`.
        - Returns `'control'` if any error occurs during retrieval.
    - **Usage**:
      ```javascript
      // In a component method or computed property
      const currentVariant = this.getFeatureFlagVariant('new-homepage-design'); // e.g., 'variant_a', 'variant_b', 'control'
      if (currentVariant === 'variant_a') {
        // Logic for variant A
      }
      ```

2.  **`featureFlagExists(testId)`**
    - **Purpose**: Checks if a feature flag (identified by `testId`) exists or is being overridden by URL.
    - **Logic**:
        - Returns `true` if the flag is being overridden by URL parameters (`?testFlag=testId&testVariant=...`).
        - Otherwise, checks if `this.$store.state.posthogFeatureFlags[testId]` is defined.
        - Returns `false` if any error occurs.
    - **Usage**:
      ```javascript
      if (this.featureFlagExists('beta-feature-x')) {
        // Feature X is active (either via PostHog or URL override)
        const variant = this.getFeatureFlagVariant('beta-feature-x');
        // ... proceed with variant-specific logic
      } else {
        // Fallback logic if the flag doesn't exist
      }
      ```

3.  **`experimentIsTest(testId)`**
    - **Purpose**: A convenience method to specifically check if an experiment's variant is the `'test'` variant.
    - **Logic**:
        - First checks if the flag exists using `this.featureFlagExists(testId)`.
        - If it exists, it then checks if `this.getFeatureFlagVariant(testId)` returns exactly `'test'`.
    - **Usage**:
      ```javascript
      // Typically used when an experiment has a 'control' and a 'test' variant.
      if (this.experimentIsTest('checkout-flow-v2')) {
        // User is in the 'test' group for the new checkout flow
        // Render NewCheckoutComponent
      } else {
        // User is in the 'control' group or flag doesn't exist
        // Render OldCheckoutComponent
      }
      ```

## Computed Property in Mixin
- **`posthogFeatureFlags`**: A computed property in the mixin that directly returns `this.$store.state.posthogFeatureFlags`. This is used internally by the methods above.

## Implementing Conditional Logic
- Use the methods from `PosthogMixin.js` to make decisions in your component logic or templates.

  **Example in `<script>`:**
  ```javascript
  export default {
    // PosthogMixin is globally available
    computed: {
      showNewDashboard() {
        if (this.featureFlagExists('dashboard-revamp')) {
          return this.getFeatureFlagVariant('dashboard-revamp') === 'new_ui';
        }
        return false; // Default to old UI if flag not present
      },
      isSimplifiedSignup() {
        return this.experimentIsTest('simplified-signup-flow');
      }
    },
    methods: {
      handleFeatureClick() {
        const variant = this.getFeatureFlagVariant('cta-button-text');
        if (variant === 'short_text') {
          // ...
        } else if (variant === 'long_text') {
          // ...
        }
      }
    }
  }
  ```

  **Example in `<template>` (less common to call methods directly, better to use computed props):**
  ```vue
  <template>
    <div>
      <div v-if="showNewDashboard">
        <NewDashboardComponent />
      </div>
      <div v-else>
        <OldDashboardComponent />
      </div>

      <ButtonPrimary v-if="isSimplifiedSignup">Sign Up (Quick)</ButtonPrimary>
      <ButtonPrimary v-else>Sign Up</ButtonPrimary>
    </div>
  </template>
  ```

## Using `<ExperimentWrapper>` for Declarative Experiments

For a more declarative way to handle experiment variations directly in your templates, the project provides an `<ExperimentWrapper>` component (`components/experiment/Wrapper.vue`). This component simplifies showing different content based on the experiment variant.

**File Path**: `components/experiment/Wrapper.vue`
(Typically used as `<ExperimentWrapper>` or `<experiment-wrapper>` in templates)

**Overview**:
The `<ExperimentWrapper>` component uses the PostHog methods from `PosthogMixin.js` internally to determine the current variant for a given experiment ID. It then dynamically renders a slot corresponding to that variant name.

**Props**:
- **`id`**:
    - Type: `String`
    - Required: `true`
    - Purpose: The PostHog feature flag key (experiment ID) to check.
- **`type`**:
    - Type: `String`
    - Default: `'div'`
    - Purpose: The type of HTML element to use as the wrapper for the slot content (e.g., `'div'`, `'span'`, `'section'`).

**Computed Property**:
- **`variantId`**:
    - Internally computes the variant using `this.featureFlagExists(this.id)` and `this.getFeatureFlagVariant(this.id)`.
    - Defaults to `'control'` if the flag doesn't exist or an error occurs.

**Slots**:
- **`control`**: Named slot for the content to be displayed if the variant is `'control'` or if the flag doesn't resolve to a specific variant name.
- **Dynamic Named Slots**: For any other variant name returned by `getFeatureFlagVariant(this.id)` (e.g., `test`, `variant_a`, `new_ui`), you should provide a slot with that exact name.
    - Example: If `getFeatureFlagVariant(this.id)` returns `'treatment_x'`, the content within `<template #treatment_x>...</template>` will be rendered.

**Usage Example**:

```vue
<template>
  <div>
    <h2>New Feature Section</h2>
    <ExperimentWrapper id="new-feature-rollout" type="section">
      <template #control>
        <p>This is the standard, old content (control group).</p>
        <OldComponent />
      </template>

      <template #test_variant_A>
        <p>This is new experimental content for Variant A.</p>
        <NewComponentVariantA />
      </template>

      <template #test_variant_B>
        <p>This is different new content for Variant B.</p>
        <NewComponentVariantB />
      </template>

      <!--
        If getFeatureFlagVariant('new-feature-rollout') returns 'test_variant_A',
        only the content in the #test_variant_A slot will be rendered within a <section> tag.
        If it returns 'control' or the flag doesn't exist, the #control slot is rendered.
      -->
    </ExperimentWrapper>

    <ExperimentWrapper id="simple-ab-test" type="span">
      <template #control>
        Original Text
      </template>
      <template #test>
        New Experimental Text
      </template>
    </ExperimentWrapper>
  </div>
</template>

<script>
// No specific script logic needed in the parent for the wrapper to function,
// as ExperimentWrapper handles the flag checking internally.
// Parent component still needs PosthogMixin for other potential PostHog interactions.
export default {
  // ...
}
</script>
```

**When to Use `<ExperimentWrapper>`**:
- When you need to switch out entire blocks of template code based on an experiment variant.
- When you prefer a more declarative, template-driven approach to A/B testing UI elements.
- It keeps the script section cleaner by encapsulating the variant-checking logic for rendering.

## Overriding Flags for Testing (Previewing)
- The `getFeatureFlagVariant` and `featureFlagExists` methods in `PosthogMixin.js` automatically handle overrides via URL query parameters:
  `?testFlag=your-flag-key&testVariant=desired-variant`
  (e.g., `?testFlag=new-checkout&testVariant=test` or `?testFlag=homepage-text&testVariant=variant_b`).
- This allows developers and QA to force specific variants for testing without needing PostHog UI changes.
- The older `?preview=flag-key:variant-value` mechanism (handled in `plugins/mixins.js` by directly calling `this.$posthog.featureFlags.override()`) also still exists and would override the flags *before* they are read by the `PosthogMixin` methods, effectively achieving a similar outcome for previewing.

## Tracking Experiment Events
- To measure the impact of experiments, relevant user interactions should be tracked using PostHog events.
- This is typically done using `this.$posthog.capture('event_name', { property1: 'value1', ... })`. This part of PostHog interaction is separate from the flag *retrieval* methods in `PosthogMixin.js`.
- When capturing events related to an experiment, always include the variant as a property:
  ```javascript
  const variant = this.getFeatureFlagVariant('my-cta-experiment');
  this.$posthog.capture('cta_clicked', {
    cta_name: 'learn_more_button',
    experiment_variant: variant || 'undefined' // Send the variant
  });
  ```
- The global mixin (`plugins/mixins.js`) already includes `PosthogMixin.js`, making `this.$posthog.capture` available.

## Best Practices
- **Use the Mixin or Wrapper**: Always prefer `this.getFeatureFlagVariant()`, etc., from `PosthogMixin.js` for script-based logic, or `<ExperimentWrapper>` for template-based declarative experiments.
- **Clear Naming**: Use consistent and clear `testId` names for flags in PostHog and in the code.
- **Default/Control State**: Ensure your code gracefully handles cases where a flag might not exist or an unexpected variant is returned (though `getFeatureFlagVariant` defaults to `'control'`).
- **Testing**: Thoroughly test all variants and the default experience.
- **Cleanup**: Regularly remove old or completed experiment flags from PostHog and associated code.
