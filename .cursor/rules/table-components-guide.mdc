---
description: Guide for the suite of table components: <Table> (main wrapper), <TableHead> (headers), <TableRow> (rows), and <TableItem> (cells). Covers their props, slots, and typical usage.
globs:
alwaysApply: false
---
# Component Guide: Table Components

**File Paths**:
- `components/table/Table.vue` (Main wrapper, used as `<Table>`)
- `components/table/Head.vue` (Table header, used as `<TableHead>`)
- `components/table/Row.vue` (Table row, used as `<TableRow>`)
- `components/table/Item.vue` (Table cell/item, used as `<TableItem>`)

## Overview
This set of components provides a structured way to create HTML tables with consistent styling. `<Table>` is the main container, `<TableHead>` defines the header row, `<TableRow>` represents each data row, and `<TableItem>` represents individual cells within a row.

## Component Details

### 1. `<Table>` (`components/table/Table.vue`)
- **Purpose**: The main wrapper for the entire table.
- **Template Structure (Simplified)**:
  ```html
  <div>
    <table class="min-w-full divide-y divide-gray-300">
      <TableHead v-if="head" :head="head" class="top-0" :class="{ 'lg:sticky lg:self-start': sticky }" />
      <tbody class="divide-y divide-[#EAECF0] bg-white">
        <slot /> <!-- Expected to contain <TableRow> components -->
      </tbody>
    </table>
  </div>
  ```
- **Props**:
    - `head`:
        - Type: `Array`
        - Required: `false`
        - Purpose: An array of strings to be passed to `<TableHead>` for rendering column headers.
    - `sticky`:
        - Type: `Boolean`
        - Required: `false`
        - Default: `true`
        - Purpose: If `true` (and `head` is provided), makes the table header sticky on large screens (`lg:sticky`).
- **Slots**:
    - **Default Slot**: This is where you place your `<TableRow>` components.

### 2. `<TableHead>` (`components/table/Head.vue`)
- **Purpose**: Renders the `<thead>` section of the table with column headers.
- **Template Structure (Simplified)**:
  ```html
  <thead class="bg-[#FCFCFD]">
    <template v-for="(item, index) in head">
      <th :key="index" class="py-3 table-break-all text-left text-xs font-medium text-[#667085] last:relative px-6 last:text-right">
        {{ item }}
      </th>
    </template>
  </thead>
  ```
- **Props**:
    - `head`:
        - Type: `Array`
        - Required: `true`
        - Purpose: An array of strings, where each string is a column header title.
- **Usage**: Typically used internally by the `<Table>` component when its `head` prop is provided. Can also be used directly within a `<table>` if manual control is preferred.

### 3. `<TableRow>` (`components/table/Row.vue`)
- **Purpose**: Represents a `<tr>` (table row) in the table body.
- **Template Structure (Simplified)**:
  ```html
  <tr class="hover:bg-[#FCFCFD]">
    <slot /> <!-- Expected to contain <TableItem> components -->
  </tr>
  ```
- **Props**: None.
- **Slots**:
    - **Default Slot**: This is where you place `<TableItem>` components for the cells of this row.

### 4. `<TableItem>` (`components/table/Item.vue`)
- **Purpose**: Represents a `<td>` (table data cell) within a `<TableRow>`.
- **Template Structure (Simplified)**:
  ```html
  <td class="table-break-all p-6 text-sm text-[#667085]">
    <slot /> <!-- Content of the cell -->
  </td>
  ```
- **Props**: None.
- **Slots**:
    - **Default Slot**: Content for the table cell (text, links, buttons, other components, etc.).

## Usage Example

```vue
<template>
  <div class="container mx-auto p-4">
    <Table :head="tableHeaders" :sticky="true">
      <TableRow v-for="user in users" :key="user.id">
        <TableItem>
          <div class="font-medium text-gray-900">{{ user.name }}</div>
          <div class="text-gray-500">{{ user.email }}</div>
        </TableItem>
        <TableItem>{{ user.role }}</TableItem>
        <TableItem>
          <span :class="user.status === 'Active' ? 'text-green-600' : 'text-red-600'">
            {{ user.status }}
          </span>
        </TableItem>
        <TableItem class="text-right">
          <ButtonWhite size="xs" @click="editUser(user.id)">Edit</ButtonWhite>
        </TableItem>
      </TableRow>

      <TableRow v-if="users.length === 0">
        <TableItem colspan="4" class="text-center text-gray-500">
          No users found.
        </TableItem>
      </TableRow>
    </Table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableHeaders: ['Name', 'Role', 'Status', 'Action'],
      users: [
        { id: 1, name: 'Jane Cooper', email: '<EMAIL>', role: 'Admin', status: 'Active' },
        { id: 2, name: 'Cody Fisher', email: '<EMAIL>', role: 'Member', status: 'Inactive' },
        // ... more users
      ]
    };
  },
  methods: {
    editUser(userId) {
      // console.log('Edit user:', userId);
      this.$router.push(`/admin/users/${userId}/edit`);
    }
  }
};
</script>
```
*Grep results show many uses in admin pages like `pages/admin/auditlog.vue`, `pages/profile/invoices.vue`, etc., often directly placing `<TableItem>` inside `<TableRow>` and those inside a main `<table>` or `<Table>` component.*

## Styling
- `<Table>`: `min-w-full`, divides table and body with `divide-y divide-gray-300` and `divide-[#EAECF0]` respectively. Body background is `bg-white`.
- `<TableHead>`: Header background `bg-[#FCFCFD]`. Header cells (`<th>`) are `py-3 px-6`, `text-left`, `text-xs font-medium text-[#667085]`. The class `table-break-all` is applied to header cells, which might imply `word-break: break-all` or similar, but this custom class needs verification if specific break behavior is critical.
- `<TableRow>`: Hover effect `hover:bg-[#FCFCFD]`.
- `<TableItem>`: Padding `p-6`, text style `text-sm text-[#667085]`. Also applies `table-break-all`.

## Best Practices
- Structure your tables using `<Table>` as the root, pass the `head` prop for headers, and then populate with `<TableRow>` and `<TableItem>` components.
- Use the `sticky` prop on `<Table>` for sticky headers on long tables, especially on larger screens.
- Content inside `<TableItem>` can be simple text or complex HTML/Vue components.
- For empty states, you can add a `<TableRow>` with a single `<TableItem>` spanning all columns (`colspan` attribute) as shown in the example.
- Customize cell content or styling by adding classes directly to `<TableItem>` or wrapping content within it.
