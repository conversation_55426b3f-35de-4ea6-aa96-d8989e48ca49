---
description: Guide for the <TabItem> component (components/tab/TabItem.vue), representing an individual clickable tab. Covers its 'active' prop, click event, and styling for active/inactive states.
globs:
alwaysApply: false
---
# Component Guide: TabItem

**File Path**: `components/tab/TabItem.vue`
(Likely used as `<TabItem>` in templates, though no current usage examples were found via grep search. Expected to be used as children of a `<Tab>` component).

## Overview
The `<TabItem>` component represents an individual clickable tab within a `<Tab>` container. It changes its appearance based on whether it's active and emits a click event.

## Template Structure (Simplified)
```html
<button
  :class="{
    'bg-white text-primary-500 font-bold': active,
    'text-paragraph hover:bg-white/80 hover:text-primary-500': !active
  }"
  class="cursor-pointer inline-flex items-center px-3 rounded text-sm transition-all duration-200 sm:w-auto py-[6px] whitespace-nowrap group"
  @click="$emit('click')"
>
  <slot /> <!-- Tab label/content -->
</button>
```

## Props

- **`active`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Determines if the tab item is visually styled as active. The parent component should manage which tab is currently active and pass this prop accordingly.

## Slots

- **Default Slot**:
    - Purpose: This is where the text or content for the tab label should be placed.
    - Example:
      ```html
      <TabItem :active="isActive" @click="handleClick">User Profile</TabItem>
      ```
      ```html
      <TabItem :active="isActive" @click="handleClick">
        <IconUser class="w-4 h-4 mr-2" />
        <span>Details</span>
      </TabItem>
      ```

## Events

- **`click`**:
    - Emitted when the tab item (button) is clicked.
    - Purpose: Allows the parent component to react to tab selection, typically by updating its active tab state and displaying the corresponding content.

## Usage Examples

*No direct usage examples were found in the codebase via grep search. It should be used within a `<Tab>` component. See the `<Tab>` component guide for a combined hypothetical usage example.*

## Styling
- **Active State**: White background (`bg-white`), primary text color (`text-primary-500`), bold font (`font-bold`).
- **Inactive State**: Paragraph text color (`text-paragraph`), slight white background on hover (`hover:bg-white/80`), primary text color on hover (`hover:text-primary-500`).
- General styling: `cursor-pointer`, `inline-flex`, `items-center`, padding (`px-3 py-[6px]`), rounded corners (`rounded`), small text (`text-sm`), transitions (`transition-all duration-200`), responsive width (`sm:w-auto`), no wrapping of text (`whitespace-nowrap`).

## Best Practices
- Use `<TabItem>` components inside a `<Tab>` container.
- The parent component should manage the `active` state for each `<TabItem>` and listen to the `click` event to switch between tabs.
- Keep the content within the default slot concise (typically just a label).
- Given no current usage found, verify if this component is actively used or if there are preferred alternatives for tabbed interfaces.
